<?php


// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */


add_action('rank_math/vars/register_extra_replacements', function () {
    // Register 'store_heading' variable
    rank_math_register_var_replacement(
        'store_heading',
        [
            'name'        => esc_html__('Store Heading', 'rank-math'),
            'description' => esc_html__('The custom store heading for the coupon store taxonomy', 'rank-math'),
            'variable'    => 'store_heading',
            'example'     => store_heading_callback(),
        ],
        'store_heading_callback'
    );

    // Register 'store_image' variable
    rank_math_register_var_replacement(
        'store_image',
        [
            'name'        => esc_html__('Store Image URL', 'rank-math'),
            'description' => esc_html__('The store image URL for the coupon store taxonomy', 'rank-math'),
            'variable'    => 'store_image',
            'example'     => store_image_callback(),
        ],
        'store_image_callback'
    );
});

/**
 * Callback function to return the custom store heading.
 */
function store_heading_callback() {
    if (is_admin()) {
        // Get the term ID from the URL in the term edit screen
        $term_id = isset($_GET['tag_ID']) ? intval($_GET['tag_ID']) : 0;

        // Check if we have a valid term ID
        if (!$term_id) {
            return 'No valid term ID found.';
        }

        // Get the term object by ID
        $term = get_term($term_id, 'coupon_store');

        // Check if we have a valid term object
        if (!$term || is_wp_error($term)) {
            return 'No term object found.';
        }

        // Retrieve the store heading meta
        $store_heading = get_term_meta($term_id, '_ags_store_heading', true);

        if (!$store_heading) {
            return 'No store heading found in term meta.';
        }

        return $store_heading;
    }

    return '';
}

/**
 * Callback function to return the store image URL.
 */
function store_image_callback() {
    if (is_admin()) {
        // Get the term ID from the URL in the term edit screen
        $term_id = isset($_GET['tag_ID']) ? intval($_GET['tag_ID']) : 0;

        // Check if we have a valid term ID
        if (!$term_id) {
            return 'No valid term ID found.';
        }

        // Get the term object by ID
        $term = get_term($term_id, 'coupon_store');

        // Check if we have a valid term object
        if (!$term || is_wp_error($term)) {
            return 'No term object found.';
        }

        // Retrieve the store image meta
        $store_image = get_term_meta($term_id, '_ags_store_image', true);

        if (!$store_image) {
            return 'No store image found in term meta.';
        }

        return $store_image;
    }

    return '';
}