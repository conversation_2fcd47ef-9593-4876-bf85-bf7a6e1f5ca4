<?php
/**
 * Template Name: Contact Page - Enhanced
 *
 * Modern, creative contact page with premium UI/UX
 * Features: Glass morphism, brand colors, enhanced form validation, responsive design
 *
 * @package HalaCoupon
 * @version 2.0.0 - Enhanced Experience
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

// Get current language direction for RTL/LTR support
$is_rtl = is_rtl();
$text_direction = $is_rtl ? 'rtl' : 'ltr';

// Get contact options with enhanced error handling
$contact_email = halacoupon_get_option('contact_form_email', '');
$phone = halacoupon_get_option('contact_phone_number', '');
$contact_address = halacoupon_get_option('contact_address', '');
$social_facebook = halacoupon_get_option('social_facebook', '');
$social_twitter = halacoupon_get_option('social_twitter', '');
$social_instagram = halacoupon_get_option('social_instagram', '');
$social_linkedin = halacoupon_get_option('social_linkedin', '');

get_header();

?>

<!-- Enhanced Contact Page with Premium Design -->
<div id="content-wrap" class="page-contact bg-background overflow-hidden" dir="<?php echo esc_attr($text_direction); ?>">

	<!-- Enhanced Hero Section -->
	<section class="relative py-8 sm:py-12 lg:py-16 overflow-hidden">
		<!-- Background Elements -->
		<div class="absolute inset-0 pointer-events-none">
			<div class="absolute inset-0 bg-gradient-to-br from-background via-white to-accent/5"></div>
			<div class="absolute top-10 <?php echo $is_rtl ? 'left-10' : 'right-10'; ?> w-32 h-32 lg:w-48 lg:h-48 bg-gradient-to-br from-accent/20 via-primary/15 to-transparent rounded-full mix-blend-multiply filter blur-2xl opacity-60 animate-float"></div>
			<div class="absolute bottom-10 <?php echo $is_rtl ? 'right-10' : 'left-10'; ?> w-24 h-24 lg:w-40 lg:h-40 bg-gradient-to-tr from-primary/15 via-secondary/10 to-transparent rounded-full mix-blend-multiply filter blur-xl opacity-40 animate-float" style="animation-delay: 2s;"></div>
		</div>

		<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-10">
			<!-- Premium Header -->
			<div class="text-center mb-12">
				<div class="inline-flex items-center gap-2 bg-primary/10 backdrop-blur-sm px-4 py-2 rounded-full border border-primary/20 mb-6">
					<svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
					</svg>
					<span class="text-sm font-medium text-primary"><?php esc_html_e('Get in Touch', 'halacoupon'); ?></span>
				</div>
				<h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold text-text mb-4"><?php echo get_the_title(); ?></h1>
				<p class="text-lg text-gray-600 max-w-2xl mx-auto"><?php esc_html_e('We\'re here to help! Get in touch with us for any questions, suggestions, or support you need.', 'halacoupon'); ?></p>
			</div>
		</div>
	</section>

	<div id="primary" class="content-area">
		<main id="main" class="site-main" role="main">

			<!-- Enhanced Contact Section -->
			<section class="py-16 bg-white/50 backdrop-blur-sm">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">

						<!-- Enhanced Contact Form -->
						<div class="contact_us_form">
							<div class="bg-white/80 backdrop-blur-xl rounded-2xl border border-white/20 shadow-float p-8">

								<!-- Contact Info Cards -->
								<div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
									<?php if ($contact_email) : ?>
									<div class="bg-gradient-to-br from-primary/5 to-secondary/5 rounded-xl p-4 border border-primary/10">
										<div class="flex items-center gap-3">
											<div class="w-10 h-10 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-lg flex items-center justify-center">
												<svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
												</svg>
											</div>
											<div>
												<p class="text-sm text-gray-600 font-medium"><?php esc_html_e('Email', 'halacoupon'); ?></p>
												<a href="mailto:<?php echo esc_attr($contact_email); ?>" class="text-primary hover:text-secondary transition-colors duration-300 font-medium">
													<?php echo esc_html($contact_email); ?>
												</a>
											</div>
										</div>
									</div>
									<?php endif; ?>

									<?php if ($phone) : ?>
									<div class="bg-gradient-to-br from-green-400/5 to-emerald-500/5 rounded-xl p-4 border border-green-400/10">
										<div class="flex items-center gap-3">
											<div class="w-10 h-10 bg-gradient-to-br from-green-400/20 to-emerald-500/20 rounded-lg flex items-center justify-center">
												<svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
												</svg>
											</div>
											<div>
												<p class="text-sm text-gray-600 font-medium"><?php esc_html_e('Phone', 'halacoupon'); ?></p>
												<a href="tel:<?php echo esc_attr($phone); ?>" class="text-green-600 hover:text-green-700 transition-colors duration-300 font-medium">
													<?php echo esc_html($phone); ?>
												</a>
											</div>
										</div>
									</div>
									<?php endif; ?>
								</div>

								<!-- Form Description -->
								<div class="mb-8">
									<h3 class="text-xl font-bold text-text mb-3"><?php esc_html_e('Send us a Message', 'halacoupon'); ?></h3>
									<p class="text-gray-600"><?php esc_html_e('Contact us through this form and we will reply as soon as possible', 'halacoupon'); ?></p>
								</div>

								<!-- Enhanced Contact Form -->
								<div class="contact-form-wrapper">
									<?php
									if (function_exists('halacoupon_contact_form')) {
										halacoupon_contact_form();
									} else {
										echo '<p class="text-gray-500">' . esc_html__('Contact form is not available at the moment.', 'halacoupon') . '</p>';
									}
									?>
								</div>
							</div>
						</div>

						<!-- Enhanced Contact Image/Info -->
						<div class="contact_us_image">
							<div class="bg-white/80 backdrop-blur-xl rounded-2xl border border-white/20 shadow-float p-8">

								<!-- Contact Image -->
								<div class="text-center mb-8">
									<div class="relative inline-block">
										<img src="<?php echo get_template_directory_uri(); ?>/assets/images/contact_us.png"
											class="w-full max-w-md mx-auto rounded-xl shadow-lg"
											alt="<?php echo esc_attr(get_the_title()); ?>">
										<!-- Decorative elements -->
										<div class="absolute -top-4 -<?php echo $is_rtl ? 'left' : 'right'; ?>-4 w-8 h-8 bg-primary/20 rounded-full animate-float"></div>
										<div class="absolute -bottom-2 -<?php echo $is_rtl ? 'right' : 'left'; ?>-2 w-6 h-6 bg-secondary/20 rounded-full animate-float" style="animation-delay: 1s;"></div>
									</div>
								</div>

								<!-- Additional Contact Info -->
								<div class="space-y-6">
									<div class="text-center">
										<h3 class="text-xl font-bold text-text mb-4"><?php esc_html_e('Why Contact Us?', 'halacoupon'); ?></h3>
									</div>

									<!-- Features List -->
									<div class="space-y-4">
										<div class="flex items-center gap-3">
											<div class="w-8 h-8 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-lg flex items-center justify-center flex-shrink-0">
												<svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
												</svg>
											</div>
											<span class="text-gray-700"><?php esc_html_e('Quick response within 24 hours', 'halacoupon'); ?></span>
										</div>

										<div class="flex items-center gap-3">
											<div class="w-8 h-8 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-lg flex items-center justify-center flex-shrink-0">
												<svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
												</svg>
											</div>
											<span class="text-gray-700"><?php esc_html_e('Professional customer support', 'halacoupon'); ?></span>
										</div>

										<div class="flex items-center gap-3">
											<div class="w-8 h-8 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-lg flex items-center justify-center flex-shrink-0">
												<svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
												</svg>
											</div>
											<span class="text-gray-700"><?php esc_html_e('Help with deals and offers', 'halacoupon'); ?></span>
										</div>

										<div class="flex items-center gap-3">
											<div class="w-8 h-8 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-lg flex items-center justify-center flex-shrink-0">
												<svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
												</svg>
											</div>
											<span class="text-gray-700"><?php esc_html_e('Technical support and guidance', 'halacoupon'); ?></span>
										</div>
									</div>

									<!-- Social Media Links -->
									<?php if ($social_facebook || $social_twitter || $social_instagram || $social_linkedin) : ?>
									<div class="pt-6 border-t border-gray-200">
										<h4 class="text-lg font-semibold text-text mb-4 text-center"><?php esc_html_e('Follow Us', 'halacoupon'); ?></h4>
										<div class="flex justify-center gap-4">
											<?php if ($social_facebook) : ?>
											<a href="<?php echo esc_url($social_facebook); ?>" target="_blank" rel="noopener noreferrer"
											   class="w-10 h-10 bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-lg flex items-center justify-center hover:from-blue-500 hover:to-blue-600 hover:text-white transition-all duration-300">
												<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
													<path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
												</svg>
											</a>
											<?php endif; ?>

											<?php if ($social_twitter) : ?>
											<a href="<?php echo esc_url($social_twitter); ?>" target="_blank" rel="noopener noreferrer"
											   class="w-10 h-10 bg-gradient-to-br from-sky-500/20 to-sky-600/20 rounded-lg flex items-center justify-center hover:from-sky-500 hover:to-sky-600 hover:text-white transition-all duration-300">
												<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
													<path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
												</svg>
											</a>
											<?php endif; ?>

											<?php if ($social_instagram) : ?>
											<a href="<?php echo esc_url($social_instagram); ?>" target="_blank" rel="noopener noreferrer"
											   class="w-10 h-10 bg-gradient-to-br from-pink-500/20 to-purple-600/20 rounded-lg flex items-center justify-center hover:from-pink-500 hover:to-purple-600 hover:text-white transition-all duration-300">
												<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
													<path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.247 7.053 7.757 8.35 7.757s2.448.49 3.323 1.297c.897.897 1.387 2.048 1.387 3.345s-.49 2.448-1.387 3.345c-.875.807-2.026 1.297-3.323 1.297zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.897-1.387-2.048-1.387-3.345s.49-2.448 1.387-3.345c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.897.897 1.387 2.048 1.387 3.345s-.49 2.448-1.387 3.345c-.875.807-2.026 1.297-3.323 1.297z"/>
												</svg>
											</a>
											<?php endif; ?>

											<?php if ($social_linkedin) : ?>
											<a href="<?php echo esc_url($social_linkedin); ?>" target="_blank" rel="noopener noreferrer"
											   class="w-10 h-10 bg-gradient-to-br from-blue-700/20 to-blue-800/20 rounded-lg flex items-center justify-center hover:from-blue-700 hover:to-blue-800 hover:text-white transition-all duration-300">
												<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
													<path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
												</svg>
											</a>
											<?php endif; ?>
										</div>
									</div>
									<?php endif; ?>
								</div>
							</div>
						</div>
					</div>
				</div>
			</section>

		</main><!-- #main -->
	</div><!-- #primary -->

	<?php
	// Clean up
	wp_reset_postdata();
	?>
</div> <!-- /#content-wrap -->

<!-- Enhanced JavaScript for Contact Form -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced form validation
    const contactForm = document.querySelector('.contact-form-wrapper form');
    if (contactForm) {
        const inputs = contactForm.querySelectorAll('input, textarea');

        inputs.forEach(input => {
            // Add enhanced styling
            input.classList.add('w-full', 'px-4', 'py-3', 'border', 'border-gray-300', 'rounded-xl', 'focus:ring-2', 'focus:ring-primary/20', 'focus:border-primary', 'transition-all', 'duration-300', 'bg-white/80', 'backdrop-blur-sm');

            // Add validation on blur
            input.addEventListener('blur', function() {
                validateField(this);
            });

            // Add real-time validation
            input.addEventListener('input', function() {
                if (this.classList.contains('error')) {
                    validateField(this);
                }
            });
        });

        // Enhanced submit button styling
        const submitButton = contactForm.querySelector('input[type="submit"], button[type="submit"]');
        if (submitButton) {
            submitButton.classList.add('w-full', 'px-6', 'py-3', 'bg-gradient-to-r', 'from-primary', 'to-secondary', 'text-white', 'rounded-xl', 'font-medium', 'hover:shadow-glow', 'transition-all', 'duration-300', 'transform', 'hover:scale-105');
        }

        // Form submission handling
        contactForm.addEventListener('submit', function(e) {
            let isValid = true;
            inputs.forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });

            if (!isValid) {
                e.preventDefault();
                showNotification('Please fill in all required fields correctly.', 'error');
            } else {
                showNotification('Sending your message...', 'info');
            }
        });
    }

    // Field validation function
    function validateField(field) {
        const value = field.value.trim();
        const fieldType = field.type;
        const isRequired = field.hasAttribute('required');

        // Remove existing error styling
        field.classList.remove('error', 'border-red-500', 'bg-red-50');

        let isValid = true;

        if (isRequired && !value) {
            isValid = false;
        } else if (fieldType === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
            }
        }

        if (!isValid) {
            field.classList.add('error', 'border-red-500', 'bg-red-50');
        } else {
            field.classList.add('border-green-500', 'bg-green-50');
        }

        return isValid;
    }

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-6 py-4 rounded-xl shadow-lg transform translate-x-full transition-transform duration-300 ${getNotificationClasses(type)}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 5000);
    }

    function getNotificationClasses(type) {
        switch (type) {
            case 'success':
                return 'bg-green-500 text-white';
            case 'error':
                return 'bg-red-500 text-white';
            case 'warning':
                return 'bg-yellow-500 text-white';
            default:
                return 'bg-blue-500 text-white';
        }
    }

    // Enhanced hover effects for contact cards
    const contactCards = document.querySelectorAll('.contact_us_form > div > div, .contact_us_image > div');
    contactCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.01)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Smooth scroll for contact links
    const contactLinks = document.querySelectorAll('a[href^="mailto:"], a[href^="tel:"]');
    contactLinks.forEach(link => {
        link.addEventListener('click', function() {
            showNotification('Opening ' + (this.href.startsWith('mailto:') ? 'email client' : 'phone app') + '...', 'info');
        });
    });
});
</script>

<?php get_footer(); ?>