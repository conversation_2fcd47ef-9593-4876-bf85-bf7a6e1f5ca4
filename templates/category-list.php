<?php
/**
 * Template Name: Categories List - Enhanced
 *
 * Modern, creative categories listing page with premium UI/UX
 * Features: Glass morphism, brand colors, enhanced filtering, responsive design
 *
 * @package HalaCoupon
 * @version 2.0.0 - Enhanced Experience
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

get_header();
the_post();

// Get current language direction for RTL/LTR support
$is_rtl = is_rtl();
$text_direction = $is_rtl ? 'rtl' : 'ltr';

?>

<!-- Enhanced Categories List Page with Premium Design -->
<div id="content-wrap" class="page-categories-list bg-background overflow-hidden" dir="<?php echo esc_attr($text_direction); ?>">

	<div id="primary" class="content-area">
		<main id="main" class="site-main" role="main">

			<!-- Enhanced Hero Section with Dark Glass Background & Fixed Title -->
			<section class="relative py-16 sm:py-20 lg:py-24 overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800">
				<!-- Enhanced floating elements with better positioning -->
				<div class="absolute inset-0 pointer-events-none z-0">
					<div class="absolute top-20 left-20 w-48 h-48 bg-gradient-secondary rounded-full filter blur-3xl opacity-20 animate-float"></div>
					<div class="absolute bottom-20 right-20 w-40 h-40 bg-gradient-primary rounded-full filter blur-2xl opacity-15 animate-float-slow"></div>
					<div class="absolute top-1/3 right-1/4 w-32 h-32 bg-gradient-accent rounded-full filter blur-xl opacity-10 animate-float-fast"></div>
					<!-- Additional glass blur elements -->
					<div class="absolute inset-0 bg-gradient-to-br from-black/50 via-transparent to-black/30 backdrop-blur-sm"></div>
				</div>

				<div class="relative max-w-5xl mx-auto px-6 sm:px-8 z-10">
					<!-- Enhanced Brand Badge with better contrast -->
					<div class="flex justify-center mb-8">
						<span class="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-secondary to-primary shadow-glow text-white font-bold text-sm backdrop-blur-xl border-2 border-white/30 hover:border-white/50 transition-all duration-300 transform hover:scale-105">
							<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
							</svg>
							<?php esc_html_e('Premium Verified Categories', 'halacoupon'); ?>
						</span>
					</div>

					<!-- Fixed H1 Title with proper gradient text -->
					<h1 class="text-center text-5xl sm:text-6xl lg:text-7xl font-black font-rubik mb-8 leading-tight drop-shadow-2xl">
						<span class="bg-gradient-to-r from-white via-yellow-200 to-white bg-clip-text text-transparent">
							<?php echo esc_html(get_the_title()); ?>
						</span>
					</h1>

					<!-- Enhanced Statistics with better contrast and layout -->
					<div class="grid grid-cols-1 sm:grid-cols-2 gap-8 mb-8 max-w-2xl mx-auto">
						<!-- Enhanced Total Categories Card -->
						<div class="group bg-white/10 backdrop-blur-2xl rounded-3xl shadow-float-lg border-2 border-white/20 p-8 hover:bg-white/15 hover:border-white/30 transition-all duration-500 transform hover:scale-105 hover:shadow-glow">
							<div class="flex items-center gap-6">
								<div class="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-primary to-secondary rounded-2xl shadow-glow group-hover:shadow-glow-secondary transition-all duration-300">
									<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
									</svg>
								</div>
								<div>
									<div class="text-4xl font-black text-white mb-1">
										<?php
										if (function_exists('halacoupon_get_total_published_categories')) {
											$total_categories = halacoupon_get_total_published_categories();
											echo esc_html($total_categories);
										} else {
											echo '0';
										}
										?>
									</div>
									<div class="text-sm font-semibold text-white/90 uppercase tracking-wide"><?php esc_html_e('Verified Categories', 'halacoupon'); ?></div>
								</div>
							</div>
						</div>
						
						<!-- Enhanced Total Coupons Card -->
						<div class="group bg-white/10 backdrop-blur-2xl rounded-3xl shadow-float-lg border-2 border-white/20 p-8 hover:bg-white/15 hover:border-white/30 transition-all duration-500 transform hover:scale-105 hover:shadow-glow-secondary">
							<div class="flex items-center gap-6">
								<div class="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-secondary to-accent rounded-2xl shadow-glow-secondary group-hover:shadow-glow transition-all duration-300">
									<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
									</svg>
								</div>
								<div>
									<div class="text-4xl font-black text-white mb-1">
										<?php
										if (function_exists('halacoupon_get_total_published_coupons')) {
											$total_coupons = halacoupon_get_total_published_coupons();
											echo esc_html($total_coupons);
										} else {
											echo '0';
										}
										?>
									</div>
									<div class="text-sm font-semibold text-white/90 uppercase tracking-wide"><?php esc_html_e('Active Deals', 'halacoupon'); ?></div>
								</div>
							</div>
						</div>
					</div>

					<!-- Enhanced Date Badge with better styling -->
					<div class="flex justify-center">
						<span class="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-black/60 backdrop-blur-xl text-white font-medium text-sm border-2 border-white/20 shadow-soft hover:bg-black/70 hover:border-white/30 transition-all duration-300">
							<div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
							<svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
							<?php esc_html_e('Last Updated:', 'halacoupon'); ?> <?php echo get_current_month_info('current_day_full'); ?>
						</span>
					</div>
				</div>
			</section>

			<!-- Enhanced Featured Categories Section -->
			<section class="py-12 bg-white/50 backdrop-blur-sm">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center mb-8">
						<div class="inline-flex items-center gap-2 bg-secondary/10 backdrop-blur-sm px-4 py-2 rounded-full border border-secondary/20 mb-6">
							<svg class="w-5 h-5 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
							</svg>
							<span class="text-sm font-medium text-secondary"><?php esc_html_e('Featured', 'halacoupon'); ?></span>
						</div>
						<h2 class="text-2xl sm:text-3xl font-bold text-text mb-4"><?php esc_html_e('Featured Categories', 'halacoupon'); ?></h2>
						<p class="text-lg text-gray-600"><?php esc_html_e('Discover our most popular categories with the best deals', 'halacoupon'); ?></p>
					</div>

					<div class="featured-categories-container">
						<?php
						if (function_exists('halacoupon_get_featured_categories_slider')) {
							echo halacoupon_get_featured_categories_slider();
						} else {
							echo '<div class="text-center py-8"><p class="text-gray-500">' . esc_html__('Featured categories will be displayed here.', 'halacoupon') . '</p></div>';
						}
						?>
					</div>
				</div>
			</section>
			<!-- Enhanced All Categories Section -->
			<section class="py-16 bg-gradient-to-br from-white via-background/50 to-white">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

					<!-- Enhanced Section Header -->
					<div class="text-center mb-12">
						<div class="inline-flex items-center gap-2 bg-primary/10 backdrop-blur-sm px-4 py-2 rounded-full border border-primary/20 mb-6">
							<svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
							</svg>
							<span class="text-sm font-medium text-primary"><?php esc_html_e('Browse All', 'halacoupon'); ?></span>
						</div>
						<h2 class="text-3xl sm:text-4xl font-bold text-text mb-4"><?php esc_html_e('All Categories', 'halacoupon'); ?></h2>
						<p class="text-lg text-gray-600 max-w-2xl mx-auto"><?php esc_html_e('Browse through all available categories to find the perfect deals and offers for every occasion', 'halacoupon'); ?></p>
					</div>

					<?php
					// Enhanced error handling and validation
					if ( taxonomy_exists( 'coupon_category' ) ) {
						$args = array(
							'type'                     => 'post',
							'child_of'                 => 0,
							'orderby'                  => 'name',
							'order'                    => 'ASC',
							'hide_empty'               => 0,
							'hierarchical'             => 1,
							'taxonomy'                 => 'coupon_category',
							'pad_counts'               => false
						);

						$categories = get_categories( $args );

						// Enhanced error handling
						if ( is_wp_error( $categories ) ) {
							$categories = array();
						}

						$_categories = array();
						$count_cat = 1;

						foreach ( $categories as $k => $c ) {
							$count_cat++;
							if ( $c->parent == 0 ) {
								$_categories[ $c->term_id ] = array();
								$_categories[ $c->term_id ]['data'] = $c;
								unset( $categories[ $k ] );
								if ( ! empty ( $categories ) ) {
									foreach ( $categories as $ck => $cc) {
										if ( $cc->parent == $c->term_id ) {
											if (!isset($_categories[ $c->term_id ]['child'])) {
												$_categories[ $c->term_id ]['child'] = array();
											}
											$_categories[ $c->term_id ]['child'][] = $cc;
											unset( $categories[$ck] );
										}
									}
								}
							}
						}
					?>

					<!-- Enhanced Categories Grid -->
					<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
						<?php
						foreach ( $_categories as $cat_id => $c ) {
							$cate_id = $cat_id;
							$store_image = get_term_meta( $cate_id, '_ags_icon', true );
							$store_link = get_term_link($cate_id, 'coupon_category');
							$category_name = $c['data']->name;
							$coupon_counts = halacoupon_get_coupon_counts_by_type( $cate_id , 'coupon_category');
							$total_coupons = ($coupon_counts['code'] ?? 0) + ($coupon_counts['sale'] ?? 0);
							?>
						<!-- Enhanced Category Card -->
						<div class="category-card group">
							<div class="relative bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-float overflow-hidden transition-all duration-300 hover:shadow-glow hover:bg-white/90 hover:-translate-y-2">
								<!-- Gradient overlay -->
								<div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

								<a href="<?php echo esc_url($store_link); ?>"
								   class="relative block p-6 text-center"
								   aria-label="<?php echo esc_attr($category_name); ?>">

									<!-- Enhanced Category Icon -->
									<div class="relative w-20 h-20 mx-auto mb-4">
										<div class="w-full h-full bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 border border-primary/20">
											<?php if ( $store_image ) : ?>
											<i class="category <?php echo esc_attr( $store_image ); ?> text-3xl text-primary"></i>
											<?php else : ?>
											<svg class="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
											</svg>
											<?php endif; ?>
										</div>
										<!-- Verified badge -->
										<div class="absolute -top-1 -<?php echo $is_rtl ? 'left' : 'right'; ?>-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
											<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
											</svg>
										</div>
									</div>

									<!-- Enhanced Category Name -->
									<h3 class="text-lg font-bold text-text mb-3 group-hover:text-primary transition-colors duration-300">
										<?php echo esc_html($category_name); ?>
									</h3>

									<!-- Enhanced Coupon Count -->
									<div class="flex items-center justify-center gap-2 text-sm text-gray-600">
										<svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
										</svg>
										<span class="font-medium"><?php echo esc_html($total_coupons); ?></span>
										<span><?php esc_html_e('offers', 'halacoupon'); ?></span>
									</div>

									<!-- Hover effect arrow -->
									<div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
										<svg class="w-5 h-5 mx-auto text-primary <?php echo $is_rtl ? 'rotate-180' : ''; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
										</svg>
									</div>
								</a>
							</div>
						</div>
						<?php } ?>
					</div>
					<?php } else { ?>
					<!-- Enhanced No Categories Found Section -->
					<div class="bg-white/80 backdrop-blur-xl rounded-3xl border border-white/20 shadow-float p-12 text-center">
						<div class="max-w-md mx-auto">
							<!-- Animated Icon -->
							<div class="relative mb-8">
								<div class="w-24 h-24 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center animate-pulse">
									<svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
									</svg>
								</div>
								<!-- Floating elements -->
								<div class="absolute top-0 left-0 w-4 h-4 bg-secondary/20 rounded-full animate-float"></div>
								<div class="absolute top-4 right-2 w-3 h-3 bg-primary/20 rounded-full animate-float" style="animation-delay: 1s;"></div>
								<div class="absolute bottom-2 left-4 w-2 h-2 bg-accent/20 rounded-full animate-float" style="animation-delay: 2s;"></div>
							</div>

							<h3 class="text-2xl font-bold text-text mb-4"><?php esc_html_e( 'No categories found', 'halacoupon' ); ?></h3>
							<p class="text-gray-600 mb-8 leading-relaxed"><?php esc_html_e( 'We couldn\'t find any categories at the moment. Our team is constantly adding new categories and deals. Please check back later or contact us if you\'re looking for a specific category.', 'halacoupon' ); ?></p>

							<!-- Action buttons -->
							<div class="flex flex-col sm:flex-row gap-4 justify-center">
								<a href="<?php echo esc_url(home_url('/')); ?>"
								   class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-xl font-medium hover:shadow-glow transition-all duration-300">
									<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
									</svg>
									<?php esc_html_e('Go to Homepage', 'halacoupon'); ?>
								</a>

								<?php if (function_exists('halacoupon_get_option') && halacoupon_get_option('contact_form_email')) : ?>
								<a href="<?php echo esc_url(get_permalink(get_page_by_path('contact'))); ?>"
								   class="inline-flex items-center gap-2 px-6 py-3 bg-white/80 backdrop-blur-sm text-text border border-gray-200 rounded-xl font-medium hover:bg-white hover:shadow-glow transition-all duration-300">
									<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
									</svg>
									<?php esc_html_e('Contact Us', 'halacoupon'); ?>
								</a>
								<?php endif; ?>
							</div>
						</div>
					</div>
					<?php } ?>
				</div>
			</section>

		</main><!-- #main -->
	</div><!-- #primary -->

	<?php
	// Clean up
	wp_reset_postdata();
	?>

</div> <!-- /#content-wrap -->

<!-- Enhanced JavaScript for Category Interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const categoryCards = document.querySelectorAll('.category-card');

    categoryCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Add loading animation for category images
    const categoryIcons = document.querySelectorAll('.category i');
    categoryIcons.forEach(icon => {
        icon.addEventListener('load', function() {
            this.style.opacity = '1';
        });
    });
});
</script>

<?php get_footer(); ?>