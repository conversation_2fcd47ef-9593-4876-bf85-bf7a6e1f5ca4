<?php
/**
 * Template Name: All Coupons - Enhanced
 *
 * Modern, performance-optimized all coupons listing page
 * Features: Advanced filtering, pagination, responsive design, caching
 *
 * @package HalaCoupon
 * @version 1.0.0 - All Coupons Experience
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

get_header();
the_post();

// Get current language direction for RTL/LTR support
$is_rtl = is_rtl();
$text_direction = $is_rtl ? 'rtl' : 'ltr';

// Performance optimization: Check for cached data first
$cache_key = 'halacoupon_all_coupons_stats_v1';
$cached_stats = get_transient($cache_key);

if (false === $cached_stats) {
    // Get total counts for statistics
    $total_coupons_query = new WP_Query(array(
        'post_type' => 'coupon',
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'fields' => 'ids',
        'no_found_rows' => false,
        'update_post_meta_cache' => false,
        'update_post_term_cache' => false,
    ));
    
    $total_coupons = $total_coupons_query->found_posts;
    wp_reset_postdata();
    
    // Get total stores count
    $total_stores = wp_count_terms('coupon_store', array('hide_empty' => false));
    if (is_wp_error($total_stores)) {
        $total_stores = 0;
    }
    
    // Cache stats for 30 minutes
    $cached_stats = array(
        'total_coupons' => $total_coupons,
        'total_stores' => $total_stores
    );
    set_transient($cache_key, $cached_stats, 30 * MINUTE_IN_SECONDS);
} else {
    $total_coupons = $cached_stats['total_coupons'];
    $total_stores = $cached_stats['total_stores'];
}

// Pagination setup
$paged = get_query_var('paged') ? get_query_var('paged') : 1;
$coupons_per_page = 20;

// Query arguments for coupons
$coupon_args = array(
    'post_type' => 'coupon',
    'post_status' => 'publish',
    'posts_per_page' => $coupons_per_page,
    'paged' => $paged,
    'orderby' => 'date',
    'order' => 'DESC',
    'meta_query' => array(
        array(
            'key' => '_ags_coupon_expire',
            'value' => current_time('Y-m-d'),
            'compare' => '>=',
            'type' => 'DATE'
        )
    )
);

// Apply filters if any
if (isset($_GET['store']) && !empty($_GET['store'])) {
    $coupon_args['tax_query'] = array(
        array(
            'taxonomy' => 'coupon_store',
            'field' => 'slug',
            'terms' => sanitize_text_field($_GET['store'])
        )
    );
}

if (isset($_GET['type']) && in_array($_GET['type'], array('code', 'sale'))) {
    $coupon_args['meta_query'][] = array(
        'key' => '_ags_coupon_type',
        'value' => sanitize_text_field($_GET['type']),
        'compare' => '='
    );
}

// Execute the query
$coupons_query = new WP_Query($coupon_args);

?>

<div id="content-wrap" class="page-all-coupons bg-background overflow-hidden" dir="<?php echo esc_attr($text_direction); ?>">

    <div id="primary" class="content-area">
        <main id="main" class="site-main" role="main">
            
            <!-- Enhanced Hero Section -->
            <section class="relative py-16 sm:py-20 lg:py-24 overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800">
                <!-- Floating elements -->
                <div class="absolute hidden lg:block inset-0 pointer-events-none z-0">
                    <div class="absolute top-20 left-20 w-48 h-48 bg-gradient-secondary rounded-full filter blur-3xl opacity-20 animate-float"></div>
                    <div class="absolute bottom-20 right-20 w-40 h-40 bg-gradient-primary rounded-full filter blur-2xl opacity-15 animate-float-slow"></div>
                    <div class="absolute top-1/3 right-1/4 w-32 h-32 bg-gradient-accent rounded-full filter blur-xl opacity-10 animate-float-fast"></div>
                    <div class="absolute inset-0 bg-gradient-to-br from-black/50 via-transparent to-black/30 backdrop-blur-sm"></div>
                </div>

                <div class="relative max-w-5xl mx-auto px-6 sm:px-8 z-10">
                    <!-- Brand Badge -->
                    <div class="flex justify-center mb-8">
                        <span class="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-secondary to-primary shadow-glow text-white font-bold text-sm backdrop-blur-xl border-2 border-white/30 hover:border-white/50 transition-all duration-300 transform hover:scale-105">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                            </svg>
                            <?php esc_html_e('All Premium Coupons', 'halacoupon'); ?>
                        </span>
                    </div>

                    <!-- Page Title -->
                    <h1 class="text-center text-5xl sm:text-6xl lg:text-7xl font-black font-rubik mb-8 leading-tight drop-shadow-2xl">
                        <span class="bg-gradient-to-r from-white via-yellow-200 to-white bg-clip-text text-transparent">
                            <?php echo esc_html(get_the_title()); ?>
                        </span>
                    </h1>

                    <!-- Statistics -->
                    <div class="grid grid-cols-2 gap-6 max-w-2xl mx-auto mb-12">
                        <div class="text-center p-6 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-glow">
                            <div class="text-3xl sm:text-4xl font-black text-white mb-2"><?php echo number_format($total_coupons); ?></div>
                            <div class="text-white/80 font-medium"><?php esc_html_e('Total Coupons', 'halacoupon'); ?></div>
                        </div>
                        <div class="text-center p-6 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-glow">
                            <div class="text-3xl sm:text-4xl font-black text-white mb-2"><?php echo number_format($total_stores); ?></div>
                            <div class="text-white/80 font-medium"><?php esc_html_e('Partner Stores', 'halacoupon'); ?></div>
                        </div>
                    </div>

                    <!-- Page Description -->
                    <?php if (get_the_content()) : ?>
                        <div class="text-center text-xl text-white/90 max-w-4xl mx-auto leading-relaxed">
                            <?php the_content(); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </section>

            <!-- Coupons Grid Section -->
            <section class="py-16 bg-gradient-to-br from-background via-white to-background/50 relative">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(235,191,67,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
                </div>

                <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-10">
                    
                    <!-- Filter Section -->
                    <div class="mb-12">
                        <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-soft border border-white/50">
                            <h3 class="text-xl font-bold text-gray-800 mb-4"><?php esc_html_e('Filter Coupons', 'halacoupon'); ?></h3>
                            <form method="get" class="flex flex-wrap gap-4">
                                <select name="store" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value=""><?php esc_html_e('All Stores', 'halacoupon'); ?></option>
                                    <?php
                                    $stores = get_terms(array(
                                        'taxonomy' => 'coupon_store',
                                        'hide_empty' => true,
                                        'orderby' => 'name',
                                        'order' => 'ASC'
                                    ));
                                    foreach ($stores as $store) {
                                        $selected = (isset($_GET['store']) && $_GET['store'] === $store->slug) ? 'selected' : '';
                                        echo '<option value="' . esc_attr($store->slug) . '" ' . $selected . '>' . esc_html($store->name) . '</option>';
                                    }
                                    ?>
                                </select>
                                
                                <select name="type" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value=""><?php esc_html_e('All Types', 'halacoupon'); ?></option>
                                    <option value="code" <?php selected(isset($_GET['type']) ? $_GET['type'] : '', 'code'); ?>><?php esc_html_e('Coupon Codes', 'halacoupon'); ?></option>
                                    <option value="sale" <?php selected(isset($_GET['type']) ? $_GET['type'] : '', 'sale'); ?>><?php esc_html_e('Deals & Sales', 'halacoupon'); ?></option>
                                </select>
                                
                                <button type="submit" class="px-6 py-2 bg-gradient-to-r from-primary to-secondary text-white rounded-lg font-medium hover:shadow-glow transition-all duration-300">
                                    <?php esc_html_e('Filter', 'halacoupon'); ?>
                                </button>
                                
                                <?php if (isset($_GET['store']) || isset($_GET['type'])) : ?>
                                    <a href="<?php echo esc_url(get_permalink()); ?>" class="px-6 py-2 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600 transition-colors duration-300">
                                        <?php esc_html_e('Clear', 'halacoupon'); ?>
                                    </a>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>

                    <!-- Coupons Grid -->
                    <?php if ($coupons_query->have_posts()) : ?>
                        <?php halacoupon_render_coupons_grid($coupons_query, $coupons_per_page); ?>
                        
                        <!-- Pagination -->
                        <div class="mt-16">
                            <?php
                            $pagination_args = array(
                                'total' => $coupons_query->max_num_pages,
                                'current' => $paged,
                                'prev_text' => '&larr; ' . esc_html__('Previous', 'halacoupon'),
                                'next_text' => esc_html__('Next', 'halacoupon') . ' &rarr;',
                                'type' => 'array'
                            );
                            
                            $pagination_links = paginate_links($pagination_args);
                            
                            if ($pagination_links) {
                                echo '<nav class="pagination-wrapper" aria-label="' . esc_attr__('Coupons pagination', 'halacoupon') . '">';
                                echo '<ul class="flex justify-center items-center space-x-2">';
                                foreach ($pagination_links as $link) {
                                    echo '<li class="pagination-item">' . $link . '</li>';
                                }
                                echo '</ul>';
                                echo '</nav>';
                            }
                            ?>
                        </div>
                    <?php else : ?>
                        <!-- No Coupons Found -->
                        <div class="text-center py-16">
                            <div class="max-w-md mx-auto">
                                <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-bold text-gray-800 mb-4"><?php esc_html_e('No Coupons Found', 'halacoupon'); ?></h3>
                                <p class="text-gray-600 mb-6"><?php esc_html_e('Sorry, no coupons match your current filters. Try adjusting your search criteria.', 'halacoupon'); ?></p>
                                <a href="<?php echo esc_url(get_permalink()); ?>" class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-xl font-medium hover:shadow-glow transition-all duration-300">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    <?php esc_html_e('View All Coupons', 'halacoupon'); ?>
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Custom Content Section -->
                    <?php
                    $custom_content = get_post_meta(get_the_ID(), '_halacoupon_all_coupons_custom_content', true);
                    if (!empty($custom_content)) :
                    ?>
                        <div class="mt-16 custom-content-section">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-soft border border-white/50">
                                <?php echo wp_kses_post($custom_content); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                </div>
            </section>

        </main>
    </div>
</div>

<?php
wp_reset_postdata();
get_footer();
?>
