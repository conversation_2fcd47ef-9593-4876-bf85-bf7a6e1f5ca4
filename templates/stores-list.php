<?php
/**
 * Template Name: Stores List - Enhanced
 *
 * Modern, creative stores listing page with premium UI/UX
 * Features: Glass morphism, brand colors, enhanced filtering, responsive design
 *
 * @package HalaCoupon
 * @version 2.0.0 - Enhanced Experience
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

get_header();
the_post();

// Get current language direction for RTL/LTR support
$is_rtl = is_rtl();
$text_direction = $is_rtl ? 'rtl' : 'ltr';

// Enhanced error handling and data validation
$stores_data = [];
$stores_by_letter = [];
$stores_by_number = [];
$total_stores = 0;
$total_coupons = 0;

?>

<div id="content-wrap" class="page-stores-list bg-background overflow-hidden" dir="<?php echo esc_attr($text_direction); ?>">

	<div id="primary" class="content-area">
		<main id="main" class="site-main" role="main">
			<?php
                // Enhanced error handling and validation
                if ( taxonomy_exists( 'coupon_store' ) ) {
                    // Optimized query arguments
                    $args = array(
                        'orderby'                => 'name',
                        'order'                  => 'ASC',
                        'hide_empty'             => false,
                        'include'                => array(),
                        'exclude'                => array(),
                        'exclude_tree'           => array(),
                        'number'                 => '',
                        'offset'                 => '',
                        'fields'                 => 'all',
                        'name'                   => '',
                        'slug'                   => '',
                        'hierarchical'           => false,
                        'search'                 => '',
                        'name__like'             => '',
                        'description__like'      => '',
                        'pad_counts'             => false,
                        'get'                    => '',
                        'child_of'               => 0,
                        'parent'                 => false,
                        'childless'              => false,
                        'cache_domain'           => 'core',
                        'update_term_meta_cache' => true,
                        'meta_query'             => ''
                    );

                    $stores = get_terms( 'coupon_store', $args );

                    // Enhanced error handling
                    if ( is_wp_error( $stores ) ) {
                        $stores = array();
                    }

                    $_stores = array();
                    $_stores_number = array();

                    // Enhanced grouping with better character handling
                    foreach ( $stores as $k => $store ) {
                        $first_char = mb_substr($store->name, 0, 1);
                        $first_char = mb_strtoupper( $first_char );

                        if( is_numeric( $first_char ) ) {
                            $_stores_number[] = $store;
                        } else {
                            if ( ! isset( $_stores[ $first_char ] ) ) {
                                $_stores[ $first_char ]  =  array();
                            }
                            $_stores[ $first_char ][] = $store;
                        }
                    }

                    // Sort alphabetically
                    ksort($_stores);
                ?>
			<!-- Enhanced Hero Section with Dark Glass Background & Fixed Title -->
			<section class="relative py-16 sm:py-20 lg:py-24 overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800">
				<!-- Enhanced floating elements with better positioning -->
				<div class="absolute hidden lg:block inset-0 pointer-events-none z-0">
					<div class="absolute top-20 left-20 w-48 h-48 bg-gradient-secondary rounded-full filter blur-3xl opacity-20 animate-float"></div>
					<div class="absolute bottom-20 right-20 w-40 h-40 bg-gradient-primary rounded-full filter blur-2xl opacity-15 animate-float-slow"></div>
					<div class="absolute top-1/3 right-1/4 w-32 h-32 bg-gradient-accent rounded-full filter blur-xl opacity-10 animate-float-fast"></div>
					<!-- Additional glass blur elements -->
					<div class="absolute inset-0 bg-gradient-to-br from-black/50 via-transparent to-black/30 backdrop-blur-sm"></div>
				</div>

				<div class="relative max-w-5xl mx-auto px-6 sm:px-8 z-10">
					<!-- Enhanced Brand Badge with better contrast -->
					<div class="flex justify-center mb-8">
						<span class="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-secondary to-primary shadow-glow text-white font-bold text-sm backdrop-blur-xl border-2 border-white/30 hover:border-white/50 transition-all duration-300 transform hover:scale-105">
							<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
							</svg>
							<?php esc_html_e('Premium Verified Stores', 'halacoupon'); ?>
						</span>
					</div>

					<!-- Fixed H1 Title with proper gradient text -->
					<h1 class="text-center text-5xl sm:text-6xl lg:text-7xl font-black font-rubik mb-8 leading-tight drop-shadow-2xl">
						<span class="bg-gradient-to-r from-white via-yellow-200 to-white bg-clip-text text-transparent">
							<?php echo esc_html(get_the_title()); ?>
						</span>
					</h1>



					<!-- Enhanced Date Badge with better styling -->
					<div class="flex justify-center">
						<span class="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-black/60 backdrop-blur-xl text-white font-medium text-sm border-2 border-white/20 shadow-soft hover:bg-black/70 hover:border-white/30 transition-all duration-300">
							<div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
							<svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
							<?php esc_html_e('Last Updated:', 'halacoupon'); ?> <?php echo get_current_month_info('current_day_full'); ?>
						</span>
					</div>
				</div>
			</section>

			<!-- Enhanced Featured Stores Section -->
			<section class="py-12 bg-white/50 backdrop-blur-sm">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center mb-8">
						<h2 class="text-2xl sm:text-3xl font-bold text-text mb-4"><?php esc_html_e('Featured Stores', 'halacoupon'); ?></h2>
						<p class="text-lg text-gray-600"><?php esc_html_e('Discover our most popular and trusted stores', 'halacoupon'); ?></p>
					</div>

					<div class="featured-stores-container">
						<?php
						if (function_exists('halacoupon_get_featured_stores_slider')) {
							echo halacoupon_get_featured_stores_slider();
						} else {
							echo '<div class="text-center py-8"><p class="text-gray-500">' . esc_html__('Featured stores will be displayed here.', 'halacoupon') . '</p></div>';
						}
						?>
					</div>
				</div>
			</section>
			<!-- Enhanced All Stores Section with Premium UI/UX -->
			<section class="py-16 bg-gradient-to-br from-background via-white to-background/50 relative">
				<!-- Background Pattern -->
				<div class="absolute inset-0 opacity-5">
					<div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(235,191,67,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
				</div>

				<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

					<!-- Enhanced Section Header -->
					<div class="text-center mb-12">
						<div class="inline-flex items-center gap-2 bg-gradient-to-r from-primary/10 to-secondary/10 backdrop-blur-sm px-6 py-3 rounded-full border border-primary/20 mb-6 shadow-soft">
							<svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
							</svg>
							<span class="text-sm font-semibold text-primary"><?php esc_html_e('Browse All Stores', 'halacoupon'); ?></span>
						</div>
						<h2 class="text-3xl sm:text-4xl lg:text-5xl font-bold text-text mb-6 leading-tight"><?php esc_html_e('All Stores', 'halacoupon'); ?></h2>
						<p class="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed"><?php esc_html_e('Discover amazing deals from our verified partner stores. Browse alphabetically or search for your favorite brands to find the best offers and discounts.', 'halacoupon'); ?></p>
					</div>

					<!-- Compact Filter Section -->
					<div class="mb-12">
						<div class="bg-white/90 backdrop-blur-xl rounded-2xl border border-white/30 shadow-float p-4 lg:p-6">

							<!-- Compact Alphabetic Filter Section -->
							<div class="text-center">
								<div class="mb-4">
									<h3 class="text-lg font-bold text-text mb-1"><?php esc_html_e('Filter by Letter', 'halacoupon'); ?></h3>
									<p class="text-xs text-gray-600"><?php esc_html_e('Click any letter to filter stores', 'halacoupon'); ?></p>
								</div>

								<div class="store-filter-section">
									<div class="flex flex-wrap gap-1 store-az-menu justify-center" role="tablist" aria-label="<?php esc_attr_e('Filter stores by letter', 'halacoupon'); ?>">
										<!-- All Stores Button -->
										<button type="button"
												class="item px-3 py-2 text-xs font-bold rounded-lg transition-all duration-200 bg-gradient-to-r from-primary to-secondary text-white shadow-soft hover:shadow-glow transform hover:scale-105 active focus:outline-none focus:ring-1 focus:ring-primary/50"
												data-character="all"
												role="tab"
												aria-selected="true"
												aria-controls="all-stores">
											<span><?php esc_html_e('All', 'halacoupon'); ?></span>
										</button>

										<!-- Letter Buttons -->
										<?php foreach ($_stores as $k => $list_stores) { ?>
										<button type="button"
												class="item px-2.5 py-2 text-xs font-bold rounded-lg transition-all duration-200 bg-white/90 backdrop-blur-sm text-text border border-gray-200 hover:bg-gradient-to-r hover:from-primary hover:to-secondary hover:text-white hover:border-primary hover:shadow-soft transform hover:scale-105 focus:outline-none focus:ring-1 focus:ring-primary/50"
												data-character="<?php echo esc_attr($k); ?>"
												role="tab"
												aria-selected="false"
												aria-controls="character-<?php echo esc_attr($k); ?>">
											<span class="block">
												<span class="font-bold text-sm"><?php echo esc_html($k); ?></span>
												<span class="block text-xs opacity-75 leading-none"><?php echo count($list_stores); ?></span>
											</span>
										</button>
										<?php } ?>

										<!-- Numbers Button -->
										<?php if (count($_stores_number)) { ?>
										<button type="button"
												class="item px-2.5 py-2 text-xs font-bold rounded-lg transition-all duration-200 bg-white/90 backdrop-blur-sm text-text border border-gray-200 hover:bg-gradient-to-r hover:from-accent hover:to-gray-700 hover:text-white hover:border-accent hover:shadow-soft transform hover:scale-105 focus:outline-none focus:ring-1 focus:ring-accent/50"
												data-character="0-9"
												role="tab"
												aria-selected="false"
												aria-controls="character-0-9">
											<span class="block">
												<span class="font-bold text-sm"><?php esc_html_e('#', 'halacoupon'); ?></span>
												<span class="block text-xs opacity-75 leading-none"><?php echo count($_stores_number); ?></span>
											</span>
										</button>
										<?php } ?>
									</div>
								</div>
							</div>

						</div>
					</div>

				<div class="store-listing-container">
					<div class="store-listing">
						<?php foreach (  $_stores as $k => $list_stores ) { ?>
						<div id="character-<?php echo esc_attr( $k ); ?>" class="store-section mb-20" role="tabpanel" aria-labelledby="tab-<?php echo esc_attr($k); ?>">
							<!-- Enhanced Section Header with Premium Design -->
							<div class="mb-10">
								<div class="flex items-center gap-6 mb-6">
									<!-- Letter Badge -->
									<div class="relative">
										<div class="w-16 h-16 bg-gradient-to-br from-primary via-secondary to-primary rounded-2xl flex items-center justify-center shadow-glow transform rotate-3 hover:rotate-0 transition-transform duration-300">
											<span class="text-2xl font-black text-white"><?php echo esc_html($k); ?></span>
										</div>
										<div class="absolute -top-1 -<?php echo $is_rtl ? 'left' : 'right'; ?>-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
											<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
											</svg>
										</div>
									</div>

									<!-- Section Info -->
									<div class="flex-1">
										<h3 class="text-3xl font-bold text-text mb-2 leading-tight">
											<?php printf( esc_html__( 'Stores starting with %s', 'halacoupon' ), $k ); ?>
										</h3>
										<div class="flex items-center gap-4 text-gray-600">
											<span class="flex items-center gap-2">
												<svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
												</svg>
												<span class="font-semibold"><?php echo count($list_stores); ?></span>
												<span><?php esc_html_e('verified stores', 'halacoupon'); ?></span>
											</span>
											<span class="text-sm opacity-75">•</span>
											<span class="text-sm">
												<?php
												$total_coupons_in_section = 0;
												foreach ($list_stores as $store) {
													$coupon_counts = halacoupon_get_coupon_counts_by_type($store->term_id, 'coupon_store');
													$total_coupons_in_section += ($coupon_counts['code'] ?? 0) + ($coupon_counts['sale'] ?? 0);
												}
												printf(esc_html__('%d total offers', 'halacoupon'), $total_coupons_in_section);
												?>
											</span>
										</div>
									</div>
								</div>

								<!-- Decorative Divider -->
								<div class="relative">
									<div class="h-px bg-gradient-to-r from-transparent via-primary/30 to-transparent"></div>
									<div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-primary rounded-full shadow-glow"></div>
								</div>
							</div>

							<!-- Minimized Store Cards Grid - 4 per row, Responsive, RTL/LTR -->
							<div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 lg:gap-6" data-character="<?php echo esc_attr($k); ?>">
								<?php foreach ( $list_stores as $store ) { ?>
								<?php
								$store_image = get_term_meta($store->term_id, '_ags_store_image', true);
								$store_link = get_term_link( $store, 'coupon_store' );
								$coupon_counts = halacoupon_get_coupon_counts_by_type($store->term_id, 'coupon_store');
								$total_coupons = ($coupon_counts['code'] ?? 0) + ($coupon_counts['sale'] ?? 0);
								?>
								<!-- Minimized Store Card -->
								<article class="store-card group" data-store-name="<?php echo esc_attr(strtolower($store->name)); ?>" itemscope itemtype="https://schema.org/Store">
									<a href="<?php echo esc_url($store_link); ?>" class="block" itemprop="url">
										<div class="relative bg-white/95 backdrop-blur-sm rounded-xl border border-white/30 shadow-soft overflow-hidden transition-all duration-300 hover:shadow-glow hover:bg-white hover:-translate-y-2 hover:scale-105">

											<!-- Gradient Overlay -->
											<div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

											<!-- Verified Badge -->
											<div class="absolute top-2 <?php echo $is_rtl ? 'left-2' : 'right-2'; ?> z-10">
												<div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center shadow-sm">
													<svg class="w-2.5 h-2.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
													</svg>
												</div>
											</div>

											<div class="relative p-4">
												<!-- Store Logo -->
												<div class="text-center mb-3">
													<div class="relative w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 mx-auto">
														<?php if ($store_image) : ?>
														<img src="<?php echo esc_url($store_image); ?>"
															alt="<?php echo esc_attr($store->name); ?>"
															class="w-full h-full rounded-lg object-contain border border-gray-100 group-hover:border-primary/30 transition-colors duration-300 bg-white p-1.5 shadow-soft"
															itemprop="logo"
															loading="lazy">
														<?php else : ?>
														<div class="w-full h-full rounded-lg bg-gradient-to-br from-primary/15 to-secondary/15 border border-primary/20 flex items-center justify-center text-primary font-bold text-sm lg:text-base shadow-soft group-hover:shadow-glow transition-all duration-300">
															<?php echo esc_html(mb_substr($store->name, 0, 2)); ?>
														</div>
														<?php endif; ?>
													</div>
												</div>

												<!-- Store Information -->
												<div class="text-center space-y-2">
													<!-- Store Name -->
													<h4 class="text-sm lg:text-base font-bold text-text group-hover:text-primary transition-colors duration-300 leading-tight line-clamp-2" itemprop="name">
														<?php echo esc_html($store->name); ?>
													</h4>

													<!-- Offers Count -->
													<div class="flex items-center justify-center gap-1 text-xs">
														<div class="flex items-center gap-1 bg-gradient-to-r from-primary/10 to-secondary/10 px-2 py-1 rounded-full border border-primary/20">
															<svg class="w-3 h-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
															</svg>
															<span class="font-bold text-primary"><?php echo esc_html($total_coupons); ?></span>
															<span class="text-gray-600"><?php esc_html_e('deals', 'halacoupon'); ?></span>
														</div>
													</div>
												</div>
											</div>

											<!-- Bottom Shine Effect -->
											<div class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary via-secondary to-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
										</div>
									</a>
								</article>
								<?php } ?>
							</div>
						</div>
						<?php  } ?>

						<?php if ( count( $_stores_number ) ) { ?>
						<div id="character-0-9" class="store-section mb-20" role="tabpanel" aria-labelledby="tab-0-9">
							<!-- Enhanced Section Header for Numbers -->
							<div class="mb-10">
								<div class="flex items-center gap-6 mb-6">
									<!-- Number Badge -->
									<div class="relative">
										<div class="w-16 h-16 bg-gradient-to-br from-accent via-gray-700 to-accent rounded-2xl flex items-center justify-center shadow-glow transform rotate-3 hover:rotate-0 transition-transform duration-300">
											<span class="text-2xl font-black text-white">#</span>
										</div>
										<div class="absolute -top-1 -<?php echo $is_rtl ? 'left' : 'right'; ?>-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
											<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
											</svg>
										</div>
									</div>

									<!-- Section Info -->
									<div class="flex-1">
										<h3 class="text-3xl font-bold text-text mb-2 leading-tight">
											<?php esc_html_e( 'Stores starting with numbers', 'halacoupon' ); ?>
										</h3>
										<div class="flex items-center gap-4 text-gray-600">
											<span class="flex items-center gap-2">
												<svg class="w-4 h-4 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
												</svg>
												<span class="font-semibold"><?php echo count($_stores_number); ?></span>
												<span><?php esc_html_e('verified stores', 'halacoupon'); ?></span>
											</span>
											<span class="text-sm opacity-75">•</span>
											<span class="text-sm">
												<?php
												$total_coupons_in_section = 0;
												foreach ($_stores_number as $store) {
													$coupon_counts = halacoupon_get_coupon_counts_by_type($store->term_id, 'coupon_store');
													$total_coupons_in_section += ($coupon_counts['code'] ?? 0) + ($coupon_counts['sale'] ?? 0);
												}
												printf(esc_html__('%d total offers', 'halacoupon'), $total_coupons_in_section);
												?>
											</span>
										</div>
									</div>
								</div>

								<!-- Decorative Divider -->
								<div class="relative">
									<div class="h-px bg-gradient-to-r from-transparent via-accent/30 to-transparent"></div>
									<div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-accent rounded-full shadow-glow"></div>
								</div>
							</div>

							<!-- Minimized Store Cards Grid for Numbers - 4 per row, Responsive, RTL/LTR -->
							<div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 lg:gap-6" data-character="0-9">
								<?php foreach ( $_stores_number as $store ) { ?>
								<?php
								$store_image = get_term_meta($store->term_id, '_ags_store_image', true);
								$store_link = get_term_link( $store, 'coupon_store' );
								$coupon_counts = halacoupon_get_coupon_counts_by_type($store->term_id, 'coupon_store');
								$total_coupons = ($coupon_counts['code'] ?? 0) + ($coupon_counts['sale'] ?? 0);
								?>
								<!-- Minimized Store Card for Numbers -->
								<article class="store-card group" data-store-name="<?php echo esc_attr(strtolower($store->name)); ?>" itemscope itemtype="https://schema.org/Store">
									<a href="<?php echo esc_url($store_link); ?>" class="block" itemprop="url">
										<div class="relative bg-white/95 backdrop-blur-sm rounded-xl border border-white/30 shadow-soft overflow-hidden transition-all duration-300 hover:shadow-glow hover:bg-white hover:-translate-y-2 hover:scale-105">

											<!-- Gradient Overlay -->
											<div class="absolute inset-0 bg-gradient-to-br from-accent/5 via-transparent to-gray-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

											<!-- Verified Badge -->
											<div class="absolute top-2 <?php echo $is_rtl ? 'left-2' : 'right-2'; ?> z-10">
												<div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center shadow-sm">
													<svg class="w-2.5 h-2.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
													</svg>
												</div>
											</div>

											<div class="relative p-4">
												<!-- Store Logo -->
												<div class="text-center mb-3">
													<div class="relative w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 mx-auto">
														<?php if ($store_image) : ?>
														<img src="<?php echo esc_url($store_image); ?>"
															alt="<?php echo esc_attr($store->name); ?>"
															class="w-full h-full rounded-lg object-contain border border-gray-100 group-hover:border-accent/30 transition-colors duration-300 bg-white p-1.5 shadow-soft"
															itemprop="logo"
															loading="lazy">
														<?php else : ?>
														<div class="w-full h-full rounded-lg bg-gradient-to-br from-accent/15 to-gray-500/15 border border-accent/20 flex items-center justify-center text-accent font-bold text-sm lg:text-base shadow-soft group-hover:shadow-glow transition-all duration-300">
															<?php echo esc_html(mb_substr($store->name, 0, 2)); ?>
														</div>
														<?php endif; ?>
													</div>
												</div>

												<!-- Store Information -->
												<div class="text-center space-y-2">
													<!-- Store Name -->
													<h4 class="text-sm lg:text-base font-bold text-text group-hover:text-accent transition-colors duration-300 leading-tight line-clamp-2" itemprop="name">
														<?php echo esc_html($store->name); ?>
													</h4>

													<!-- Offers Count -->
													<div class="flex items-center justify-center gap-1 text-xs">
														<div class="flex items-center gap-1 bg-gradient-to-r from-accent/10 to-gray-500/10 px-2 py-1 rounded-full border border-accent/20">
															<svg class="w-3 h-3 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
															</svg>
															<span class="font-bold text-accent"><?php echo esc_html($total_coupons); ?></span>
															<span class="text-gray-600"><?php esc_html_e('deals', 'halacoupon'); ?></span>
														</div>
													</div>
												</div>
											</div>

											<!-- Bottom Shine Effect -->
											<div class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-accent via-gray-600 to-accent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
										</div>
									</a>
								</article>
								<?php } ?>
							</div>
						</div>
						<?php  } ?>

					</div>
				</div>
			</section>

				</div>
			</section>

		</div>
		<?php } else { ?>
		<!-- Enhanced No Stores Found Section -->
		<section class="py-16">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="bg-white/80 backdrop-blur-xl rounded-3xl border border-white/20 shadow-float p-12 text-center">
					<div class="max-w-md mx-auto">
						<!-- Animated Icon -->
						<div class="relative mb-8">
							<div class="w-24 h-24 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center animate-pulse">
								<svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
								</svg>
							</div>
							<!-- Floating elements -->
							<div class="absolute top-0 left-0 w-4 h-4 bg-primary/20 rounded-full animate-float"></div>
							<div class="absolute top-4 right-2 w-3 h-3 bg-secondary/20 rounded-full animate-float" style="animation-delay: 1s;"></div>
							<div class="absolute bottom-2 left-4 w-2 h-2 bg-accent/20 rounded-full animate-float" style="animation-delay: 2s;"></div>
						</div>

						<h3 class="text-2xl font-bold text-text mb-4"><?php esc_html_e( 'No stores found', 'halacoupon' ); ?></h3>
						<p class="text-gray-600 mb-8 leading-relaxed"><?php esc_html_e( 'We couldn\'t find any stores at the moment. Our team is constantly adding new stores and deals. Please check back later or contact us if you\'re looking for a specific store.', 'halacoupon' ); ?></p>

						<!-- Action buttons -->
						<div class="flex flex-col sm:flex-row gap-4 justify-center">
							<a href="<?php echo esc_url(home_url('/')); ?>"
							   class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-xl font-medium hover:shadow-glow transition-all duration-300">
								<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
								</svg>
								<?php esc_html_e('Go to Homepage', 'halacoupon'); ?>
							</a>

							<?php if (function_exists('halacoupon_get_option') && halacoupon_get_option('contact_form_email')) : ?>
							<a href="<?php echo esc_url(get_permalink(get_page_by_path('contact'))); ?>"
							   class="inline-flex items-center gap-2 px-6 py-3 bg-white/80 backdrop-blur-sm text-text border border-gray-200 rounded-xl font-medium hover:bg-white hover:shadow-glow transition-all duration-300">
								<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
								</svg>
								<?php esc_html_e('Contact Us', 'halacoupon'); ?>
							</a>
							<?php endif; ?>
						</div>
					</div>
				</div>
			</div>
		</section>
		<?php } ?>

	</main><!-- #main -->
</div><!-- #primary -->

<?php

        wp_reset_postdata();

        ?>

</div> <!-- /#content-wrap -->





<!-- Enhanced JavaScript for Store Filtering with Premium UX -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced Store Filtering System (No Search, No Scrolling)
    const filterButtons = document.querySelectorAll('.store-az-menu .item');
    const storeSections = document.querySelectorAll('.store-section');
    const storeCards = document.querySelectorAll('.store-card');

    let currentFilter = 'all';

    // Enhanced Filter Functionality
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const character = this.getAttribute('data-character');
            currentFilter = character;

            // Update active button with enhanced animations
            updateActiveButton(this);

            // Apply filters (no scrolling)
            applyFilters();

            // Update ARIA attributes for accessibility
            updateAriaAttributes(character);
        });
    });

    // Search functionality removed for simplified filtering

    // Simplified apply filters function (character-based only)
    function applyFilters() {
        storeSections.forEach(section => {
            const sectionCharacter = section.getAttribute('id').replace('character-', '');

            // Check if section matches current filter
            const sectionMatches = currentFilter === 'all' || sectionCharacter === currentFilter;

            if (sectionMatches) {
                showSection(section);

                // Show all cards in matching sections
                const cardsInSection = section.querySelectorAll('.store-card');
                cardsInSection.forEach(card => {
                    showCard(card);
                });
            } else {
                hideSection(section);
            }
        });
    }

    // Enhanced button state management
    function updateActiveButton(activeButton) {
        filterButtons.forEach(btn => {
            btn.classList.remove('active', 'bg-gradient-to-r', 'from-primary', 'to-secondary', 'text-white', 'shadow-glow');
            btn.classList.add('bg-white/90', 'backdrop-blur-sm', 'text-text', 'border', 'border-gray-200');
            btn.setAttribute('aria-selected', 'false');
        });

        activeButton.classList.add('active', 'bg-gradient-to-r', 'from-primary', 'to-secondary', 'text-white', 'shadow-glow');
        activeButton.classList.remove('bg-white/90', 'backdrop-blur-sm', 'text-text', 'border', 'border-gray-200');
        activeButton.setAttribute('aria-selected', 'true');
    }

    // Enhanced CSS-only show/hide animations for better performance
    function showCard(card) {
        card.classList.remove('hidden');
        card.classList.add('visible');
        card.style.display = 'block';

        // Force reflow for smooth animation
        card.offsetHeight;

        // Use requestAnimationFrame for smooth animations
        requestAnimationFrame(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0) scale(1)';
        });
    }

    function hideCard(card) {
        card.classList.remove('visible');
        card.classList.add('hidden');

        // Use CSS transitions for smooth hiding
        card.style.opacity = '0';
        card.style.transform = 'translateY(-20px) scale(0.95)';

        // Hide after animation completes
        setTimeout(() => {
            if (card.classList.contains('hidden')) {
                card.style.display = 'none';
            }
        }, 300);
    }

    function showSection(section) {
        section.classList.remove('hidden');
        section.style.display = 'block';

        // Force reflow for smooth animation
        section.offsetHeight;

        requestAnimationFrame(() => {
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        });
    }

    function hideSection(section) {
        section.classList.add('hidden');
        section.style.opacity = '0';
        section.style.transform = 'translateY(-10px)';

        // Hide after animation completes
        setTimeout(() => {
            if (section.classList.contains('hidden')) {
                section.style.display = 'none';
            }
        }, 300);
    }

    // Results counter and no results message removed for simplified filtering

    // Update ARIA attributes for accessibility
    function updateAriaAttributes(character) {
        filterButtons.forEach(btn => {
            const btnCharacter = btn.getAttribute('data-character');
            btn.setAttribute('aria-selected', btnCharacter === character ? 'true' : 'false');
        });
    }

    // Enhanced hover effects for store cards
    storeCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Keyboard navigation for filter buttons
    filterButtons.forEach((button, index) => {
        button.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
                e.preventDefault();
                const direction = e.key === 'ArrowRight' ? 1 : -1;
                const nextIndex = (index + direction + filterButtons.length) % filterButtons.length;
                filterButtons[nextIndex].focus();
            } else if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });

    // Performance optimization: Debounce function
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Initialize CSS-only filtering system with error handling
    function initializeCSSFiltering() {
        try {
            console.log('Initializing CSS-only store filtering system...');

            // Ensure all store sections are visible initially
            storeSections.forEach((section, index) => {
                section.style.display = 'block';
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
                section.classList.remove('hidden');
                section.classList.add('visible');
            });

            // Ensure all store cards are visible initially
            storeCards.forEach((card, index) => {
                card.style.display = 'block';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0) scale(1)';
                card.classList.remove('hidden');
                card.classList.add('visible');
            });

            console.log(`CSS filtering initialized: ${storeCards.length} cards, ${storeSections.length} sections`);

        } catch (error) {
            console.error('Error initializing CSS filtering:', error);

            // Fallback: ensure basic functionality
            if (resultsCounter) {
                resultsCounter.textContent = '<?php esc_html_e('Stores loaded', 'halacoupon'); ?>';
            }
        }
    }

    // Performance monitoring
    function measureFilteringPerformance(callback) {
        const startTime = performance.now();
        callback();
        const endTime = performance.now();
        console.log(`Filtering completed in ${(endTime - startTime).toFixed(2)}ms`);
    }

    // Enhanced apply filters with performance monitoring
    const originalApplyFilters = applyFilters;
    applyFilters = function() {
        measureFilteringPerformance(originalApplyFilters);
    };

    // Initialize the filtering system
    initializeCSSFiltering();

    // Add loading state management
    function setLoadingState(isLoading) {
        const storeContainer = document.querySelector('.store-listing');
        if (storeContainer) {
            if (isLoading) {
                storeContainer.classList.add('loading');
            } else {
                storeContainer.classList.remove('loading');
            }
        }
    }
});
</script>

<!-- Enhanced CSS for Store Filtering Performance -->
<style>
/* CSS-only filtering performance optimizations */
.store-section {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.store-section.hidden {
    opacity: 0;
    transform: translateY(-10px);
    pointer-events: none;
}

.store-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.store-card.hidden {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
    pointer-events: none;
}

.store-card.visible {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: auto;
}

/* Enhanced hover effects for better UX */
.store-card:hover {
    transform: translateY(-8px) scale(1.02);
}

/* Search input removed for simplified filtering */

/* Compact filter button styles for 30+ characters */
.store-az-menu {
    max-width: 100%;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(235, 191, 67, 0.3) transparent;
}

.store-az-menu::-webkit-scrollbar {
    height: 4px;
}

.store-az-menu::-webkit-scrollbar-track {
    background: transparent;
}

.store-az-menu::-webkit-scrollbar-thumb {
    background: rgba(235, 191, 67, 0.3);
    border-radius: 2px;
}

.store-az-menu .item {
    min-width: 40px;
    flex-shrink: 0;
    text-align: center;
    line-height: 1.2;
	    margin: 4px;

}

.store-az-menu .item.active {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(235, 191, 67, 0.3);
}

/* Compact button spacing */
.store-az-menu .item:not(:last-child) {
    margin-right: 0.25rem;
}

[dir="rtl"] .store-az-menu .item:not(:last-child) {
    margin-right: 0;
    margin-left: 0.25rem;
}

/* Loading states */
.store-listing.loading .store-card {
    opacity: 0.5;
    pointer-events: none;
}

/* Line clamp utility for text truncation */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Responsive grid improvements */
@media (max-width: 640px) {
    .store-card {
        margin-bottom: 1rem;
    }

    /* Smaller cards on mobile */
    .store-card .relative.p-4 {
        padding: 0.75rem;
    }

    /* Adjust logo size on mobile */
    .store-card .w-12.h-12 {
        width: 2.5rem;
        height: 2.5rem;
    }
}

@media (min-width: 641px) and (max-width: 1024px) {
    /* Tablet optimizations */
    .store-card .text-sm.lg\\:text-base {
        font-size: 0.875rem;
    }
}

/* RTL/LTR specific improvements */
[dir="rtl"] .store-card .group-hover\\:translate-x-1 {
    transform: translateX(-0.25rem);
}

[dir="ltr"] .store-card .group-hover\\:translate-x-1 {
    transform: translateX(0.25rem);
}

/* Enhanced hover states for minimized cards */
.store-card:hover .w-12.h-12,
.store-card:hover .sm\\:w-14.sm\\:h-14,
.store-card:hover .lg\\:w-16.lg\\:h-16 {
    transform: scale(1.1);
}

/* Improved focus states for accessibility */
.store-card a:focus {
    outline: 2px solid #EBBF43;
    outline-offset: 2px;
    border-radius: 0.75rem;
}

/* Grid gap adjustments for different screen sizes */
@media (max-width: 640px) {
    .grid.gap-4.lg\\:gap-6 {
        gap: 0.75rem;
    }
}

@media (min-width: 1024px) {
    .grid.gap-4.lg\\:gap-6 {
        gap: 1.5rem;
    }
}

/* Accessibility improvements */
.store-az-menu .item:focus {
    outline: 2px solid #EBBF43;
    outline-offset: 2px;
}

/* Performance: Use transform instead of changing layout properties */
.store-section,
.store-card {
    will-change: transform, opacity;
}

/* Smooth scrolling for better UX */
html {
    scroll-behavior: smooth;
}

/* No results message styling */
#no-results-message {
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<?php get_footer(); ?>