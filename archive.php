<?php
/**
 * Archive Template - Modern Creative Design
 * 
 * Enhanced archive page with glass morphism, brand colors, and responsive design
 * Supports categories, tags, dates, and custom taxonomies
 *
 * @package HalaCoupon
 * @version 1.0.0 - Creative Archive Design
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

get_header();

// Get archive information
$archive_title = get_the_archive_title();
$archive_description = get_the_archive_description();
$is_rtl = is_rtl();
$queried_object = get_queried_object();

// Determine archive type for styling
$archive_type = 'default';
if (is_category()) {
    $archive_type = 'category';
} elseif (is_tag()) {
    $archive_type = 'tag';
} elseif (is_date()) {
    $archive_type = 'date';
} elseif (is_author()) {
    $archive_type = 'author';
}

?>

<div class="min-h-screen bg-background" dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">

    <section class="relative py-16 md:py-24 bg-gradient-to-br from-primary via-secondary to-primary overflow-hidden">
        
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute hidden lg:block top-10 <?php echo $is_rtl ? 'right-10' : 'left-10'; ?> w-32 h-32 bg-white/10 rounded-full blur-xl animate-float"></div>
            <div class="absolute hidden lg:block bottom-10 <?php echo $is_rtl ? 'left-10' : 'right-10'; ?> w-24 h-24 bg-white/15 rounded-full blur-lg animate-float-slow"></div>
            <div class="absolute hidden lg:block top-1/3 <?php echo $is_rtl ? 'left-1/4' : 'right-1/4'; ?> w-16 h-16 bg-accent/20 rounded-full blur-md animate-float-fast"></div>
            
            <div class="absolute hidden lg:block top-20 <?php echo $is_rtl ? 'left-20' : 'right-20'; ?> w-8 h-8 border-2 border-white/20 rotate-45 animate-pulse"></div>
            <div class="absolute hidden lg:block bottom-32 <?php echo $is_rtl ? 'right-32' : 'left-32'; ?> w-6 h-6 bg-white/10 rounded-full animate-bounce"></div>
        </div>

        <!-- Content Container -->
        <div class="relative z-10">
            <div class="container mx-auto px-4">
                <div class="text-center max-w-4xl mx-auto">
                    
                    <!-- Archive Type Badge -->
                    <div class="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full px-4 py-2 mb-6">
                        <?php if ($archive_type === 'category') : ?>
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                            </svg>
                            <span class="text-white font-medium"><?php esc_html_e('Category', 'halacoupon'); ?></span>
                        <?php elseif ($archive_type === 'tag') : ?>
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                            </svg>
                            <span class="text-white font-medium"><?php esc_html_e('Tag', 'halacoupon'); ?></span>
                        <?php elseif ($archive_type === 'date') : ?>
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                            <span class="text-white font-medium"><?php esc_html_e('Date Archive', 'halacoupon'); ?></span>
                        <?php elseif ($archive_type === 'author') : ?>
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                            <span class="text-white font-medium"><?php esc_html_e('Author', 'halacoupon'); ?></span>
                        <?php else : ?>
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                            </svg>
                            <span class="text-white font-medium"><?php esc_html_e('Archive', 'halacoupon'); ?></span>
                        <?php endif; ?>
                    </div>

                    <!-- Archive Title -->
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                        <?php echo wp_kses_post($archive_title); ?>
                    </h1>

                    <!-- Archive Description -->
                    <?php if ($archive_description) : ?>
                    <div class="text-xl text-white/90 max-w-2xl mx-auto leading-relaxed">
                        <?php echo wp_kses_post($archive_description); ?>
                    </div>
                    <?php endif; ?>


                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="relative py-12 md:py-20">
        <div class="container mx-auto px-4">
            
            <?php if (have_posts()) : ?>
            
            <!-- Content Grid -->
            <div class="max-w-7xl mx-auto">
                
                <!-- Posts Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mb-12">
                    <?php
                    $post_index = 0;
                    while (have_posts()) :
                        the_post();
                        $post_index++;
                        
                        // Include the loop template
                        get_template_part('loop/loop');
                        
                    endwhile;
                    ?>
                </div>

                <!-- Enhanced Pagination -->
                <div class="flex justify-center">
                    <?php
                    // Use theme's pagination function
                    if (function_exists('halacoupon_pagination')) {
                        halacoupon_pagination();
                    } else {
                        the_posts_pagination([
                            'mid_size' => 2,
                            'prev_text' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/></svg>',
                            'next_text' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/></svg>',
                        ]);
                    }
                    ?>
                </div>

            </div>

            <?php else : ?>
            
            <!-- No Posts Found -->
            <div class="max-w-2xl mx-auto text-center py-16">
                <div class="glass-card rounded-2xl border border-white/30 p-8 md:p-12">
                    
                    <!-- Empty State Icon -->
                    <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center">
                        <svg class="w-12 h-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                    </div>

                    <h2 class="text-2xl md:text-3xl font-bold text-text mb-4">
                        <?php esc_html_e('No Posts Found', 'halacoupon'); ?>
                    </h2>
                    
                    <p class="text-text/70 mb-8 leading-relaxed">
                        <?php esc_html_e('Sorry, no posts were found in this archive. Try browsing other categories or return to the homepage.', 'halacoupon'); ?>
                    </p>

                    <a href="<?php echo esc_url(home_url('/')); ?>" 
                       class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-primary text-white font-semibold rounded-2xl shadow-glow hover:shadow-float transform hover:scale-105 transition-all duration-300">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                        </svg>
                        <?php esc_html_e('Back to Home', 'halacoupon'); ?>
                    </a>
                </div>
            </div>

            <?php endif; ?>

        </div>
    </main>

</div>

<?php get_footer(); ?>
