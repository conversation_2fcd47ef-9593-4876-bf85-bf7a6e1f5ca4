<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */


$theme_data   = wp_get_theme();
if ( $theme_data->exists() ) {
	define( 'ST_THEME_NAME', $theme_data->get( 'Name' ) );
	define( 'ST_THEME_VERSION', $theme_data->get( 'Version' ) );
	
}

if (!defined('halacoupon_THEME_URI')) {
    define('halacoupon_THEME_URI', get_template_directory_uri() . '/');
}

if (!defined('halacoupon_ASSETS_PATH')) {
    define('halacoupon_ASSETS_PATH', halacoupon_THEME_URI . 'assets/');
}

if (!defined('halacoupon_CSS_PATH')) {
    define('halacoupon_CSS_PATH', halacoupon_ASSETS_PATH . 'css/');
}

if (!defined('halacoupon_JS_PATH')) {
    define('halacoupon_JS_PATH', halacoupon_ASSETS_PATH . 'js/');
}

if (!defined('halacoupon_IMAGES_PATH')) {
    define('halacoupon_IMAGES_PATH', halacoupon_ASSETS_PATH . 'images/');
}





if ( ! isset( $content_width ) ) {
	$content_width = 1280; /* pixels */
}

if ( ! function_exists( 'halacoupon_theme_setup' ) ) :

	function halacoupon_theme_setup() {

		// Force load our theme's translation file with higher priority
		$locale = get_locale();
		if ($locale === 'ar' || isset($_GET['force_arabic']) || isset($_GET['lang'])) {
			$mo_file = get_template_directory() . '/languages/ar.mo';
			if (file_exists($mo_file)) {
				// Unload any existing textdomain first
				unload_textdomain('halacoupon');
				// Load our specific translation file
				load_textdomain('halacoupon', $mo_file);
			}
		} else {
			load_theme_textdomain( 'halacoupon', get_template_directory() . '/languages' );
		}
		add_theme_support( 'automatic-feed-links' );
		add_filter( 'widget_text', 'do_shortcode' );
		add_theme_support( 'title-tag' );
		add_theme_support( 'post-thumbnails' );
		add_image_size( 'halacoupon_small_thumb', 200, 115, false );
		add_image_size( 'halacoupon_medium-thumb', 480, 480, false );
		add_image_size( 'halacoupon_blog_medium', 620, 300, true );
		add_theme_support(
			'html5',
			array(
				'search-form',
				'comment-form',
				'comment-list',
				'gallery',
				'caption',
			)
		);
		register_nav_menus(
			array(
				'primary' => esc_html__( 'Primary', 'halacoupon' ),
				'footer_menu_1' => __( 'Footer Menu 1', 'halacoupon' ),
				'footer_menu_2' => __( 'Footer Menu 2', 'halacoupon' ),
				'footer_menu_3' => __( 'Footer Menu 3', 'halacoupon' ),
			)
		);

	}
endif;
add_action( 'after_setup_theme', 'halacoupon_theme_setup' );



require get_template_directory() . '/inc/ags-int.php';

/**
 * Add font preload links for performance optimization (Rubik Font - Local)
 * Preloads critical Rubik font files for optimal performance
 */
function halacoupon_add_font_preloads() {
    $theme_uri = get_template_directory_uri();

    // Preload critical Rubik font weights for optimal performance
    // Regular (400) - Most commonly used
    echo '<link rel="preload" href="' . $theme_uri . '/assets/css/fonts/Rubik-Regular.ttf" as="font" type="font/ttf" crossorigin>';

    // Bold (700) - Commonly used for headings
    echo '<link rel="preload" href="' . $theme_uri . '/assets/css/fonts/Rubik-Bold.ttf" as="font" type="font/ttf" crossorigin>';

    // SemiBold (600) - Used for emphasis
    echo '<link rel="preload" href="' . $theme_uri . '/assets/css/fonts/Rubik-SemiBold.ttf" as="font" type="font/ttf" crossorigin>';

    // Medium (500) - Used for UI elements
    echo '<link rel="preload" href="' . $theme_uri . '/assets/css/fonts/Rubik-Medium.ttf" as="font" type="font/ttf" crossorigin>';
}
add_action('wp_head', 'halacoupon_add_font_preloads', 1);

/**
 * Localize modal strings for JavaScript translation support
 */
function halacoupon_localize_modal_strings() {
    // Only localize if modals script is enqueued
    if (wp_script_is('halacoupon-global-coupon-modal', 'enqueued')) {
        wp_localize_script('halacoupon-global-coupon-modal', 'halacoupon_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('halacoupon_ajax_nonce'),
            'strings' => array(
                'coupon_code'         => __('Coupon Code', 'halacoupon'),
                'copy_short'          => __('COPY', 'halacoupon'),
                'copy_paste_checkout' => __('Copy & paste at checkout', 'halacoupon'),
                'copy_code_button'    => __('Copy & Visit Store', 'halacoupon'),
                'get_deal_now'        => __('Get Deal Now', 'halacoupon'),
                'get_deal_button'     => __('Get Deal', 'halacoupon'),
                'no_code_needed'      => __('No code needed', 'halacoupon'),
                'share_link_copied'   => __('Share link copied!', 'halacoupon'),
                'copied_success'      => __('Copied!', 'halacoupon'),
                'copy_failed'         => __('Copy failed', 'halacoupon'),
                'copy_share_link'     => __('Copy share link', 'halacoupon'),
                'code_copied_redirect' => __('Code copied! Redirecting...', 'halacoupon'),
                'getting_deal_redirect' => __('Getting deal... Redirecting...', 'halacoupon'),
                'loading_deal'        => __('Loading Amazing Deal...', 'halacoupon'),
                'please_wait'         => __('Please wait while we prepare your coupon', 'halacoupon'),
                'copy_code'           => __('Copy Code', 'halacoupon'),
                'code_copied'         => __('Code Copied!', 'halacoupon'),
                'get_deal'            => __('Get Deal', 'halacoupon'),
                'share_coupon'        => __('Share Coupon', 'halacoupon'),
                'close'               => __('Close', 'halacoupon'),
                'error_occurred'      => __('An error occurred. Please try again.', 'halacoupon'),
                'vote_success'        => __('Vote recorded successfully!', 'halacoupon'),
                'vote_error'          => __('Failed to record vote. Please try again.', 'halacoupon'),
                'already_voted'       => __('You have already voted for this coupon.', 'halacoupon'),
                'special_offer'       => __('Special Offer', 'halacoupon'),
                'store'               => __('Store', 'halacoupon'),
                'expires'             => __('Expires:', 'halacoupon'),
                'success_rate'        => __('Success Rate', 'halacoupon'),
                'used_today'          => __('Used Today', 'halacoupon'),
                'coupon_details'      => __('Coupon Details', 'halacoupon'),
                'share_this_coupon'   => __('Share this coupon', 'halacoupon'),
            )
        ));
    }
}
add_action('wp_enqueue_scripts', 'halacoupon_localize_modal_strings', 35);

/**
 * Add language-specific body classes for font switching
 */
function halacoupon_add_language_body_class($classes) {
    if (is_rtl()) {
        $classes[] = 'lang-ar';
        $classes[] = 'font-rubik';
    } else {
        $classes[] = 'lang-en';
        $classes[] = 'font-rubik';
    }
    return $classes;
}
add_filter('body_class', 'halacoupon_add_language_body_class');

/**
 * Enqueue RTL enhancement styles for modern page templates
 */
function halacoupon_enqueue_rtl_enhancements() {
    // Only enqueue RTL styles if RTL is active
    if (is_rtl()) {
        wp_enqueue_style(
            'halacoupon-rtl-enhancements',
            get_template_directory_uri() . '/assets/css/rtl-enhancements.css',
            array(), // Dependencies
            ST_THEME_VERSION,
            'all'
        );
    }
}
add_action('wp_enqueue_scripts', 'halacoupon_enqueue_rtl_enhancements', 15);

/**
 * Enqueue scripts and styles.
 */


/**
 * Helper lib
 */
require_once get_template_directory() . '/inc/core/helper.php';

/**
 * Theme Options
 */
if ( class_exists( 'ReduxFramework' ) ) {
	require_once get_template_directory() . '/inc/config/option-config.php';
}


// Retrieve theme option values
if ( ! function_exists( 'halacoupon_get_option' ) ) {
    /**
     * Retrieves an option value from the global settings.
     *
     * @param string $id The ID of the option.
     * @param mixed $fallback The fallback value if the option is not set.
     * @param string|bool $key (Optional) A specific key to retrieve from the option array.
     * @return mixed The option value or the fallback if not found.
     */
    function halacoupon_get_option( $id, $fallback = '', $key = false ) {
        global $ag_option;
        
        // Get the options array if not already fetched.
        if ( ! isset( $ag_option ) ) {
            $ag_option = get_option( 'ag_halacoupon', array() ); // Ensure $ag_option is an array by default.
        }

        // If the options array is not an array, return the fallback.
        if ( ! is_array( $ag_option ) ) {
            return $fallback;
        }

        // Check if the specified option ID exists.
        if ( isset( $ag_option[ $id ] ) && $ag_option[ $id ] !== '' ) {
            $output = $ag_option[ $id ];

            // If a key is specified and exists within the option array, return it.
            if ( $key && is_array( $output ) && isset( $output[ $key ] ) ) {
                return $output[ $key ];
            }

            return $output; // Return the main option value.
        }

        // Return the fallback if the option is not found or empty.
        return $fallback;
    }
}

/**
 * Support coupon type
 *
 * @return array
 */
function halacoupon_get_coupon_types( $plural = false ){


    if ( $plural ) {
        $types = array(
            'code'       => esc_html__( 'Codes', 'halacoupon' ),
            'sale'       => esc_html__( 'Sales', 'coupolala' ),
        );

    } else {
        $types = array(
            'code'       => esc_html__( 'Code', 'halacoupon' ),
            'sale'       => esc_html__( 'Sale', 'halacoupon' ),
        );

    }
    return apply_filters( 'coupolala_get_coupon_types', $types, $plural );
}




/**
 * Recommend plugins via TGM activation class
 */
require_once get_template_directory() . '/inc/tgmpa/tgmpa-config.php';


/**
 * Post type
 */
require_once get_template_directory() . '/inc/post-type.php';


/**
 * Coupon functions.
 */
require_once get_template_directory() . '/inc/core/coupon.php';

/**
 * Coupon functions.
 */
require_once get_template_directory() . '/inc/core/store.php';


/**
 * Coupon functions.
 */
require_once get_template_directory() . '/inc/core/sharing.php';

/**
 * Search functions.
 */
require_once get_template_directory() . '/inc/core/search.php';


/**
 * Ajax handle
 */
require_once get_template_directory() . '/inc/core/ajax.php';



/**
 * Custom template tags for this theme.
 */
require_once get_template_directory() . '/inc/template-tags.php';
require_once get_template_directory() . '/inc/newsletter.php';
require_once get_template_directory() . '/inc/rating.php';
require_once get_template_directory() . '/inc/likes.php';
require_once get_template_directory() . '/inc/menu-icon.php';
require_once get_template_directory() . '/inc/app/reviews/reviews-functions.php';




if ( is_multisite() ) {
    $file = get_template_directory() . '/inc/network-config.php';
    if ( file_exists( $file ) ) {
        require_once $file;
    }
}










/**
 * Custom functions that act independently of the theme templates.
 */
require_once get_template_directory() . '/inc/extras.php';

/**
 * Load custom metaboxes config.
 */
require_once get_template_directory() . '/inc/config/metabox-config.php';





require_once get_template_directory() . '/out.php';



function halacoupon_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'halacoupon_excerpt_length', 999);
  

add_action('wp_dashboard_setup', 'my_custom_dashboard_widgets');
function my_custom_dashboard_widgets() {
global $wp_meta_boxes;

wp_add_dashboard_widget('custom_help_widget', 'الدعم الفني للقالب', 'custom_dashboard_help');
}

function custom_dashboard_help() {
echo '<p>انت الان تستخدم قالب halacoupon ؟ ! إذا كنت تحتاج مساعده ؟ تواصل مع المطور <a href="https://www.facebook.com/Mr.LaLa.Man">Abdullah Gamal</a>. </p>';
}















/**
 * Enqueue Global Coupon Modal CSS
 */
function halacoupon_enqueue_global_coupon_modal_css() {
    wp_enqueue_style(
        'halacoupon-global-coupon-modal',
        get_template_directory_uri() . '/assets/css/components/global-coupon-modal.css',
        array(),
        ST_THEME_VERSION
    );
}
add_action('wp_enqueue_scripts', 'halacoupon_enqueue_global_coupon_modal_css', 25);

/**
 * Enqueue Global Coupon Modal JS
 */
function halacoupon_enqueue_modal_js() {
        wp_enqueue_script(
            'halacoupon-global-coupon-modal',
            get_template_directory_uri() . '/assets/js/modals.js',
            array('jquery', 'halacoupon_global'),
            ST_THEME_VERSION,
            true
        );

}
add_action('wp_enqueue_scripts', 'halacoupon_enqueue_modal_js', 30);

// Localize modal strings after scripts are enqueued
add_action('wp_enqueue_scripts', 'halacoupon_localize_modal_strings', 35);








