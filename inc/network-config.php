<?php
/**
 * WP_Network_Publisher: Safely publish posts or taxonomies to all subsites in a WordPress Multisite network.
 */

if ( ! class_exists( 'WP_Network_Publisher' ) ) {
	class WP_Network_Publisher {
		/**
		 * Constructor: Hooks for admin bar, action, and notices.
		 */
		public function __construct() {
			add_action( 'admin_bar_menu', [ $this, 'add_admin_bar_link' ], 100 );
			add_action( 'admin_post_publish_to_network', [ $this, 'handle_publish_action' ] );
			add_action( 'admin_post_sync_redux_options', [ $this, 'handle_sync_redux_options_action' ] );
			add_action( 'admin_notices', [ $this, 'publish_network_notice' ] );
		}

		/**
		 * Show admin notice after publishing.
		 */
		public function publish_network_notice() {
			if ( isset( $_GET['publish_network_success'] ) || isset( $_GET['publish_network_error'] ) ) {
				$success = intval( $_GET['publish_network_success'] ?? 0 );
				$error = intval( $_GET['publish_network_error'] ?? 0 );
				if ( $success > 0 ) {
					echo '<div class="notice notice-success is-dismissible"><p>' . esc_html( sprintf( 'Published to %d site(s) successfully.', $success ) ) . '</p></div>';
				}
				if ( $error > 0 ) {
					echo '<div class="notice notice-error is-dismissible"><p>' . esc_html( sprintf( 'Failed to publish to %d site(s).', $error ) ) . '</p></div>';
				}
			}
			if ( isset( $_GET['sync_redux_success'] ) || isset( $_GET['sync_redux_error'] ) ) {
				$success = intval( $_GET['sync_redux_success'] ?? 0 );
				$error = intval( $_GET['sync_redux_error'] ?? 0 );
				if ( $success > 0 ) {
					echo '<div class="notice notice-success is-dismissible"><p>' . esc_html( sprintf( 'Theme options synced to %d site(s) successfully.', $success ) ) . '</p></div>';
				}
				if ( $error > 0 ) {
					echo '<div class="notice notice-error is-dismissible"><p>' . esc_html( sprintf( 'Failed to sync theme options to %d site(s).', $error ) ) . '</p></div>';
				}
			}
		}

		/**
		 * Add admin bar link for publishing only (no Redux sync button).
		 */
		public function add_admin_bar_link( $wp_admin_bar ) {
			if ( ! is_admin() || ! current_user_can( 'manage_network' ) ) {
				return;
			}

			global $pagenow;
			$post_id = 0;
			$term_id = 0;
			$taxonomy = '';
			$url = '';

			if ( $pagenow === 'post.php' && isset( $_GET['post'] ) ) {
				$post_id = intval( $_GET['post'] );
				$url = add_query_arg([
					'action' => 'publish_to_network',
					'post_id' => $post_id,
					'_wpnonce' => wp_create_nonce( 'publish_to_network_action' ),
				], admin_url( 'admin-post.php' ) );
			}
			elseif ( $pagenow === 'term.php' && isset( $_GET['tag_ID'], $_GET['taxonomy'] ) ) {
				$term_id = intval( $_GET['tag_ID'] );
				$taxonomy = sanitize_text_field( $_GET['taxonomy'] );
				$url = add_query_arg([
					'action' => 'publish_to_network',
					'term_id' => $term_id,
					'taxonomy' => $taxonomy,
					'_wpnonce' => wp_create_nonce( 'publish_to_network_action' ),
				], admin_url( 'admin-post.php' ) );
			}

			if ( $url ) {
				$wp_admin_bar->add_node([
					'id'    => 'publish_to_network',
					'title' => '📤 Publish to Network',
					'href'  => $url,
					'meta'  => [ 'class' => 'publish-to-network-bar' ]
				]);
			}
		}

		/**
		 * Handle the publish action for posts or terms.
		 */
		public function handle_publish_action() {
			if ( ! current_user_can( 'manage_network' ) ) {
				wp_die( 'Permission denied.' );
			}

			$nonce = isset( $_GET['_wpnonce'] ) ? $_GET['_wpnonce'] : '';
			if ( ! wp_verify_nonce( $nonce, 'publish_to_network_action' ) ) {
				wp_die( 'Invalid nonce.' );
			}

			$post_id  = isset( $_GET['post_id'] ) ? intval( $_GET['post_id'] ) : 0;
			$term_id  = isset( $_GET['term_id'] ) ? intval( $_GET['term_id'] ) : 0;
			$taxonomy = isset( $_GET['taxonomy'] ) ? sanitize_text_field( $_GET['taxonomy'] ) : '';

			if ( ! $post_id && ! $term_id ) {
				wp_die( 'No post or term specified.' );
			}

			$current_blog_id = get_current_blog_id();
			$sites = get_sites([ 'fields' => 'ids' ]);
			$success = 0;
			$error = 0;

			foreach ( $sites as $site_id ) {
				if ( $site_id == $current_blog_id ) continue;
				try {
					switch_to_blog( $site_id );
					if ( $post_id ) {
						$result = $this->publish_post( $post_id, $current_blog_id );
					} elseif ( $term_id && $taxonomy ) {
						$result = $this->publish_term( $term_id, $taxonomy, $current_blog_id );
					}
					if ( $result ) {
						$success++;
					} else {
						$error++;
					}
				} catch ( Exception $e ) {
					$error++;
				} finally {
					restore_current_blog();
				}
			}

			$redirect_url = isset( $_SERVER['HTTP_REFERER'] ) ? esc_url_raw( $_SERVER['HTTP_REFERER'] ) : admin_url();
			$redirect_url = add_query_arg([
				'publish_network_success' => $success,
				'publish_network_error' => $error
			], $redirect_url);
			wp_safe_redirect( $redirect_url );
			exit;
		}

		/**
		 * Handle Redux sync action and show notice.
		 */
		public function handle_sync_redux_options_action() {
			if ( ! is_main_site() || ! current_user_can( 'manage_network' ) ) {
				wp_die( 'Permission denied.' );
			}
			$nonce = isset( $_GET['_wpnonce'] ) ? $_GET['_wpnonce'] : '';
			if ( ! wp_verify_nonce( $nonce, 'sync_redux_options_action' ) ) {
				wp_die( 'Invalid nonce.' );
			}
			$result = redux_sync_options_to_network( 'ag_halacoupon' );
			$redirect_url = isset( $_SERVER['HTTP_REFERER'] ) ? esc_url_raw( $_SERVER['HTTP_REFERER'] ) : admin_url();
			$redirect_url = add_query_arg([
				'sync_redux_success' => $result['success'],
				'sync_redux_error' => $result['error']
			], $redirect_url);
			wp_safe_redirect( $redirect_url );
			exit;
		}

		/**
		 * Publish a post and all meta to the current blog.
		 */
		private function publish_post( $post_id, $source_blog_id ) {
			$protected_meta = apply_filters( 'wp_network_publisher_protected_meta', [ '_edit_lock', '_edit_last', '_wp_old_slug', '_wp_trash_meta_status', '_wp_trash_meta_time' ] );
			switch_to_blog( $source_blog_id );
			$post = get_post( $post_id );
			if ( ! $post ) {
				restore_current_blog();
				return false;
			}
			$meta = get_post_meta( $post_id );
			$thumbnail_id = get_post_thumbnail_id( $post_id );
			$thumbnail_url = $thumbnail_id ? wp_get_attachment_url( $thumbnail_id ) : '';
			restore_current_blog();

			$new_post = [
				'post_title'    => $post->post_title,
				'post_content'  => $post->post_content,
				'post_excerpt'  => $post->post_excerpt,
				'post_name'     => $post->post_name,
				'post_type'     => $post->post_type,
				'post_status'   => $post->post_status,
				'post_author'   => get_current_user_id(),
				'post_date'     => $post->post_date,
				'post_date_gmt' => $post->post_date_gmt,
			];
			$existing = get_page_by_path( $post->post_name, OBJECT, $post->post_type );
			if ( $existing ) {
				$new_post_id = $existing->ID;
				wp_update_post( array_merge( $new_post, [ 'ID' => $new_post_id ] ) );
			} else {
				$new_post_id = wp_insert_post( $new_post );
			}
			if ( ! $new_post_id ) return false;

			foreach ( $meta as $key => $values ) {
				if ( in_array( $key, $protected_meta ) || strpos( $key, '_wp_' ) === 0 ) continue;
				foreach ( $values as $value ) {
					update_post_meta( $new_post_id, $key, maybe_unserialize( $value ) );
				}
			}

			if ( $thumbnail_url ) {
				$attach_id = $this->copy_attachment_from_url( $thumbnail_url, $new_post_id );
				if ( $attach_id ) {
					set_post_thumbnail( $new_post_id, $attach_id );
				}
			}

			return true;
		}

		/**
		 * Publish a term and all meta to the current blog.
		 */
		private function publish_term( $term_id, $taxonomy, $source_blog_id ) {
			$protected_meta = apply_filters( 'wp_network_publisher_protected_term_meta', [] );
			switch_to_blog( $source_blog_id );
			$term = get_term( $term_id, $taxonomy );
			if ( ! $term || is_wp_error( $term ) ) {
				restore_current_blog();
				return false;
			}
			$meta = function_exists( 'get_term_meta' ) ? get_term_meta( $term_id ) : [];
			restore_current_blog();

			$existing = get_term_by( 'slug', $term->slug, $taxonomy );
			if ( $existing ) {
				$new_term_id = $existing->term_id;
				wp_update_term( $new_term_id, $taxonomy, [
					'name'        => $term->name,
					'description' => $term->description,
					'slug'        => $term->slug,
				] );
			} else {
				$result = wp_insert_term( $term->name, $taxonomy, [
					'slug'        => $term->slug,
					'description' => $term->description,
				] );
				if ( is_wp_error( $result ) ) return false;
				$new_term_id = $result['term_id'];
			}

			if ( $new_term_id && $meta ) {
				foreach ( $meta as $key => $values ) {
					if ( in_array( $key, $protected_meta ) ) continue;
					foreach ( $values as $value ) {
						update_term_meta( $new_term_id, $key, maybe_unserialize( $value ) );
					}
				}
			}

			return true;
		}

		/**
		 * Download and sideload image to current blog, attach to $post_id
		 */
		private function copy_attachment_from_url( $url, $post_id ) {
			if ( ! $url ) return 0;
			require_once ABSPATH . 'wp-admin/includes/file.php';
			require_once ABSPATH . 'wp-admin/includes/media.php';
			require_once ABSPATH . 'wp-admin/includes/image.php';
			$tmp = download_url( $url );
			if ( is_wp_error( $tmp ) ) return 0;
			$file_array = [
				'name'     => basename( $url ),
				'tmp_name' => $tmp,
			];
			$attach_id = media_handle_sideload( $file_array, $post_id );
			@unlink( $tmp );
			if ( is_wp_error( $attach_id ) ) return 0;
			// Generate and update all image sizes/metadata
			$attach_file = get_attached_file( $attach_id );
			if ( $attach_file ) {
				$attach_data = wp_generate_attachment_metadata( $attach_id, $attach_file );
				if ( $attach_data ) {
					wp_update_attachment_metadata( $attach_id, $attach_data );
				}
			}
			return $attach_id;
		}

		/**
		 * Clone all main site content, CPTs, taxonomies, and all meta (including CMB2) to a new site.
		 * Hooks into wpmu_new_blog.
		 */
		public static function clone_main_site_content_to_new_site( $blog_id ) {
			$main_blog_id = get_main_site_id();
			if ( $blog_id == $main_blog_id ) return;

			$opts = get_site_option( 'network_clone_settings', [] );
			$selected_theme = $opts['active_theme'] ?? get_option( 'stylesheet' );

			// 0. Set theme first
			switch_to_blog( $blog_id );
			if ( $selected_theme ) {
				switch_theme( $selected_theme );
			}
			restore_current_blog();

			// 1. Gather all taxonomies, posts, pages, CPTs, menus, and menu items from main site
			switch_to_blog( $main_blog_id );
			$post_types = array_keys( is_array($opts['copy_post_types'] ?? null) ? $opts['copy_post_types'] : [] );
			$taxonomies = array_keys( is_array($opts['copy_taxonomies'] ?? null) ? $opts['copy_taxonomies'] : [] );
			$all_posts = [];
			$all_pages = [];
			$all_cpts = [];
			$post_id_map = [];
			$page_id_map = [];
			$cpt_id_map = [];
			$term_id_map = [];
			foreach ( $post_types as $pt ) {
				if ( $pt === 'page' ) {
					$all_pages = array_merge( $all_pages, get_posts([
						'post_type' => $pt,
						'posts_per_page' => -1,
						'post_status' => 'publish',
						'fields' => 'ids',
					]) );
				} elseif ( $pt === 'post' ) {
					$all_posts = array_merge( $all_posts, get_posts([
						'post_type' => $pt,
						'posts_per_page' => -1,
						'post_status' => 'publish',
						'fields' => 'ids',
					]) );
				} else {
					$all_cpts = array_merge( $all_cpts, get_posts([
						'post_type' => $pt,
						'posts_per_page' => -1,
						'post_status' => 'publish',
						'fields' => 'ids',
					]) );
				}
			}
			$all_terms = [];
			foreach ( $taxonomies as $tax ) {
				$all_terms[ $tax ] = get_terms([
					'taxonomy' => $tax,
					'hide_empty' => false,
					'fields' => 'ids',
				]);
			}
			$menus = get_terms([
				'taxonomy' => 'nav_menu',
				'hide_empty' => false,
				'fields' => 'ids',
			]);
			$menu_items = get_posts([
				'post_type' => 'nav_menu_item',
				'posts_per_page' => -1,
				'post_status' => 'publish',
				'fields' => 'ids',
			]);
			$redux_options = get_option( 'ag_halacoupon' );
			restore_current_blog();

			// 2. Copy all taxonomies and terms (including nav_menu)
			switch_to_blog( $blog_id );
			foreach ( $all_terms as $tax => $term_ids ) {
				foreach ( $term_ids as $term_id ) {
					$new_term_id = self::clone_term_from_main( $term_id, $tax, $main_blog_id );
					$term_id_map[$tax][$term_id] = $new_term_id;
				}
			}
			// 3. Copy all posts (not pages or CPTs)
			foreach ( $all_posts as $post_id ) {
				$new_post_id = self::clone_post_from_main( $post_id, $main_blog_id );
				$post_id_map[$post_id] = $new_post_id;
			}
			// 4. Copy all pages
			foreach ( $all_pages as $page_id ) {
				$new_page_id = self::clone_post_from_main( $page_id, $main_blog_id );
				$page_id_map[$page_id] = $new_page_id;
			}
			// 5. Copy all custom post types (including coupons)
			foreach ( $all_cpts as $cpt_id ) {
				$new_cpt_id = self::clone_post_from_main( $cpt_id, $main_blog_id );
				$cpt_id_map[$cpt_id] = $new_cpt_id;
			}
			// 6. Copy menus (nav_menu taxonomy)
			$menu_id_map = [];
			$menu_slug_map = [];
			$main_menu_slugs = [];
			foreach ( $menus as $menu_term_id ) {
				// Get menu object and slug from main site
				switch_to_blog( $main_blog_id );
				$menu_obj = get_term( $menu_term_id, 'nav_menu' );
				restore_current_blog();
				if ( $menu_obj && !is_wp_error($menu_obj) ) {
					$new_menu_term_id = self::clone_term_from_main( $menu_term_id, 'nav_menu', $main_blog_id );
					$menu_id_map[$menu_term_id] = $new_menu_term_id;
					$menu_slug_map[$menu_obj->slug] = $new_menu_term_id;
					$main_menu_slugs[$menu_term_id] = $menu_obj->slug;
				}
			}
			// 7. Copy nav_menu_item posts, remapping object IDs
			$menu_item_map = [];
			$menu_item_parent_map = [];
			$menu_items_by_menu = [];
			// Group menu items by menu term
			foreach ( $menu_items as $item_id ) {
				switch_to_blog( $main_blog_id );
				$item = get_post( $item_id );
				$item_meta = get_post_meta( $item_id );
				restore_current_blog();
				$menu_term_id = null;
				if ( isset($item_meta['_menu_item_menu'][0]) ) {
					$menu_term_id = intval($item_meta['_menu_item_menu'][0]);
					$menu_items_by_menu[$menu_term_id][] = [
						'id' => $item_id,
						'post' => $item,
						'meta' => $item_meta
					];
				}
			}
			// For each menu, insert all items, then update parent/child and assign to menu
			foreach ( $menu_items_by_menu as $old_menu_id => $items ) {
				foreach ( $items as $item_data ) {
					$item = $item_data['post'];
					$item_meta = $item_data['meta'];
					$new_item = [
						'post_title'    => $item->post_title,
						'post_content'  => $item->post_content,
						'post_excerpt'  => $item->post_excerpt,
						'post_name'     => $item->post_name,
						'post_type'     => $item->post_type,
						'post_status'   => 'publish',
						'post_author'   => get_current_user_id(),
						'post_date'     => $item->post_date,
						'post_date_gmt' => $item->post_date_gmt,
					];
					$new_item_id = wp_insert_post( $new_item );
					if ( ! $new_item_id ) continue;
					$menu_item_map[$item_data['id']] = $new_item_id;
					$menu_term_id = $menu_id_map[$old_menu_id] ?? $old_menu_id;
					foreach ( $item_meta as $key => $values ) {
						foreach ( $values as $value ) {
							if ( $key === '_menu_item_object_id' ) {
								$object_type = $item_meta['_menu_item_type'][0] ?? '';
								$object_id = intval($value);
								$new_object_id = $object_id;
								if ( $object_type === 'post_type' ) {
									if ( isset($post_id_map[$object_id]) ) $new_object_id = $post_id_map[$object_id];
									if ( isset($page_id_map[$object_id]) ) $new_object_id = $page_id_map[$object_id];
									if ( isset($cpt_id_map[$object_id]) ) $new_object_id = $cpt_id_map[$object_id];
								} elseif ( $object_type === 'taxonomy' ) {
									$tax = $item_meta['_menu_item_object'][0] ?? '';
									if ( isset($term_id_map[$tax][$object_id]) ) $new_object_id = $term_id_map[$tax][$object_id];
								}
								update_post_meta( $new_item_id, $key, $new_object_id );
							} elseif ( $key === '_menu_item_menu_item_parent' ) {
								$menu_item_parent_map[$new_item_id] = intval($value);
								update_post_meta( $new_item_id, $key, 0 ); // Set to 0 for now, fix in second pass
							} elseif ( $key === '_menu_item_menu' ) {
								update_post_meta( $new_item_id, $key, $menu_term_id );
							} else {
								update_post_meta( $new_item_id, $key, maybe_unserialize( $value ) );
							}
						}
					}
					if ( $menu_term_id ) {
						wp_set_object_terms( $new_item_id, intval($menu_term_id), 'nav_menu' );
					}
				}
			}
			// Second pass: update parent/child relationships
			foreach ( $menu_item_parent_map as $new_id => $old_parent_id ) {
				if ( $old_parent_id && isset($menu_item_map[$old_parent_id]) ) {
					update_post_meta( $new_id, '_menu_item_menu_item_parent', $menu_item_map[$old_parent_id] );
				} else {
					update_post_meta( $new_id, '_menu_item_menu_item_parent', 0 );
				}
			}
			// Third pass: fix object_id and url for post_type/taxonomy items
			foreach ( $items as $item ) {
				if ( !isset($old_to_new_item_id[$item->ID]) ) continue;
				$new_item_id = $old_to_new_item_id[$item->ID];
				if ( $item->type === 'post_type' && isset($item->object_id) ) {
					switch_to_blog( $main_blog_id );
					$main_post = get_post( $item->object_id );
					restore_current_blog();
					if ( $main_post ) {
						$sub_post = get_page_by_path( $main_post->post_name, OBJECT, $item->object );
						if ( $sub_post ) {
							update_post_meta( $new_item_id, '_menu_item_object_id', $sub_post->ID );
							update_post_meta( $new_item_id, '_menu_item_url', get_permalink($sub_post->ID) );
						}
					}
				} elseif ( $item->type === 'taxonomy' && isset($item->object_id) ) {
					switch_to_blog( $main_blog_id );
					$main_term = get_term( $item->object_id, $item->object );
					restore_current_blog();
					if ( $main_term && !is_wp_error($main_term) ) {
						$sub_term = get_term_by( 'slug', $main_term->slug, $item->object );
						if ( $sub_term ) {
							update_post_meta( $new_item_id, '_menu_item_object_id', $sub_term->term_id );
							update_post_meta( $new_item_id, '_menu_item_url', get_term_link($sub_term) );
						}
					}
				}
			}
			// 9. Update menu locations in theme options
			switch_to_blog( $main_blog_id );
			$main_locations = get_theme_mod( 'nav_menu_locations' );
			restore_current_blog();
			if ( is_array($main_locations) ) {
				$new_locations = [];
				foreach ( $main_locations as $loc => $main_menu_id ) {
					$main_menu_slug = $main_menu_slugs[$main_menu_id] ?? '';
					if ( $main_menu_slug && isset($menu_slug_map[$main_menu_slug]) ) {
						$new_locations[$loc] = $menu_slug_map[$main_menu_slug];
					} elseif ( isset($menu_id_map[$main_menu_id]) ) {
						$new_locations[$loc] = $menu_id_map[$main_menu_id];
					}
				}
				set_theme_mod( 'nav_menu_locations', $new_locations );
			}
			// 10. Copy theme options, widgets, logo (Redux)
			if ( !empty($opts['copy_theme_options']) && $redux_options ) {
				$media_fields = self::get_redux_media_fields();
				foreach ( $media_fields as $field ) {
					if ( !empty($redux_options[$field]['id']) ) {
						$img_id = $redux_options[$field]['id'];
						switch_to_blog( $main_blog_id );
						$url = wp_get_attachment_url( $img_id );
						restore_current_blog();
						if ( $url ) {
							$new_attach_id = ( new self )->copy_attachment_from_url( $url, 0 );
							$redux_options[$field]['id'] = $new_attach_id;
							$redux_options[$field]['url'] = wp_get_attachment_url( $new_attach_id );
						}
					}
				}
				update_option( 'ag_halacoupon', $redux_options );
			}
			// 11. Copy images if checked (already handled in post copy)
			flush_rewrite_rules();
			restore_current_blog();
		}

		/**
		 * Helper: Copy an image from main site to current site, return new attachment ID.
		 */
		private static function clone_image_from_main( $image_id_or_url, $main_blog_id, $attach_to = 0 ) {
			if ( ! $image_id_or_url ) return 0;
			$url = '';
			if ( is_numeric( $image_id_or_url ) ) {
				switch_to_blog( $main_blog_id );
				$url = wp_get_attachment_url( $image_id_or_url );
				restore_current_blog();
			} elseif ( filter_var( $image_id_or_url, FILTER_VALIDATE_URL ) ) {
				$url = $image_id_or_url;
			}
			if ( ! $url ) return 0;
			require_once ABSPATH . 'wp-admin/includes/file.php';
			require_once ABSPATH . 'wp-admin/includes/media.php';
			require_once ABSPATH . 'wp-admin/includes/image.php';
			$tmp = download_url( $url );
			if ( is_wp_error( $tmp ) ) return 0;
			$file_array = [
				'name'     => basename( $url ),
				'tmp_name' => $tmp,
			];
			$attach_id = media_handle_sideload( $file_array, $attach_to );
			@unlink( $tmp );
			if ( is_wp_error( $attach_id ) ) return 0;
			// Generate and update all image sizes/metadata
			$attach_file = get_attached_file( $attach_id );
			if ( $attach_file ) {
				$attach_data = wp_generate_attachment_metadata( $attach_id, $attach_file );
				if ( $attach_data ) {
					wp_update_attachment_metadata( $attach_id, $attach_data );
				}
			}
			return $attach_id;
		}

		/**
		 * Clone a single post (with meta and thumbnail) from main site to current site, remapping CMB2 image fields.
		 */
		public static function clone_post_from_main( $post_id, $main_blog_id ) {
			switch_to_blog( $main_blog_id );
			$post = get_post( $post_id );
			if ( ! $post ) { restore_current_blog(); return false; }
			$meta = get_post_meta( $post_id );
			$thumbnail_id = get_post_thumbnail_id( $post_id );
			$thumbnail_url = $thumbnail_id ? wp_get_attachment_url( $thumbnail_id ) : '';
			$cmb2_image_fields = self::get_cmb2_image_fields( $post->post_type );
			$post_taxonomies = get_object_taxonomies( $post->post_type );
			$post_terms = [];
			foreach ( $post_taxonomies as $tax ) {
				$terms = wp_get_object_terms( $post_id, $tax, [ 'fields' => 'slugs' ] );
				if ( ! is_wp_error($terms) && !empty($terms) ) {
					$post_terms[$tax] = $terms;
				}
			}
			restore_current_blog();
			$new_post = [
				'post_title'    => $post->post_title,
				'post_content'  => $post->post_content,
				'post_excerpt'  => $post->post_excerpt,
				'post_name'     => $post->post_name,
				'post_type'     => $post->post_type,
				'post_status'   => $post->post_status,
				'post_author'   => get_current_user_id(),
				'post_date'     => $post->post_date,
				'post_date_gmt' => $post->post_date_gmt,
			];
			$existing = get_page_by_path( $post->post_name, OBJECT, $post->post_type );
			if ( $existing ) {
				$new_post_id = $existing->ID;
				wp_update_post( array_merge( $new_post, [ 'ID' => $new_post_id ] ) );
			} else {
				$new_post_id = wp_insert_post( $new_post );
			}
			if ( ! $new_post_id ) return false;
			foreach ( $meta as $key => $values ) {
				foreach ( $values as $value ) {
					// Remap CMB2 image fields (single image)
					if ( in_array( $key, $cmb2_image_fields ) && is_numeric($value) ) {
						switch_to_blog( $main_blog_id );
						$url = wp_get_attachment_url( $value );
						restore_current_blog();
						if ( $url ) {
							$new_attach_id = ( new self )->copy_attachment_from_url( $url, $new_post_id );
							$field_val = [ 'id' => $new_attach_id, 'url' => wp_get_attachment_url( $new_attach_id ) ];
							update_post_meta( $new_post_id, $key, $field_val );
							continue;
						}
					}
					// Remap gallery/array of IDs (CMB2 gallery fields)
					if ( is_array($value) && in_array( $key, $cmb2_image_fields ) ) {
						$new_gallery = [];
						foreach ( $value as $v ) {
							if ( is_numeric($v) ) {
								switch_to_blog( $main_blog_id );
								$url = wp_get_attachment_url( $v );
								restore_current_blog();
								if ( $url ) {
									$new_id = ( new self )->copy_attachment_from_url( $url, $new_post_id );
									$new_gallery[] = [ 'id' => $new_id, 'url' => wp_get_attachment_url( $new_id ) ];
								} else {
									$new_gallery[] = $v;
								}
							} else {
								$new_gallery[] = $v;
							}
						}
						update_post_meta( $new_post_id, $key, $new_gallery );
						continue;
					}
					// Remap single attachment ID in meta (non-CMB2 fields)
					if ( is_numeric($value) && get_post_type($value) === 'attachment' ) {
						switch_to_blog( $main_blog_id );
						$url = wp_get_attachment_url( $value );
						restore_current_blog();
						if ( $url ) {
							$new_attach_id = ( new self )->copy_attachment_from_url( $url, $new_post_id );
							update_post_meta( $new_post_id, $key, $new_attach_id );
							continue;
						}
					}
					update_post_meta( $new_post_id, $key, maybe_unserialize( $value ) );
				}
			}
			// Set taxonomies/terms
			foreach ( $post_terms as $tax => $slugs ) {
				wp_set_object_terms( $new_post_id, $slugs, $tax );
			}
			// Force permalink recalculation after terms are set
			wp_update_post([ 'ID' => $new_post_id ]);
			if ( $thumbnail_url ) {
				$attach_id = ( new self )->copy_attachment_from_url( $thumbnail_url, $new_post_id );
				if ( $attach_id ) {
					set_post_thumbnail( $new_post_id, $attach_id );
				}
			}
			return true;
		}

		/**
		 * Clone a single term (with meta) from main site to current site, remapping image fields.
		 */
		public static function clone_term_from_main( $term_id, $taxonomy, $main_blog_id ) {
			switch_to_blog( $main_blog_id );
			$term = get_term( $term_id, $taxonomy );
			$meta = function_exists( 'get_term_meta' ) ? get_term_meta( $term_id ) : [];
			$cmb2_image_fields = self::get_cmb2_image_fields( $taxonomy );
			restore_current_blog();
			$existing = get_term_by( 'slug', $term->slug, $taxonomy );
			if ( $existing ) {
				$new_term_id = $existing->term_id;
				wp_update_term( $new_term_id, $taxonomy, [
					'name'        => $term->name,
					'description' => $term->description,
					'slug'        => $term->slug,
				] );
			} else {
				$result = wp_insert_term( $term->name, $taxonomy, [
					'slug'        => $term->slug,
					'description' => $term->description,
				] );
				if ( is_wp_error( $result ) ) return false;
				$new_term_id = $result['term_id'];
			}
			if ( $new_term_id && $meta ) {
				foreach ( $meta as $key => $values ) {
					foreach ( $values as $value ) {
						// Remap CMB2 image/file fields for taxonomy meta
						if ( in_array( $key, $cmb2_image_fields ) && is_numeric($value) ) {
							switch_to_blog( $main_blog_id );
							$url = wp_get_attachment_url( $value );
							restore_current_blog();
							if ( $url ) {
								$new_attach_id = ( new self )->copy_attachment_from_url( $url, 0 );
								if ( $new_attach_id ) {
									$field_val = [ 'id' => $new_attach_id, 'url' => wp_get_attachment_url( $new_attach_id ) ];
									update_term_meta( $new_term_id, $key, $field_val );
									continue;
								}
							}
						}
						update_term_meta( $new_term_id, $key, maybe_unserialize( $value ) );
					}
				}
			}
			return true;
		}

		/**
		 * Register network admin menu for clone settings.
		 */
		public static function register_network_clone_settings_menu() {
			add_menu_page(
				'New Site Clone Settings',
				'🛠️ Site Clone Settings',
				'manage_network',
				'network-clone-settings',
				[ __CLASS__, 'render_network_clone_settings_page' ],
				'dashicons-admin-multisite',
				99
			);
		}

		/**
		 * Render the network clone settings page with dynamic post types and taxonomies.
		 */
		public static function render_network_clone_settings_page() {
			if ( ! current_user_can( 'manage_network' ) ) return;
			// Process form submission BEFORE output
			if ( isset($_POST['network_clone_settings_nonce']) && wp_verify_nonce($_POST['network_clone_settings_nonce'], 'network_clone_settings_save') ) {
				$opts = [
					'copy_post_types' => array_map('boolval', $_POST['copy_post_types'] ?? []),
					'copy_taxonomies' => array_map('boolval', $_POST['copy_taxonomies'] ?? []),
					'copy_theme_options' => !empty($_POST['copy_theme_options']),
					'copy_media' => !empty($_POST['copy_media']),
					'active_theme' => sanitize_text_field($_POST['active_theme'] ?? ''),
				];
				update_site_option( 'network_clone_settings', $opts );
				echo '<div class="updated notice is-dismissible"><p>Settings saved.</p></div>';
			}
			// Always reload options after possible save
			$opts = get_site_option( 'network_clone_settings', [] );
			$themes = wp_get_themes();
			$active_theme = $opts['active_theme'] ?? get_option( 'stylesheet' );
			// Use show_ui => true to include all admin-visible post types
			$post_types = get_post_types([ 'show_ui' => true ], 'objects');
			$all_taxonomies = get_taxonomies([], 'objects');
			$taxonomies_displayed = [];
			?>
			<div class="wrap">
			<h1>🛠️ Site Clone Settings</h1>
			<form method="post">
			<?php wp_nonce_field( 'network_clone_settings_save', 'network_clone_settings_nonce' ); ?>
			<table class="form-table">
			<tr><th>Default Theme</th><td>
				<select name="active_theme">
				<?php foreach ( $themes as $slug => $theme ) : ?>
					<option value="<?php echo esc_attr($slug); ?>" <?php selected($active_theme, $slug); ?>><?php echo esc_html($theme->get('Name')); ?></option>
				<?php endforeach; ?>
				</select>
			</td></tr>
			<tr><th>Content to Copy</th><td>
			<?php
			foreach ( $post_types as $pt_slug => $pt_obj ) {
				if ( $pt_slug === 'attachment' ) continue;
				$checked = !empty($opts['copy_post_types'][$pt_slug]);
				echo '<label><input type="checkbox" name="copy_post_types['.esc_attr($pt_slug).']" value="1" '.checked($checked, true, false).'> '.esc_html($pt_obj->labels->name).'</label><br>';
				$taxes = get_object_taxonomies( $pt_slug, 'objects' );
				if ( $taxes ) {
					echo '<div style="margin-left:2em;">';
					foreach ( $taxes as $tax_slug => $tax_obj ) {
						$taxonomies_displayed[$tax_slug] = true;
						$tax_checked = !empty($opts['copy_taxonomies'][$tax_slug]);
						echo '<label><input type="checkbox" name="copy_taxonomies['.esc_attr($tax_slug).']" value="1" '.checked($tax_checked, true, false).'> '.esc_html($tax_obj->labels->name).'</label><br>';
					}
					echo '</div>';
				}
			}
			// List orphan taxonomies (not attached to any post type)
			$orphan_taxonomies = array_diff_key($all_taxonomies, $taxonomies_displayed);
			if ( $orphan_taxonomies ) {
				echo '<strong>Other Taxonomies:</strong><br>';
				foreach ( $orphan_taxonomies as $tax_slug => $tax_obj ) {
					$tax_checked = !empty($opts['copy_taxonomies'][$tax_slug]);
					echo '<label><input type="checkbox" name="copy_taxonomies['.esc_attr($tax_slug).']" value="1" '.checked($tax_checked, true, false).'> '.esc_html($tax_obj->labels->name).'</label><br>';
				}
			}
			?>
			<label><input type="checkbox" name="copy_theme_options" value="1" <?php checked( !empty($opts['copy_theme_options']) ); ?>> Theme Options (Redux)</label><br>
			<label><input type="checkbox" name="copy_media" value="1" <?php checked( !empty($opts['copy_media']) ); ?>> Media Attachments</label>
			</td></tr>
			</table>
			<?php submit_button('Save Settings'); ?>
			</form>
			</div>
			<?php
		}

		/**
		 * Get all CMB2 image/media field keys for a post type or taxonomy.
		 * @param string $object_type Post type or taxonomy slug
		 * @return array
		 */
		public static function get_cmb2_image_fields( $object_type ) {
			$fields = [];
			// Try to load CMB2 config if available
			$cmb2_boxes = [];
			$cmb2_config = get_template_directory() . '/inc/config/metabox-config.php';
			if ( file_exists( $cmb2_config ) ) {
				include_once $cmb2_config;
				if ( isset( $cmb2_boxes ) && is_array( $cmb2_boxes ) ) {
					foreach ( $cmb2_boxes as $box ) {
						if ( isset($box['object_types']) && in_array($object_type, $box['object_types']) ) {
							if ( isset($box['fields']) && is_array($box['fields']) ) {
								foreach ( $box['fields'] as $field ) {
									if ( isset($field['type']) && in_array($field['type'], ['file', 'file_list', 'image', 'file_upload', 'pw_map', 'pw_gallery', 'media']) && isset($field['id']) ) {
										$fields[] = $field['id'];
									}
								}
							}
						}
					}
				}
			}
			return $fields;
		}

		/**
		 * Get all Redux media/image field keys from the Redux config.
		 * @return array
		 */
		public static function get_redux_media_fields() {
			$fields = [];
			$reduxConfig = null;
			$redux_config_file = get_template_directory() . '/inc/config/option-config.php';
			if ( file_exists( $redux_config_file ) ) {
				include_once $redux_config_file;
				global $reduxConfig;
				if ( isset($reduxConfig->sections) && is_array($reduxConfig->sections) ) {
					foreach ( $reduxConfig->sections as $section ) {
						if ( isset($section['fields']) && is_array($section['fields']) ) {
							foreach ( $section['fields'] as $field ) {
								if ( isset($field['type']) && $field['type'] === 'media' && isset($field['id']) ) {
									$fields[] = $field['id'];
								}
							}
						}
					}
				}
			}
			return $fields;
		}

		/**
		 * Remove all menus from the current site.
		 */
		public static function remove_all_menus_from_site() {
			$menus = get_terms([
				'taxonomy' => 'nav_menu',
				'hide_empty' => false,
				'fields' => 'ids',
			]);
			foreach ( $menus as $menu_id ) {
				wp_delete_nav_menu( $menu_id );
			}
			// Remove menu locations
			set_theme_mod( 'nav_menu_locations', [] );
		}

		/**
		 * Add a button to the admin bar for subsites to copy menus from the main site.
		 */
		public static function add_copy_menu_from_main_bar() {
			if ( is_network_admin() || is_main_site() ) return;
			global $pagenow;
			if ( $pagenow !== 'nav-menus.php' ) return;
			if ( ! current_user_can( 'edit_theme_options' ) ) return;
			$nonce = wp_create_nonce( 'copy_menu_from_main_action' );
			$url = add_query_arg([
				'action' => 'copy_menu_from_main',
				'_wpnonce' => $nonce,
			], admin_url( 'admin-post.php' ) );
			add_action('admin_notices', function() use ($url) {
				echo '<div class="notice notice-info"><a href="'.esc_url($url).'" class="button button-primary">📋 Copy Menu from Main Site</a> This will replace all menus on this site with the main site menus.</div>';
			});
		}

		/**
		 * Handle the copy menu from main site action.
		 */
		public static function handle_copy_menu_from_main_action() {
			if ( ! current_user_can( 'edit_theme_options' ) ) wp_die('Permission denied.');
			if ( ! isset($_GET['_wpnonce']) || ! wp_verify_nonce($_GET['_wpnonce'], 'copy_menu_from_main_action') ) wp_die('Invalid nonce.');
			$main_blog_id = get_main_site_id();
			$current_blog_id = get_current_blog_id();
			if ( $current_blog_id == $main_blog_id ) wp_die('This action is for subsites only.');
			// Remove all existing menus
			self::remove_all_menus_from_site();
			// Copy menus from main site
			switch_to_blog( $main_blog_id );
			$menus = get_terms([
				'taxonomy' => 'nav_menu',
				'hide_empty' => false,
			]);
			$menu_data = [];
			foreach ( $menus as $menu ) {
				$menu_items = wp_get_nav_menu_items( $menu->term_id );
				$menu_data[] = [
					'name' => $menu->name,
					'slug' => $menu->slug,
					'description' => $menu->description,
					'items' => $menu_items,
				];
			}
			$main_locations = get_theme_mod( 'nav_menu_locations' );
			restore_current_blog();
			$menu_id_map = [];
			foreach ( $menu_data as $menu ) {
				$new_menu_id = wp_create_nav_menu( $menu['name'] );
				$menu_id_map[$menu['slug']] = $new_menu_id;
				wp_update_term( $new_menu_id, 'nav_menu', [ 'description' => $menu['description'] ] );
				$item_id_map = [];
				$old_to_new_item_id = [];
				$items = is_array($menu['items']) ? $menu['items'] : [];
				if ( empty($items) ) {
					add_action('admin_notices', function() use ($menu) {
						echo '<div class="notice notice-warning"><strong>Menu "'.esc_html($menu['name']).'" copied, but no menu items found to copy from main site.</strong></div>';
					});
					continue;
				}
				// First pass: insert all items and copy meta
				foreach ( $items as $item ) {
					$args = [
						'menu-item-title' => $item->title,
						'menu-item-type' => $item->type,
						'menu-item-status' => 'publish',
						'menu-item-parent-id' => 0,
						'menu-item-position' => $item->menu_order,
					];
					// Copy URL and replace main domain with current site domain if needed
					if ( isset($item->url) && $item->url ) {
						$main_site_url = network_site_url();
						$current_site_url = site_url();
						$args['menu-item-url'] = str_replace(parse_url($main_site_url, PHP_URL_HOST), parse_url($current_site_url, PHP_URL_HOST), $item->url);
					}
					if ( isset($item->object) ) $args['menu-item-object'] = $item->object;
					if ( isset($item->object_id) ) $args['menu-item-object-id'] = $item->object_id;
					$new_item_id = wp_update_nav_menu_item( $new_menu_id, 0, $args );
					if ( $new_item_id ) {
						$old_to_new_item_id[$item->ID] = $new_item_id;
						// Copy all meta from old item to new item
						$old_meta = get_post_meta( $item->ID );
						foreach ( $old_meta as $meta_key => $meta_values ) {
							foreach ( $meta_values as $meta_value ) {
								// If meta is _menu_item_url, also replace domain
								if ( $meta_key === '_menu_item_url' && $meta_value ) {
									$meta_value = str_replace(parse_url($main_site_url, PHP_URL_HOST), parse_url($current_site_url, PHP_URL_HOST), $meta_value);
								}
								update_post_meta( $new_item_id, $meta_key, maybe_unserialize($meta_value) );
							}
						}
					}
				}
				// Second pass: set parent relationships
				foreach ( $items as $item ) {
					if ( $item->menu_item_parent && isset($old_to_new_item_id[$item->menu_item_parent]) && isset($old_to_new_item_id[$item->ID]) ) {
						update_post_meta( $old_to_new_item_id[$item->ID], '_menu_item_menu_item_parent', $old_to_new_item_id[$item->menu_item_parent] );
					}
				}
				// Third pass: fix object_id and url for post_type/taxonomy items
				foreach ( $items as $item ) {
					if ( !isset($old_to_new_item_id[$item->ID]) ) continue;
					$new_item_id = $old_to_new_item_id[$item->ID];
					if ( $item->type === 'post_type' && isset($item->object_id) ) {
						switch_to_blog( $main_blog_id );
						$main_post = get_post( $item->object_id );
						restore_current_blog();
						if ( $main_post ) {
							$sub_post = get_page_by_path( $main_post->post_name, OBJECT, $item->object );
							if ( $sub_post ) {
								update_post_meta( $new_item_id, '_menu_item_object_id', $sub_post->ID );
								update_post_meta( $new_item_id, '_menu_item_url', get_permalink($sub_post->ID) );
							}
						}
					} elseif ( $item->type === 'taxonomy' && isset($item->object_id) ) {
						switch_to_blog( $main_blog_id );
						$main_term = get_term( $item->object_id, $item->object );
						restore_current_blog();
						if ( $main_term && !is_wp_error($main_term) ) {
							$sub_term = get_term_by( 'slug', $main_term->slug, $item->object );
							if ( $sub_term ) {
								update_post_meta( $new_item_id, '_menu_item_object_id', $sub_term->term_id );
								update_post_meta( $new_item_id, '_menu_item_url', get_term_link($sub_term) );
							}
						}
					}
				}
			}
			// Set menu locations
			$new_locations = [];
			foreach ( (array)$main_locations as $loc => $main_menu_id ) {
				// Find menu slug for this location
				$slug = '';
				foreach ( $menu_data as $menu ) {
					if ( $menu['slug'] && $main_menu_id && $menu['slug'] ) {
						$slug = $menu['slug'];
						break;
					}
				}
				if ( $slug && isset($menu_id_map[$slug]) ) {
					$new_locations[$loc] = $menu_id_map[$slug];
				}
			}
			set_theme_mod( 'nav_menu_locations', $new_locations );
			wp_safe_redirect( admin_url('nav-menus.php?copy_menu_from_main=1') );
			exit;
		}
	}

	// Initialize the class
	new WP_Network_Publisher();
}

/**
 * Sync Redux Framework theme options from the main site to all network subsites.
 * Usage: redux_sync_options_to_network();
 *
 * @param string $option_name The Redux option name (default: 'ag_halacoupon')
 * @return array [ 'success' => int, 'error' => int ]
 */
function redux_sync_options_to_network( $option_name = 'ag_halacoupon' ) {
	if ( ! is_multisite() || ! is_main_site() ) {
		return [ 'success' => 0, 'error' => 0 ];
	}
	$main_options = get_option( $option_name );
	if ( ! $main_options ) {
		return [ 'success' => 0, 'error' => 0 ];
	}
	$sites = get_sites([ 'fields' => 'ids' ]);
	$main_blog_id = get_main_site_id();
	$success = 0;
	$error = 0;
	foreach ( $sites as $site_id ) {
		if ( $site_id == $main_blog_id ) continue;
		try {
			switch_to_blog( $site_id );
			$new_options = $main_options;
			// Remap Redux media fields (set both id and url, handle array/object)
			$media_fields = WP_Network_Publisher::get_redux_media_fields();
			foreach ( $media_fields as $field ) {
				if ( !empty($new_options[$field]) ) {
					$val = $new_options[$field];
					$img_id = is_array($val) && isset($val['id']) ? $val['id'] : (is_numeric($val) ? $val : 0);
					if ( $img_id ) {
						switch_to_blog( $main_blog_id );
						$url = wp_get_attachment_url( $img_id );
						restore_current_blog();
						if ( $url ) {
							$new_attach_id = WP_Network_Publisher::clone_image_from_main( $img_id, $main_blog_id );
							$new_url = wp_get_attachment_url( $new_attach_id );
							$new_options[$field] = [ 'id' => $new_attach_id, 'url' => $new_url ];
						}
					}
				}
			}
			update_option( $option_name, $new_options );
			$success++;
		} catch ( Exception $e ) {
			$error++;
		} finally {
			restore_current_blog();
		}
	}
	return [ 'success' => $success, 'error' => $error ];
}

// Register the hook
add_action( 'wpmu_new_blog', [ 'WP_Network_Publisher', 'clone_main_site_content_to_new_site' ], 20, 1 );
// Register the menu in network admin
add_action( 'network_admin_menu', [ 'WP_Network_Publisher', 'register_network_clone_settings_menu' ] );
// Add a network admin page for clone progress
add_action('network_admin_menu', function() {
	add_menu_page('Site Clone Progress', 'Clone Progress', 'manage_network', 'site-clone-progress', function() {
		?>
		<div class="wrap">
		<h1>Site Clone Progress</h1>
		<div style="width:100%;background:#eee;border-radius:4px;overflow:hidden;height:30px;margin-bottom:10px;">
			<div id="clone-progress-bar" style="width:0;height:30px;background:#0073aa;"></div>
		</div>
		<div id="clone-progress-label">Starting...</div>
		</div>
		<script src="<?php echo get_template_directory_uri(); ?>/inc/clone-progress.js"></script>
		<?php
	});
});

// AJAX endpoint for progress polling
add_action('wp_ajax_get_clone_progress', function() {
	$progress = get_site_transient('site_clone_progress') ?: [ 'percent' => 0, 'message' => 'Starting...', 'done' => false ];
	wp_send_json($progress);
});

// Example: batch process posts (real logic should batch all types)
function process_clone_batch($blog_id, $step = 0) {
	// This is a placeholder for batch logic. You would process a batch of posts/pages/terms here.
	// For demo, we simulate 5 steps.
	$total_steps = 5;
	if ($step >= $total_steps) {
		set_site_transient('site_clone_progress', [ 'percent' => 100, 'message' => 'Clone complete!', 'done' => true ], 60*10);
		return true;
	}
	$percent = intval(($step+1) / $total_steps * 100);
	set_site_transient('site_clone_progress', [ 'percent' => $percent, 'message' => 'Step '.($step+1).' of '.$total_steps, 'done' => false ], 60*10);
	return false;
}

// Add hooks for the new features
add_action('admin_bar_menu', ['WP_Network_Publisher', 'add_copy_menu_from_main_bar'], 200);
add_action('admin_post_copy_menu_from_main', ['WP_Network_Publisher', 'handle_copy_menu_from_main_action']);







function geo_ip_redirection_sypex() {

	if ( ! is_multisite() ) {
		return;
	}

    // Avoid redirection if already on a subdomain
    if ( $_SERVER['HTTP_HOST'] !== 'halacoupon.com' ) {
        return;
    }

    // Include Sypex Geo library
    require_once get_template_directory() . '/geo/SxGeo.php';
    $SxGeo     = new SxGeo( get_template_directory() . '/geo/SxGeo.dat' );
    $user_ip   = $_SERVER['REMOTE_ADDR'];
    $country_code = strtoupper( $SxGeo->getCountry( $user_ip ) );

    // Fetch all active network sites
    $sites = get_sites( array( 'public' => 1, 'archived' => 0, 'deleted' => 0 ) );

    $matched_site_url = '';

    foreach ( $sites as $site ) {
        $domain = $site->domain;
        $path   = $site->path;
        $url    = rtrim( ( is_ssl() ? 'https://' : 'http://' ) . $domain . $path, '/' );

        // Match like EG => egypt.halacoupon.com
        if ( strpos( $domain, strtolower( $country_code ) . '.' ) === 0 ) {
            $matched_site_url = $url;
            break;
        }
    }

    if ( $matched_site_url ) {
        wp_redirect( $matched_site_url, 302 );
        exit;
    }

    // Else: stay on the main site (no redirect)
}
add_action( 'template_redirect', 'geo_ip_redirection_sypex' );