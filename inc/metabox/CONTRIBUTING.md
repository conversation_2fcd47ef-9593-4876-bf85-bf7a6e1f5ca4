# Contributing to CMB2

Thank you for your interest in contributing back to CMB2. Please help us review your issues and/or merge your pull requests by following the below guidelines.

#### NOTE: The issues section is for bug reports and feature requests only.
_Support is not offered for this library, and the likelihood that the maintainers will respond is very low. If you need help, please use [stackoverflow](http://stackoverflow.com/search?q=cmb), or the [wordpress.org plugin forums](http://wordpress.org/support/plugin/cmb2)._

Before reporting a bug
---
1. Search [issues](https://github.com/WebDevStudios/CMB2/issues) to see if the issue has been previously reported.
2. Install the trunk version of CMB2 and test there.


How to report a bug
---
1. Specify the version number for both WordPress and CMB2.
3. Describe the problem in detail. Explain what happened, and what you expected would happen.
4. Provide a small test-case and a link to a [gist](https://gist.github.com/) containing your entire metabox registration code.
5. If helpful, include a screenshot. Annotate the screenshot for clarity.


How to contribute to CMB2
---
All contributions welcome. If you would like to submit a pull request, please follow the steps below.

1. Make sure you have a GitHub account.
2. Fork the repository on GitHub.
3. **Check out the trunk version of CMB2.** If you submit to the master branch, the PR will be closed with a link back to this document.
4. **Verify your issue still exists in the trunk branch.**
5. Make changes to your clone of the repository.
	1. Please follow the [WordPress code standards](https://make.wordpress.org/core/handbook/coding-standards).
	2. If possible, and if applicable, please also add/update unit tests for your changes.
	3. Please add documentation to any new functions, methods, actions and filters.
	4. When committing, reference your issue (if present) and include a note about the fix.
6. [Submit a pull request](https://help.github.com/articles/creating-a-pull-request/).

**Note:** You may gain more ground and avoid unecessary effort if you first open an issue with the proposed changes, but this step is not necessary.

Translations
---
If you are looking to provide language translation files, Please do so via [WP-Translations](http://wp-translations.org/project/cmb2/).

Additional Resources
---

* [CMB2 Documentation Wiki](https://github.com/WebDevStudios/CMB2/wiki)
* [CMB2 Snippet Library](https://github.com/WebDevStudios/CMB2-Snippet-Library)
* [CMB2 API Documentation](http://cmb2.io/api/)
* [General GitHub Documentation](http://help.github.com/)
* [GitHub Pull Request documentation](http://help.github.com/send-pull-requests/)
* [PHPUnit Tests Guide](http://phpunit.de/manual/current/en/writing-tests-for-phpunit.html)