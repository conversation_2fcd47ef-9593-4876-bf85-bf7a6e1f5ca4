<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */




function halacoupon_is_single_enable() {
	return false ;
}

function halacoupon_maybe_show_coupon_thumb( $type = '' ) {

		$has_thumb = has_post_thumbnail( halacoupon_coupon()->ID );
		$has_thumb = halacoupon_coupon()->has_thumb();
	

	return $has_thumb;
}


/**
 * Get our wp_nav_menu() fallback, wp_page_menu(), to show a home link.
 *
 * @param array $args Configuration arguments.
 * @return array
 */
function halacoupon_page_menu_args( $args ) {
	$args['show_home'] = true;
	return $args;
}
add_filter( 'wp_page_menu_args', 'halacoupon_page_menu_args' );


function halacoupon_browser_body_class($classes) {
    if (empty($_SERVER['HTTP_USER_AGENT'])) {
        return $classes;
    }

    $user_agent = strtolower(sanitize_text_field($_SERVER['HTTP_USER_AGENT']));

    if (wp_is_mobile()) {
        $classes[] = 'mobile';
    }
    if (strpos($user_agent, 'firefox') !== false) {
        $classes[] = 'firefox';
    } elseif (strpos($user_agent, 'opera') !== false || strpos($user_agent, 'opr/') !== false) {
        $classes[] = 'opera';
    } elseif (strpos($user_agent, 'chrome') !== false && strpos($user_agent, 'safari') !== false && strpos($user_agent, 'edge') === false) {
        // Make sure to exclude Edge which also contains 'chrome' and 'safari' in its user agent
        $classes[] = 'chrome';
    } elseif (strpos($user_agent, 'safari') !== false && strpos($user_agent, 'chrome') === false) {
        // Ensure it's not Chrome (which also contains 'safari' in its user agent string)
        $classes[] = 'safari';
    } elseif (strpos($user_agent, 'edge') !== false || strpos($user_agent, 'edg/') !== false) {
        $classes[] = 'edge';
    } elseif (strpos($user_agent, 'trident') !== false || strpos($user_agent, 'msie') !== false) {
        $classes[] = 'ie';
    } else {
        $classes[] = 'unknown-browser';
    }

    if (strpos($user_agent, 'mac') !== false) {
        $classes[] = 'osx';
    } elseif (strpos($user_agent, 'linux') !== false) {
        $classes[] = 'linux';
    } elseif (strpos($user_agent, 'windows') !== false) {
        $classes[] = 'windows';
    }

    return $classes;
}
add_filter('body_class', 'halacoupon_browser_body_class');


function halacoupon_set_html_content_type() {
	return 'text/html';
}




function halacoupon_add_opengraph_doctype($output) {
    // Avoid adding Open Graph namespaces if an SEO plugin (like Yoast or Rank Math) is active
    if (defined('WPSEO_VERSION') || defined('RANK_MATH_VERSION')) {
        return $output;
    }

    if (is_singular()) {
        if (strpos($output, 'xmlns:og') === false) {
            $output .= ' xmlns:og="https://ogp.me/ns#"';
        }
        if (strpos($output, 'xmlns:fb') === false) {
            $output .= ' xmlns:fb="http://www.facebook.com/2008/fbml"';
        }
        if (strpos($output, 'xmlns:twitter') === false) {
            $output .= ' xmlns:twitter="https://twitter.com/namespace"';
        }
    }

    return esc_attr(trim($output));
}
add_filter('language_attributes', 'halacoupon_add_opengraph_doctype');



add_filter('language_attributes', function($output) {
    return str_replace('&quot;', '"', $output); // Prevent double-encoding.
});

/**
 * Remove OG Image If have from plugin Yoat SEO
 */
function halacoupon_remove_og_img() {
	if ( is_tax( 'coupon_store' ) ) {
		add_filter( 'wpseo_og_og:image', '__return_false' );
		add_filter( 'wpseo_og_og_image', '__return_false' );
	}
}
add_action( 'wp', 'halacoupon_remove_og_img', 97 );


function halacoupon_toggle_content_more( $content ) {
	$more_content = false;
	$contents = explode( '<!--more-->', $content );
	if ( count( $contents ) > 1 ) {
		$html = apply_filters( 'the_content', $contents[0] );
		unset( $contents[0] );
		$more_content = apply_filters( 'the_content', join( " \n\r ", $contents ) );
	} else {
		$html = apply_filters( 'the_content', $content );
	}

	if ( $more_content ) {
		$html = '<div class="content-toggle"><div class="content-less">' . $html . '</div>';
		$html .= '<div class="content-more">' . $more_content . '</div>';
		$html .= '<a class="show-more" href="#">' . esc_html__( 'Read more', 'halacoupon' ) . '</a></div>';
	}

	return $html;

}





