<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */




function halacoupon_render_newsletter_form() {
    ?>
    <form id="newsletter-form" class="space-y-4">
        <?php wp_nonce_field('newsletter_subscription_action', 'newsletter_nonce'); ?>
        <div class="flex flex-col gap-4">
            <!-- Email Input -->
            <div class="flex-1 relative">
                <div class="absolute inset-y-0 left-4 pl-3 flex items-center pointer-events-none">
                    <svg class="w-5 h-5 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                    </svg>
                </div>
                <input type="email"
                    name="email"
                    placeholder="<?php esc_attr_e('Enter your email address', 'halacoupon'); ?>"
                    required
                    class="w-full pl-10 px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/50 transition-all duration-200">
            </div>

            <!-- Submit Button -->
            <button type="submit"
                class="btn-primary px-8 py-3 bg-white text-primary-600 font-semibold rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-white/50 transition-all duration-200 whitespace-nowrap">
                <?php esc_html_e('Subscribe', 'halacoupon'); ?>
            </button>
        </div>
    </form>
    <div id="newsletter-message" class="mt-4"></div>

    <script type="text/javascript">
    (function($) {
        $('#newsletter-form').on('submit', function(e) {
            e.preventDefault();

            var $form = $(this);
            var $button = $form.find('button[type="submit"]');
            var $message = $('#newsletter-message');
            var originalButtonText = $button.text();

            // Show loading state
            $button.prop('disabled', true).html('<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-current inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><?php esc_html_e('Subscribing...', 'halacoupon'); ?>');

            var formData = $form.serialize();
            formData += '&action=halacoupon_newsletter_subscription';

            $.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        $message.html('<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">' + response.data.message + '</div>');
                        $form[0].reset();
                    } else {
                        $message.html('<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">' + response.data.message + '</div>');
                    }
                },
                error: function() {
                    $message.html('<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg"><?php esc_html_e('There was an error. Please try again.', 'halacoupon'); ?></div>');
                },
                complete: function() {
                    // Reset button state
                    $button.prop('disabled', false).text(originalButtonText);
                }
            });
        });
    })(jQuery);
    </script>
    <?php
}




function halacoupon_handle_newsletter_subscription() {
    // Check nonce for security
    check_ajax_referer('newsletter_subscription_action', 'newsletter_nonce');

    // Check if email is present and valid
    if (isset($_POST['email']) && is_email($_POST['email'])) {
        $email = sanitize_email($_POST['email']);
    } else {
        wp_send_json_error(array('message' => __('Invalid email address.', 'halacoupon')));
    }

    // Get Mailchimp API key and list ID from Redux settings
    $api_key = halacoupon_get_option('newsletter_api_key');
    $list_id = halacoupon_get_option('newsletter_list_id'); ;

    // Exit if API key or list ID is missing
    if (!$api_key || !$list_id) {
        wp_send_json_error(array('message' => __('Mailchimp configuration missing.', 'halacoupon')));
    }

    // Mailchimp API URL
    $data_center = substr($api_key, strpos($api_key, '-') + 1); // Extract data center from API key
    $url = 'https://' . $data_center . '.api.mailchimp.com/3.0/lists/' . $list_id . '/members/';

    // Prepare the data for Mailchimp
    $data = json_encode(array(
        'email_address' => $email,
        'status'        => 'subscribed'
    ));

    // Set up cURL for the request
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_USERPWD, 'user:' . $api_key);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    // Handle the response
    if ($http_code == 200) {
        wp_send_json_success(array('message' => __('Subscription successful!', 'halacoupon')));
    } else {
        wp_send_json_error(array('message' => __('There was a problem with the subscription. Please try again.', 'halacoupon')));
    }
}
add_action('wp_ajax_halacoupon_newsletter_subscription', 'halacoupon_handle_newsletter_subscription');
add_action('wp_ajax_nopriv_halacoupon_newsletter_subscription', 'halacoupon_handle_newsletter_subscription');