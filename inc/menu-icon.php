<?php

// Add custom field for menu icon with WordPress media uploader
function add_menu_icon_field($item_id, $item, $depth, $args) {
    // Only allow admins to upload menu icons
    if (!current_user_can('administrator')) {
        return;
    }

    // Get the saved icon URL
    $icon = get_post_meta($item_id, '_menu_item_icon', true);
    ?>
    <p class="description description-wide">
        <label for="edit-menu-item-icon-<?php echo esc_attr($item_id); ?>">
            <?php esc_html_e('Menu Icon (64x64px)', 'halacoupon'); ?><br />
            <input type="text" id="edit-menu-item-icon-<?php echo esc_attr($item_id); ?>" class="widefat edit-menu-item-icon" name="menu-item-icon[<?php echo esc_attr($item_id); ?>]" value="<?php echo esc_attr($icon); ?>" readonly />
            <button type="button" class="button upload-icon-button" data-item-id="<?php echo esc_attr($item_id); ?>"><?php esc_html_e('Upload Icon', 'halacoupon'); ?></button>
        </label>
        <span class="description"><?php esc_html_e('Recommended image size: 64x64 px.', 'halacoupon'); ?></span>
    </p>

    <!-- Display Preview if Icon Exists -->
    <p class="menu-icon-preview">
        <?php 
        if ($icon) {
            $file_type = pathinfo($icon, PATHINFO_EXTENSION);
            if (strtolower($file_type) === 'svg') {
                // Display inline SVG for preview in admin
                echo '<div class="svg-icon">' . file_get_contents($icon) . '</div>';
            } else {
                // Display image for PNG/JPG/JPEG preview in admin
                echo '<img src="' . esc_url($icon) . '" alt="" style="max-width: 50px; height: 50px;" class="menu-icon-preview-image">';
            }
        }
        ?>
    </p>
    <?php
}
add_action('wp_nav_menu_item_custom_fields', 'add_menu_icon_field', 10, 4);

// Save the custom field when the menu item is saved
function save_menu_icon_field($menu_id, $menu_item_db_id, $args) {
    if (isset($_POST['menu-item-icon'][$menu_item_db_id])) {
        $icon = sanitize_text_field($_POST['menu-item-icon'][$menu_item_db_id]);

        // Only allow SVG, PNG, JPG, JPEG formats
        $allowed_file_types = array('svg', 'png', 'jpg', 'jpeg');
        $file_type = pathinfo($icon, PATHINFO_EXTENSION);

        if (in_array(strtolower($file_type), $allowed_file_types)) {
            // If SVG, we directly save the URL
            update_post_meta($menu_item_db_id, '_menu_item_icon', $icon);
        } else {
            delete_post_meta($menu_item_db_id, '_menu_item_icon');
        }
    } else {
        delete_post_meta($menu_item_db_id, '_menu_item_icon');
    }
}
add_action('wp_update_nav_menu_item', 'save_menu_icon_field', 10, 3);

// Enqueue the media uploader and custom JavaScript on the nav menus page
function enqueue_menu_icon_uploader_script($hook) {
    if ($hook != 'nav-menus.php') {
        return;
    }

    // Enqueue the WordPress media uploader and custom JS
    wp_enqueue_media();
    wp_enqueue_script('menu-icon-uploader', get_template_directory_uri() . '/assets/js/admin-scripts.js', array('jquery'), null, true);
}
add_action('admin_enqueue_scripts', 'enqueue_menu_icon_uploader_script');

// Display the icon in the menu (SVG inline or regular image)
function display_menu_icon($title, $item, $args, $depth) {
    $icon = get_post_meta($item->ID, '_menu_item_icon', true);

    if ($icon) {
        $file_type = pathinfo($icon, PATHINFO_EXTENSION);

        if (strtolower($file_type) === 'svg') {
            // Output inline SVG for front-end display
            $svg = file_get_contents($icon);
            $title = '<span class="menu-icon svg-icon">' . $svg . '</span>' . $title;
        } else {
            // Output regular image for PNG/JPG/JPEG
            $alt_text = !empty($title) ? esc_attr(strip_tags($title)) : '';
            $icon_html = '<img src="' . esc_url($icon) . '" alt="' . $alt_text . '" class="menu-icon" style="margin-right: 5px; max-width: 64px; height: 64px;" />';
            $title = $icon_html . $title;
        }
    }

    return $title;
}
add_filter('nav_menu_item_title', 'display_menu_icon', 10, 4);