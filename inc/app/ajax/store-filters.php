<?php
/**
 * AJAX Store Filters
 * 
 * AJAX handlers for filtering stores by character
 * 
 * @package HalaCoupon
 * @subpackage App\Ajax
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Handle AJAX request for filtering stores by character
 */
function filter_stores_by_character() {
    // Get the character passed from the AJAX request
    $character = isset($_POST['character']) ? sanitize_text_field($_POST['character']) : 'all';

    // Prepare arguments to get stores based on the character
    $args = array(
        'orderby'    => 'name',
        'order'      => 'ASC',
        'hide_empty' => false,
    );

    // Filter by first letter (exact match only) if a specific character is selected
    if ($character !== 'all') {
        if ($character === '0-9') {
            // Handle numeric values
            $args['name__like'] = '[0-9]';
        } else {
            // Ensure only stores that start with the specific letter are fetched
            $args['name__like'] = $character;
        }
    }

    $stores = get_terms('coupon_store', $args);

    if (!empty($stores) && !is_wp_error($stores)) {
        foreach ($stores as $store) {
            $first_char = strtoupper(mb_substr($store->name, 0, 1));
            if ($character === 'all' || ($character === '0-9' && is_numeric($first_char)) || $first_char === strtoupper($character)) {
                $store_image = get_term_meta($store->term_id, '_ags_store_image', true);
                $store_coupons = halacoupon_get_coupon_counts_by_type($store->term_id, 'coupon_store');
                $total_coupons = $store_coupons['all'] ?? 0;
                ?>
		<div class="store-card ui card">
			<div class="ui slide masked reveal ">
			    <div class="visible content">
     				<div class="store-logo custom-store-logo"
     					style="background-image: url('<?php echo esc_url($store_image); ?>');">
     					<a href="<?php echo get_term_link($store, 'coupon_store'); ?>">
     						<span class="visually-hidden"><?php echo esc_html($store->name); ?></span>
     					</a>
     				</div>
     			</div>
     			<div class="hidden content revealed_coupon_count">
     				<?php echo $total_coupons; ?> <?php esc_html_e('Coupons', 'halacoupon'); ?>
     			</div>
			</div>
			
			<div class="char_list_store_header">
				<div class="store-title">
					<a
						href="<?php echo get_term_link($store, 'coupon_store'); ?>"><?php echo esc_html($store->name); ?></a>
				</div>

				<div class="store-back-icon">
				<a href="<?php echo get_term_link($store, 'coupon_store'); ?>"><i class="angle left icon"></i></a>
					
			    </div>

			</div>
		</div>
		<?php
            }
        }
    } else {
        echo '<p>No stores found.</p>';
    }

    wp_die(); // This is required to terminate the AJAX request properly
}

// AJAX handlers disabled - now using CSS-only filtering for better performance
// add_action('wp_ajax_filter_stores_by_character', 'filter_stores_by_character');
// add_action('wp_ajax_nopriv_filter_stores_by_character', 'filter_stores_by_character');

// Mark this module as loaded
add_filter('halacoupon_module_store_filters_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'ajax/store-filters';
    return $modules;
});
