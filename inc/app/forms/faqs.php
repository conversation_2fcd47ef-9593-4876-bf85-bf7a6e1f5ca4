<?php
/**
 * FAQs Display
 * 
 * FAQ functionality for store pages
 * 
 * @package HalaCoupon
 * @subpackage App\Forms
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display FAQs for coupon store pages
 */
function display_coupon_store_faqs() {
    if (is_tax('coupon_store')) {
        $term_id = get_queried_object_id();
        $prefix = '_ags_';
        $faqs = get_term_meta($term_id, $prefix . 'faq_group', true);
        if (!empty($faqs) && is_array($faqs)) {
            $faq_items = array();
            $faq_schema = array(
                "@context" => "https://schema.org",
                "@type"    => "FAQPage",
                "mainEntity" => array(),
            );
            $has_content = false;
            $is_rtl = is_rtl();
            $dir = $is_rtl ? 'rtl' : 'ltr';
            $text_dir = $is_rtl ? 'text-right' : 'text-left';
            $icon_dir = $is_rtl ? 'ml-2' : 'mr-2';
            $flex_dir = $is_rtl ? 'flex-row-reverse' : 'flex-row';
            foreach ($faqs as $index => $faq) {
                $question = isset($faq['question']) ? trim(wp_strip_all_tags($faq['question'])) : '';
                $answer = isset($faq['answer']) ? trim($faq['answer']) : '';
                if ($question && $answer) {
                    $has_content = true;
                    $faq_id = 'faq-' . $term_id . '-' . $index;
                    $faq_items[] = array(
                        'id' => $faq_id,
                        'question' => $question,
                        'answer' => $answer,
                    );
                    $faq_schema['mainEntity'][] = array(
                        "@type" => "Question",
                        "name"  => $question,
                        "acceptedAnswer" => array(
                            "@type" => "Answer",
                            "text"  => wp_strip_all_tags($answer),
                        ),
                    );
                }
            }
            if (!$has_content) return;
            echo '<section class="faq-section-modern mx-auto my-10" dir="' . esc_attr($dir) . '" role="region" aria-label="' . esc_attr__('Frequently Asked Questions', 'halacoupon') . '">';
            echo '<h2 class="text-2xl md:text-3xl font-bold mb-6">' . esc_html__('Frequently Asked Questions', 'halacoupon') . '</h2>';
            echo '<div class="flex flex-col gap-3">';
            foreach ($faq_items as $i => $item) {
                $faq_id = esc_attr($item['id']);
                $is_first = $i === 0;
                echo '<div class="faq-card bg-white/80 glass-card border border-white/20 rounded-xl shadow-sm">';
                echo '<button type="button" class="faq-toggle flex ' . $flex_dir . ' items-center w-full px-5 py-4 ' . $text_dir . ' font-semibold text-lg focus:outline-none focus:ring-2 focus:ring-primary/40 transition-colors duration-200 group" aria-expanded="' . ($is_first ? 'true' : 'false') . '" aria-controls="' . $faq_id . '" id="' . $faq_id . '-label">';
                // Icon
                echo '<span class="inline-flex items-center justify-center w-7 h-7 rounded-full bg-primary/10 text-primary ' . $icon_dir . ' transition-transform duration-300 faq-icon" style="transform:rotate(' . ($is_first ? '45' : '0') . 'deg)">';
                echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 5v14m7-7H5"/></svg>';
                echo '</span>';
                // Question
                echo '<span class="flex-1 text-base md:text-lg">' . esc_html($item['question']) . '</span>';
                echo '</button>';
                // Answer
                echo '<div id="' . $faq_id . '" class="faq-answer px-5 pb-5 text-gray-700 text-base leading-relaxed overflow-hidden" style="max-height:' . ($is_first ? '500px' : '0') . ';opacity:' . ($is_first ? '1' : '0') . ';transition:max-height 0.5s cubic-bezier(.4,0,.2,1),opacity 0.4s;" role="region" aria-labelledby="' . $faq_id . '-label">' . wp_kses_post($item['answer']) . '</div>';
                echo '</div>';
            }
            echo '</div>';
            echo '</section>';
            // Accordion JS with robust animation and RTL/LTR support
            echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                var toggles = document.querySelectorAll(".faq-toggle");
                toggles.forEach(function(btn) {
                    btn.addEventListener("click", function() {
                        var expanded = this.getAttribute("aria-expanded") === "true";
                        var answer = this.parentNode.querySelector(".faq-answer");
                        var icon = this.querySelector(".faq-icon");
                        // Close all
                        toggles.forEach(function(b) {
                            b.setAttribute("aria-expanded", "false");
                            var a = b.parentNode.querySelector(".faq-answer");
                            var i = b.querySelector(".faq-icon");
                            if (a) {
                                a.style.maxHeight = 0;
                                a.style.opacity = 0;
                                a.style.pointerEvents = "none";
                            }
                            if (i) {
                                i.style.transform = "rotate(0deg)";
                            }
                        });
                        // Open this one if not already open
                        if (!expanded) {
                            this.setAttribute("aria-expanded", "true");
                            answer.style.maxHeight = answer.scrollHeight + 20 + "px";
                            answer.style.opacity = 1;
                            answer.style.pointerEvents = "auto";
                            if (icon) {
                                icon.style.transform = "rotate(45deg)";
                            }
                        }
                    });
                });
            });
            </script>';
            if (!empty($faq_schema['mainEntity'])) {
                echo '<script type="application/ld+json">' . wp_json_encode($faq_schema) . '</script>';
            }
        }
    }
}

// Mark this module as loaded
add_filter('halacoupon_module_faqs_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'forms/faqs';
    return $modules;
});
