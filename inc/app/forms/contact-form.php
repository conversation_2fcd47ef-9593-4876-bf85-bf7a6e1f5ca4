<?php
/**
 * Contact Form
 * 
 * Contact form functionality
 * 
 * @package HalaCoupon
 * @subpackage App\Forms
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display and handle contact form
 */
function halacoupon_contact_form() {
    // Get the email address from the Redux options
    $contact_email = halacoupon_get_option('contact_form_email'); // Adjust 'redux_option_name' to match your Redux option name

    // Check if the form is submitted and nonce is verified
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['submit_contact_form']) && isset($_POST['contact_form_nonce']) && wp_verify_nonce($_POST['contact_form_nonce'], 'submit_contact_form')) {
        // Sanitize input
        $full_name = sanitize_text_field($_POST['full_name']);
        $email = sanitize_email($_POST['email']);
        $message_content = sanitize_textarea_field($_POST['message_content']);
        
        // Validate the input
        if (!empty($full_name) && !empty($email) && !empty($message_content) && is_email($email)) {
            // Prepare email
            $subject = 'New Contact Form Submission';
            $message = "Name: $full_name\n\nEmail: $email\n\nMessage:\n$message_content";
            $headers = 'From: ' . sanitize_email($email) . "\r\n";

            // Send the email
            if (wp_mail(sanitize_email($contact_email), sanitize_text_field($subject), $message, $headers)) {
                // Display a success message
                echo '<div class="ui positive message">' . esc_html__('Thank you for your message! We will get back to you soon.', 'halacoupon') . '</div>';
            } else {
                // Email sending failed
                echo '<div class="ui negative message">' . esc_html__('There was an issue sending your message. Please try again.', 'halacoupon') . '</div>';
            }
        } else {
            // Validation failed
            echo '<div class="ui negative message">' . esc_html__('Please fill in all the fields correctly.', 'halacoupon') . '</div>';
        }
    }

    // Display the form
    ?>
    <form class="ui form contact-form" method="post" action="">
        <?php wp_nonce_field('submit_contact_form', 'contact_form_nonce'); ?>
        <div class="field full_name">
            <input type="text" name="full_name" placeholder="<?php esc_attr_e('Full name', 'halacoupon'); ?>" required>
        </div>
        <div class="field email">
            <input type="email" name="email" placeholder="<?php esc_attr_e('Email', 'halacoupon'); ?>" required>
        </div>
        <div class="field message_content">
            <textarea name="message_content" placeholder="<?php esc_attr_e('Message content', 'halacoupon'); ?>" required></textarea>
        </div>
        <button class="ui button btn btn_primary submit_contact_form" type="submit" name="submit_contact_form"><?php esc_html_e('Send', 'halacoupon'); ?></button>
    </form>
    <?php
}

// Mark this module as loaded
add_filter('halacoupon_module_contact_form_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'forms/contact-form';
    return $modules;
});
