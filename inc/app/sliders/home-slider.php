<?php
/**
 * Home Page Slider - Professional Refactored Version
 *
 * Modern, responsive slider using Splide.js with complete metabox integration,
 * glass morphism design, brand colors, and accessibility features.
 *
 * @package HalaCoupon
 * @subpackage App\Sliders
 * @version 3.0.0 - Complete Refactor
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display professional home page slider with complete metabox integration
 *
 * Features:
 * - Complete metabox field integration
 * - Glass morphism design with brand colors
 * - Responsive design with RTL/LTR support
 * - Accessibility features and keyboard navigation
 * - Performance optimized with lazy loading
 * - Modern Splide.js implementation
 *
 * @return void
 */
if (!function_exists('display_home_slider')) {
    function display_home_slider() {
        // Query slides with optimized parameters
        $slides_query = new WP_Query([
            'post_type'      => 'slides',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
            'orderby'        => 'menu_order',
            'order'          => 'ASC',
            'meta_query'     => [
                'relation' => 'OR',
                [
                    'key'     => '_ags_slide_headline_text',
                    'value'   => '',
                    'compare' => '!='
                ],
                [
                    'key'     => '_ags_slide_button_text',
                    'value'   => '',
                    'compare' => '!='
                ]
            ]
        ]);

        // Early return if no slides
        if (!$slides_query->have_posts()) {
            return;
        }

        // Enqueue required assets
        halacoupon_enqueue_splide_assets();

        // Get RTL direction
        $is_rtl = is_rtl();
        $direction = $is_rtl ? 'rtl' : 'ltr';

        // Generate unique slider ID for multiple sliders support
        $slider_id = 'home-slider-' . uniqid();

        ?>
        <section class="home-slider-section relative mb-8 lg:mb-12"
                 role="region"
                 aria-label="<?php esc_attr_e('Featured Content Slider', 'halacoupon'); ?>"
                 dir="<?php echo esc_attr($direction); ?>">

            <div class="relative overflow-hidden rounded-xl">

                <div class="splide home-slider"
                     id="<?php echo esc_attr($slider_id); ?>"
                     data-splide='<?php echo esc_attr(wp_json_encode(halacoupon_get_home_slider_config())); ?>'
                     role="group"
                     aria-roledescription="<?php esc_attr_e('Carousel', 'halacoupon'); ?>">

                    <div class="splide__track" role="presentation">
                        <ul class="splide__list" role="presentation">

                            <?php
                            $slide_index = 0;
                            while ($slides_query->have_posts()) :
                                $slides_query->the_post();
                                $slide_index++;

                                // Get metabox data with proper fallbacks
                                $slide_data = halacoupon_get_slide_meta_data(get_the_ID());

                                // Skip slide if no essential content
                                if (empty($slide_data['headline']) && empty($slide_data['content']) && empty($slide_data['image'])) {
                                    continue;
                                }

                                halacoupon_render_slide_item($slide_data, $slide_index, $is_rtl);

                            endwhile;
                            ?>

                        </ul>
                    </div>

                    <!-- Navigation and pagination will be auto-generated by Splide -->

                </div>
            </div>
        </section>

        <?php
        // Initialize slider with configuration
        halacoupon_initialize_home_slider($slider_id);

        // Reset post data
        wp_reset_postdata();
    }
}

/**
 * Get slide meta data with proper fallbacks and sanitization
 *
 * @param int $slide_id The slide post ID
 * @return array Sanitized slide data
 */
if (!function_exists('halacoupon_get_slide_meta_data')) {
    function halacoupon_get_slide_meta_data($slide_id) {
        // Get metabox data
        $headline_text = get_post_meta($slide_id, '_ags_slide_headline_text', true);
        $button_text = get_post_meta($slide_id, '_ags_slide_button_text', true);
        $button_url = get_post_meta($slide_id, '_ags_slide_button_url', true);

        // Get post data
        $post_title = get_the_title($slide_id);
        $post_content = get_the_content(null, false, $slide_id);
        $featured_image = get_the_post_thumbnail_url($slide_id, 'full');

        // Prepare data with fallbacks
        return [
            'id' => $slide_id,
            'headline' => !empty($headline_text) ? $headline_text : $post_title,
            'content' => !empty($post_content) ? wp_kses_post($post_content) : '',
            'button_text' => !empty($button_text) ? $button_text : __('Learn More', 'halacoupon'),
            'button_url' => !empty($button_url) ? esc_url($button_url) : '',
            'image' => $featured_image ? esc_url($featured_image) : '',
            'image_alt' => get_post_meta(get_post_thumbnail_id($slide_id), '_wp_attachment_image_alt', true),
            'title' => $post_title // Keep original title for accessibility
        ];
    }
}

/**
 * Render individual slide item with accessibility and responsive design
 *
 * @param array $slide_data Slide data from halacoupon_get_slide_meta_data()
 * @param int $slide_index Current slide index for accessibility
 * @param bool $is_rtl Whether RTL is enabled
 * @return void
 */
if (!function_exists('halacoupon_render_slide_item')) {
    function halacoupon_render_slide_item($slide_data, $slide_index, $is_rtl = false) {
        $has_button = !empty($slide_data['button_url']);
        $image_alt = !empty($slide_data['image_alt']) ? $slide_data['image_alt'] : $slide_data['headline'];
        ?>

        <li class="splide__slide"
            role="tabpanel"
            aria-roledescription="<?php esc_attr_e('slide', 'halacoupon'); ?>"
            aria-label="<?php echo esc_attr(sprintf(__('Slide %d of %d', 'halacoupon'), $slide_index, wp_count_posts('slides')->publish)); ?>">

            <article class="relative h-80 sm:h-96 lg:h-[505px] overflow-hidden rounded-lg lg:rounded-xl group">

                <!-- Background Image with Lazy Loading -->
                <?php if (!empty($slide_data['image'])) : ?>
                <div class="absolute inset-0 bg-gray-200">
                    <img src="<?php echo esc_url($slide_data['image']); ?>"
                         alt="<?php echo esc_attr($image_alt); ?>"
                         class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-700 ease-out"
                         loading="<?php echo $slide_index === 1 ? 'eager' : 'lazy'; ?>"
                         decoding="async">
                </div>
                <?php else : ?>
                <!-- Fallback gradient background -->
                <div class="absolute inset-0 bg-gradient-to-br from-primary via-secondary to-accent"></div>
                <?php endif; ?>

                <!-- Glass Morphism Overlay -->
                <div class="absolute inset-0 bg-gradient-to-<?php echo $is_rtl ? 'l' : 'r'; ?> from-black/70 via-black/40 to-transparent backdrop-blur-[1px]"></div>

                <!-- Content Container -->
                <div class="relative h-full flex items-center">
                    <div class="max-w-7xl mx-auto px-4 sm:px-10 lg:px-20 w-full">
                        <div class="max-w-2xl text-<?php echo $is_rtl ? 'right' : 'left'; ?>">

                            <!-- Headline -->
                            <?php if (!empty($slide_data['headline'])) : ?>
                            <h2 class="text-2xl sm:text-4xl  font-bold text-white mb-4 lg:mb-6 leading-tight animate-fade-in-up">
                                <?php echo esc_html($slide_data['headline']); ?>
                            </h2>
                            <?php endif; ?>

                            <!-- Content -->
                            <?php if (!empty($slide_data['content'])) : ?>
                            <div class="text-white/90 text-base sm:text-lg lg:text-xl leading-relaxed mb-6 lg:mb-8 animate-fade-in-up max-w-xl"
                                 style="animation-delay: 0.2s;">
                                <?php echo $slide_data['content']; ?>
                            </div>
                            <?php endif; ?>

                            <!-- Call to Action Button -->
                            <?php if ($has_button) : ?>
                            <div class="animate-fade-in-up" style="animation-delay: 0.4s;">
                                <a href="<?php echo esc_url($slide_data['button_url']); ?>"
                                   class="group/btn inline-flex items-center px-6 lg:px-8 py-3 lg:py-4 bg-white/95 backdrop-blur-sm text-primary font-bold text-sm lg:text-lg rounded-lg lg:rounded-xl shadow-float hover:shadow-float-lg transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-white/50"
                                   target="_blank"
                                   rel="noopener noreferrer"
                                   aria-label="<?php echo esc_attr($slide_data['button_text'] . ' - ' . __('Opens in new tab', 'halacoupon')); ?>">

                                    <span class="relative z-10 flex items-center">
									<?php echo esc_html($slide_data['button_text']); ?>
                                        <svg class="w-4 h-4 lg:w-5 lg:h-5 <?php echo $is_rtl ? 'ml-2 lg:ml-3 group-hover/btn:-translate-x-1' : 'mr-2 lg:mr-3 group-hover/btn:translate-x-1'; ?> transition-transform duration-300"
                                             fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="<?php echo $is_rtl ? 'M11 17l-5-5m0 0l5-5m-5 5h12' : 'M13 7l5 5m0 0l-5 5m5-5H6'; ?>"></path>
                                        </svg>
                                    </span>

                                    <!-- Hover Effect Overlay -->
                                    <div class="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300 rounded-lg lg:rounded-xl"></div>
                                </a>
                            </div>
                            <?php endif; ?>

                        </div>
                    </div>
                </div>

                <!-- Full Slide Link (if no button) -->
                <?php if (!$has_button && !empty($slide_data['button_url'])) : ?>
                <a href="<?php echo esc_url($slide_data['button_url']); ?>"
                   class="absolute inset-0 z-10"
                   target="_blank"
                   rel="noopener noreferrer"
                   aria-label="<?php echo esc_attr($slide_data['title'] ?: __('View slide content', 'halacoupon')); ?>">
                </a>
                <?php endif; ?>

            </article>
        </li>

        <?php
    }
}

/**
 * Get optimized Splide configuration for home slider
 *
 * @return array Splide configuration array
 */
if (!function_exists('halacoupon_get_home_slider_config')) {
    function halacoupon_get_home_slider_config() {
        $is_rtl = is_rtl();

        return [
            'type' => 'loop',
            'direction' => $is_rtl ? 'rtl' : 'ltr',
            'autoplay' => true,
            'interval' => 7000,
            'speed' => 1000,
            'easing' => 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            'pauseOnHover' => true,
            'pauseOnFocus' => true,
            'resetProgress' => false,
            'arrows' => true,
            'pagination' => true,
            'drag' => true,
            'keyboard' => 'global',
            'wheel' => false,
            'waitForTransition' => true,
            'trimSpace' => false,
            'focus' => 'center',
            'updateOnMove' => true,
            'throttle' => 100,
            'lazyLoad' => 'nearby',
            'preloadPages' => 1,
            'accessibility' => [
                'autoplay' => true,
                'rewind' => true,
                'slideFocus' => true
            ],
            'breakpoints' => [
                1024 => [
                    'arrows' => true,
                    'pagination' => true
                ],
                768 => [
                    'arrows' => false,
                    'pagination' => true,
                    'drag' => true
                ],
                480 => [
                    'arrows' => false,
                    'pagination' => true,
                    'drag' => true,
                    'autoplay' => true,
                    'interval' => 5000
                ]
            ],
            'classes' => [
                'arrows' => 'splide__arrows splide__arrows--modern',
                'arrow' => 'splide__arrow splide__arrow--modern',
                'prev' => 'splide__arrow--prev',
                'next' => 'splide__arrow--next',
                'pagination' => 'splide__pagination splide__pagination--modern',
                'page' => 'splide__pagination__page splide__pagination__page--modern'
            ],
            'i18n' => [
                'prev' => __('Previous slide', 'halacoupon'),
                'next' => __('Next slide', 'halacoupon'),
                'first' => __('Go to first slide', 'halacoupon'),
                'last' => __('Go to last slide', 'halacoupon'),
                'slideX' => __('Go to slide %s', 'halacoupon'),
                'pageX' => __('Go to page %s', 'halacoupon'),
                'play' => __('Start autoplay', 'halacoupon'),
                'pause' => __('Pause autoplay', 'halacoupon'),
                'carousel' => __('carousel', 'halacoupon'),
                'slide' => __('slide', 'halacoupon'),
                'select' => __('Select a slide to show', 'halacoupon')
            ]
        ];
    }
}

/**
 * Initialize home slider with enhanced error handling and performance optimization
 *
 * @param string $slider_id Unique slider ID
 * @return void
 */
if (!function_exists('halacoupon_initialize_home_slider')) {
    function halacoupon_initialize_home_slider($slider_id) {
        $config = halacoupon_get_home_slider_config();
        $config_json = wp_json_encode($config, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        ?>
        <script>
        (function() {
            'use strict';

            /**
             * Customize arrow icons with enhanced SVG and RTL/LTR support
             */
            function customizeArrowIcons(sliderElement) {
                const isRTL = document.documentElement.dir === 'rtl' || document.body.classList.contains('rtl');
                const prevArrow = sliderElement.querySelector('.splide__arrow--prev');
                const nextArrow = sliderElement.querySelector('.splide__arrow--next');

                // Enhanced arrow icons with better visual design
                const leftArrowIcon = `
                    <svg class="w-5 h-5 transition-transform duration-200 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M15 19l-7-7 7-7"></path>
                    </svg>
                `;

                const rightArrowIcon = `
                    <svg class="w-5 h-5 transition-transform duration-200 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M9 5l7 7-7 7"></path>
                    </svg>
                `;

                if (prevArrow) {
                    // In RTL: prev = right arrow, in LTR: prev = left arrow
                    const prevIcon = isRTL ? rightArrowIcon : leftArrowIcon;
                    prevArrow.innerHTML = prevIcon;
                    prevArrow.classList.add('group');

                    // Add enhanced accessibility
                    prevArrow.setAttribute('aria-label', isRTL ? 'الشريحة التالية' : 'Previous slide');
                    prevArrow.setAttribute('title', isRTL ? 'الشريحة التالية' : 'Previous slide');
                }

                if (nextArrow) {
                    // In RTL: next = left arrow, in LTR: next = right arrow
                    const nextIcon = isRTL ? leftArrowIcon : rightArrowIcon;
                    nextArrow.innerHTML = nextIcon;
                    nextArrow.classList.add('group');

                    // Add enhanced accessibility
                    nextArrow.setAttribute('aria-label', isRTL ? 'الشريحة السابقة' : 'Next slide');
                    nextArrow.setAttribute('title', isRTL ? 'الشريحة السابقة' : 'Next slide');
                }
            }

            /**
             * Initialize Home Slider with Enhanced Error Handling
             */
            function initializeHomeSlider() {
                const sliderId = '<?php echo esc_js($slider_id); ?>';
                const sliderElement = document.getElementById(sliderId);

                if (!sliderElement) {
                    console.warn('🔍 Home Slider: Element not found -', sliderId);
                    return;
                }

                if (typeof Splide === 'undefined') {
                    console.error('❌ Home Slider: Splide library not loaded');
                    return;
                }

                try {
                    // Initialize Splide with configuration
                    const splide = new Splide('#' + sliderId, <?php echo $config_json; ?>);

                    // Add event listeners for enhanced functionality
                    splide.on('mounted', function() {
                        console.log('✅ Home Slider: Successfully mounted -', sliderId);

                        // Add loading complete class
                        sliderElement.classList.add('splide-loaded');

                        // Customize arrow icons
                        customizeArrowIcons(sliderElement);

                        // Trigger custom event
                        const event = new CustomEvent('halacoupon:slider:loaded', {
                            detail: { sliderId: sliderId, type: 'home-slider' }
                        });
                        document.dispatchEvent(event);
                    });

                    splide.on('autoplay:playing', function() {
                        sliderElement.setAttribute('aria-live', 'off');
                    });

                    splide.on('autoplay:pause', function() {
                        sliderElement.setAttribute('aria-live', 'polite');
                    });

                    // Handle visibility change for performance
                    document.addEventListener('visibilitychange', function() {
                        if (document.hidden) {
                            splide.Components.Autoplay.pause();
                        } else {
                            splide.Components.Autoplay.play();
                        }
                    });

                    // Mount the slider
                    splide.mount();

                } catch (error) {
                    console.error('❌ Home Slider: Initialization failed -', error);

                    // Fallback: Show first slide only
                    const slides = sliderElement.querySelectorAll('.splide__slide');
                    if (slides.length > 0) {
                        slides[0].style.display = 'block';
                        for (let i = 1; i < slides.length; i++) {
                            slides[i].style.display = 'none';
                        }
                    }
                }
            }

            // Initialize when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeHomeSlider);
            } else {
                initializeHomeSlider();
            }
        })();
        </script>
        <?php
    }
}

/**
 * Enqueue additional styles for home slider if needed
 *
 * @return void
 */
if (!function_exists('halacoupon_enqueue_home_slider_styles')) {
    function halacoupon_enqueue_home_slider_styles() {
        $is_rtl = is_rtl();
        $left_pos = $is_rtl ? 'right' : 'left';
        $right_pos = $is_rtl ? 'left' : 'right';

        $custom_css = "
        /* Home Slider Specific Styles */
        .home-slider-section .splide__slide img {
            object-position: center;
        }

        .home-slider-section .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .splide-loaded {
            opacity: 1;
            transition: opacity 0.3s ease-in-out;
        }

        /* Enhanced Modern Navigation Arrows */
        .home-slider-section .splide__arrows--modern {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            pointer-events: none;
            z-index: 15;
        }

        .home-slider-section .splide__arrow--modern {
            position: absolute;
            top: 0;
            width: 56px;
            height: 56px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(12px);
            border: 2px solid rgba(235, 191, 67, 0.3);
            border-radius: 16px;
            color: #EBBF43;
            pointer-events: auto;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(235, 191, 67, 0.15);
            cursor: pointer;
            opacity: 0.9;
        }

        .home-slider-section .splide__arrow--modern:hover {
            background: rgba(235, 191, 67, 0.95);
            color: white;
            box-shadow: 0 12px 40px rgba(235, 191, 67, 0.3), 0 4px 16px rgba(0, 0, 0, 0.15);
            border-color: rgba(235, 191, 67, 0.6);
            opacity: 1;
        }

        .home-slider-section .splide__arrow--modern:focus {
            outline: none;
            box-shadow: 0 0 0 4px rgba(235, 191, 67, 0.4), 0 12px 40px rgba(235, 191, 67, 0.3);
        }

        .home-slider-section .splide__arrow--modern:active {
            transition-duration: 0.1s;
        }

        .home-slider-section .splide__arrow--modern:disabled {
            opacity: 0.3;
            cursor: not-allowed;
            transform: none;
            background: rgba(255, 255, 255, 0.6);
            color: rgba(235, 191, 67, 0.5);
        }

        .home-slider-section .splide__arrow--prev {
            {$left_pos}: 20px;
        }

        .home-slider-section .splide__arrow--next {
            {$right_pos}: 20px;
        }

        .home-slider-section .splide__arrow--modern svg {
            width: 22px;
            height: 22px;
            stroke-width: 2.5;
            transition: transform 0.2s ease;
        }



        /* Enhanced Modern Pagination */
        .home-slider-section .splide__pagination--modern {
            display: flex;
            gap: 8px;
            z-index: 10;
        }

        .home-slider-section .splide__pagination__page--modern {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            border: 2px solid rgba(235, 191, 67, 0.3);
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }

        .home-slider-section .splide__pagination__page--modern::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(45deg, rgba(235, 191, 67, 0.1), rgba(255, 188, 2, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .home-slider-section .splide__pagination__page--modern:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.15);
            border-color: rgba(235, 191, 67, 0.6);
            box-shadow: 0 4px 12px rgba(235, 191, 67, 0.2);
        }

        .home-slider-section .splide__pagination__page--modern:hover::before {
            opacity: 1;
        }

        .home-slider-section .splide__pagination__page--modern.is-active {
            background: linear-gradient(135deg, #000, #000);
            transform: scale(1.25);
            border-color: rgba(235, 191, 67, 0.8);
            box-shadow: 0 6px 20px rgba(235, 191, 67, 0.4), 0 0 0 4px rgba(235, 191, 67, 0.2);
        }

        /* Responsive Navigation */
        @media (max-width: 768px) {
            .home-slider-section .splide__arrow--modern {
                display: none;
            }

       

            .home-slider-section .splide__pagination__page--modern {
                width: 10px;
                height: 10px;
            }
        }

        @media (max-width: 480px) {

            .home-slider-section .splide__pagination__page--modern {
                width: 8px;
                height: 8px;
            }
        }

        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
            .home-slider-section .animate-fade-in-up,
            .home-slider-section .group-hover\\:scale-105,
            .home-slider-section .transform,
            .home-slider-section .splide__arrow--modern,
            .home-slider-section .splide__pagination__page--modern {
                animation: none !important;
                transform: none !important;
                transition: none !important;
            }
        }

        /* Enhanced RTL Support */
        [dir='rtl'] .home-slider-section .splide__arrow--prev {
            right: 20px;
            left: auto;
        }

        [dir='rtl'] .home-slider-section .splide__arrow--next {
            left: 20px;
            right: auto;
        }

        /* RTL Mobile Support */
        @media (max-width: 768px) {
            [dir='rtl'] .home-slider-section .splide__arrow--prev {
                right: 16px;
            }

            [dir='rtl'] .home-slider-section .splide__arrow--next {
                left: 16px;
            }
        }

        /* Loading State Enhancement */
        .home-slider-section .splide-loading {
            background: linear-gradient(135deg, rgba(253, 249, 247, 0.95), rgba(235, 191, 67, 0.05));
            backdrop-filter: blur(8px);
            border-radius: 24px;
        }

        .home-slider-section .splide-loading .animate-spin {
            border-color: rgba(235, 191, 67, 0.2);
            border-top-color: #EBBF43;
        }
        ";

        wp_add_inline_style('halacoupon-splide-custom', $custom_css);
    }
}

// Hook to add custom styles when Splide assets are enqueued
add_action('halacoupon_splide_assets_enqueued', 'halacoupon_enqueue_home_slider_styles');

/**
 * Add schema markup for home slider (SEO enhancement)
 *
 * @return void
 */
if (!function_exists('halacoupon_add_home_slider_schema')) {
    function halacoupon_add_home_slider_schema() {
        if (!is_front_page()) {
            return;
        }

        $slides_query = new WP_Query([
            'post_type' => 'slides',
            'posts_per_page' => 5, // Limit for schema
            'post_status' => 'publish'
        ]);

        if (!$slides_query->have_posts()) {
            return;
        }

        $schema_items = [];
        while ($slides_query->have_posts()) {
            $slides_query->the_post();
            $slide_data = halacoupon_get_slide_meta_data(get_the_ID());

            if (!empty($slide_data['headline'])) {
                $schema_items[] = [
                    '@type' => 'WebPageElement',
                    'name' => $slide_data['headline'],
                    'description' => wp_strip_all_tags($slide_data['content']),
                    'url' => $slide_data['button_url'] ?: get_permalink(),
                    'image' => $slide_data['image']
                ];
            }
        }

        wp_reset_postdata();

        if (!empty($schema_items)) {
            $schema = [
                '@context' => 'https://schema.org',
                '@type' => 'ItemList',
                'name' => get_bloginfo('name') . ' - ' . __('Featured Content', 'halacoupon'),
                'itemListElement' => $schema_items
            ];

            echo '<script type="application/ld+json">' . wp_json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";
        }
    }
}

// Add schema markup to wp_head
add_action('wp_head', 'halacoupon_add_home_slider_schema', 20);

// Mark this module as loaded
add_filter('halacoupon_module_home_slider_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'sliders/home-slider';
    return $modules;
});

// Add action hook for theme customization
do_action('halacoupon_home_slider_loaded');
