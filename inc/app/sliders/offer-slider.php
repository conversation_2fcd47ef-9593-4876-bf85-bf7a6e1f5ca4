<?php
/**
 * Home Page Offers Slider
 * 
 * Special offers slider for the home page
 * 
 * @package HalaCoupon
 * @subpackage App\Sliders
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display home page offers slider
 */
if (!function_exists('display_home_offer_slider')) {
    function display_home_offer_slider() {
        // Query to fetch coupons with '_ags_home_page_offers_slider' key
        $args = array(
            'post_type'      => 'coupon',
            'meta_query'     => array(
                array(
                    'key'     => '_ags_home_page_offers_slider',
                    'value'   => 'on',
                    'compare' => '='
                ),
            ),
            'posts_per_page' => 6, // Limit to 6 posts for the slider
            'orderby'        => 'date',
            'order'          => 'DESC',
        );

        $slides = new WP_Query($args);
        if ($slides->have_posts()) {
            // Custom shape container (using the same class structure)
            echo '<div class="custom-shape-container" style="max-width: 98vw; margin: 0 auto;">';
            echo '<div class="owl-carousel custom-shape home-offer-slider owl-theme" id="home_offer_slider">';

            while ($slides->have_posts()) {
                $slides->the_post();
                halacoupon_setup_coupon();

                $slide_url = esc_url(get_post_meta(get_the_ID(), '_ags_destination_url', true));
                $background_image = esc_url(get_post_meta(get_the_ID(), '_ags_home_offer_slider_bg', true));
                $store_logo = halacoupon_coupon()->get_store_thumb();
                $coupon_title = get_the_title();

                echo '<div class="custom-side shape">';
                echo '<div class="custom-card" style="background-image: url(\'' . $background_image . '\'); background-size: cover; background-position: center;">';

                // Card content with title and store logo
                echo '<div class="card-content">';
                echo '<div class="store-logo">';
                echo '<a href="' . esc_url(halacoupon_coupon()->get_store_url()) . '">' . $store_logo . '</a>';
                echo '</div>'; // end store-logo
                echo '<h3 class="coupon-title"><a href="' . esc_url($slide_url) . '" title="' . esc_html($coupon_title) . '">' . esc_html($coupon_title) . '</a></h3>';
                echo '</div>'; // end card-content

                echo '</div>'; // end custom-card
                echo '</div>'; // end custom-side
            }

            echo '</div>'; // end owl-carousel
            echo '</div>'; // end custom-shape-container

            // Owl Carousel initialization
            echo '<script>
                jQuery(document).ready(function($) {
                    $("#home_offer_slider").owlCarousel({
                        items: 1, // Show one coupon at a time
                        rtl: true, // Enable right-to-left for RTL languages
                        loop: true, // Loop through the slides
                        autoplay: true, // Automatically play slides
                        autoplayHoverPause: true, // Pause on hover
                        nav: false, // Disable navigation arrows
                        dots: false, // Disable dots navigation
                        slideBy: 1, // Slide one by one
                        responsive: {
                            0: {
                                items: 1, // Display 1 coupon on small screens
                            },
                            600: {
                                items: 1, // Display 1 coupon on medium screens
                            },
                            1000: {
                                items: 1 // Display 1 coupon on large screens
                            }
                        }
                    });
                });
            </script>';

            wp_reset_postdata();
        } else {
            echo '<p>' . esc_html__('No coupons available for the slider.', 'halacoupon') . '</p>';
        }
    }
}

// Mark this module as loaded
add_filter('halacoupon_module_offer_slider_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'sliders/offer-slider';
    return $modules;
});
