<?php
/**
 * Category Slider
 * 
 * Coupon categories slider with icons
 * 
 * @package HalaCoupon
 * @subpackage App\Sliders
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display coupon categories with icons in a slider
 */
if ( ! function_exists( 'list_coupon_categories_with_icons' ) ) {
    function list_coupon_categories_with_icons() {
        $prefix = '_ags_';

        // Fetch the top coupon categories
        $categories = get_terms( array(
            'taxonomy'   => 'coupon_category',
            'number'     => 24,  // Adjust this to change the number of categories fetched
            'hide_empty' => true,
        ) );

        // Check if categories exist
        if ( ! empty( $categories ) && ! is_wp_error( $categories ) ) {

            // Enqueue Splide assets conditionally
            halacoupon_enqueue_splide_assets();

            echo '<div class="coupon-categories-carousel relative mb-8">';
            echo '<div class="max-w-7xl mx-auto">';

            // Section header
            echo '<div class="text-center mb-8">';
            echo '<h2 class="text-2xl lg:text-3xl font-bold text-text mb-3">';
            echo '<span class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">';
            echo esc_html__('Popular Categories', 'halacoupon');
            echo '</span>';
            echo '</h2>';
            echo '</div>';

            // Splide slider container
            echo '<div class="splide category-slider" id="coupon-categories-slider" dir="rtl">';
            echo '<div class="splide__track">';
            echo '<ul class="splide__list">';

            // Loop through categories and display them in slider
            foreach ( $categories as $category ) {
                $icon = get_term_meta( $category->term_id, $prefix . 'icon', true );
                $name = $category->name;
                $link = get_term_link( $category );
                $coupon_counts = halacoupon_get_coupon_counts_by_type( $category->term_id , 'coupon_category');
                $total_coupons = ($coupon_counts['code'] ?? 0) + ($coupon_counts['sale'] ?? 0);

                echo '<li class="splide__slide">';
                echo '<div class="coupon-category-item group px-2">';
                echo '<a href="' . esc_url( $link ) . '" class="block bg-white rounded-2xl shadow-soft hover:shadow-medium border border-gray-100 p-6 text-center transition-all duration-300 hover:-translate-y-1 hover:border-primary-200 group-hover:bg-gradient-to-br group-hover:from-primary-50 group-hover:to-accent-50">';

                // Icon container with enhanced design
                echo '<div class="relative w-16 h-16 mx-auto mb-4">';
                echo '<div class="absolute inset-0 bg-gradient-primary rounded-2xl opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>';
                echo '<div class="relative w-full h-full bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-soft">';
                echo ( $icon ? '<i class="category ' . esc_attr( $icon ) . ' text-xl text-primary-600"></i>' : '<svg class="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path></svg>' );
                echo '</div>';
                echo '</div>';

                // Category name with enhanced typography
                echo '<h3 class="text-base font-bold text-text mb-2 group-hover:text-primary transition-colors duration-300 line-clamp-1">' . esc_html( $name ) . '</h3>';

                // Count with icon
                echo '<div class="flex items-center justify-center gap-1 text-sm text-gray-600 group-hover:text-primary transition-colors duration-300">';
                echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>';
                echo '</svg>';
                echo '<span class="font-medium">' . esc_html($total_coupons) . '</span>';
                echo '<span>' . esc_html__('deals', 'halacoupon') . '</span>';
                echo '</div>';

                echo '</a>';
                echo '</div>';
                echo '</li>';
            }

            echo '</ul>'; // End splide__list
            echo '</div>'; // End splide__track
            echo '</div>'; // End splide
            echo '</div>'; // End container
            echo '</div>'; // End coupon-categories-carousel

            // Initialize Splide with category-specific configuration
            $config = halacoupon_get_splide_config('category-slider');
            echo halacoupon_generate_splide_script('#coupon-categories-slider', $config);
        }
    }
}

/**
 * Display featured categories slider (alias for list_coupon_categories_with_icons)
 */
if (!function_exists('halacoupon_get_featured_categories_slider')) {
    function halacoupon_get_featured_categories_slider() {
        // Get featured categories
        $categories = get_terms(array(
            'taxonomy'   => 'coupon_category',
            'number'     => 12,  // Limit to 12 featured categories
            'hide_empty' => true,
            'orderby'    => 'count',
            'order'      => 'DESC',
        ));

        // Check if categories exist
        if (!empty($categories) && !is_wp_error($categories)) {

            // Enqueue Splide assets conditionally
            halacoupon_enqueue_splide_assets();

            echo '<div class="featured-categories-slider-container relative mb-12">';
            echo '<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">';

            // Splide slider container
            echo '<div class="splide category-slider" id="featured-categories-slider" dir="rtl">';
            echo '<div class="splide__track">';
            echo '<ul class="splide__list">';

            foreach ($categories as $category) {
                $icon = get_term_meta($category->term_id, '_ags_icon', true);
                $category_image = get_term_meta($category->term_id, '_ags_category_image', true);
                $category_link = get_term_link($category);
                $category_name = $category->name;
                $coupon_counts = halacoupon_get_coupon_counts_by_type($category->term_id, 'coupon_category');
                $total_coupons = ($coupon_counts['code'] ?? 0) + ($coupon_counts['sale'] ?? 0);

                echo '<li class="splide__slide">';
                echo '<div class="featured-category-item group px-1">';
                echo '<div class="relative bg-white rounded-xl shadow-soft hover:shadow-medium border border-gray-100 overflow-hidden transition-all duration-300 hover:-translate-y-2 hover:border-primary-200">';

                // Background gradient overlay
                echo '<div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>';

                echo '<div class="relative p-6 text-center">';

                // Category icon/image with enhanced design
                echo '<div class="relative w-20 h-20 mx-auto mb-4">';
                echo '<div class="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>';
                echo '<a href="' . esc_url($category_link) . '" class="block relative">';

                if ($category_image) {
                    echo '<img src="' . esc_url($category_image) . '" alt="' . esc_attr($category_name) . '" class="relative w-full h-full object-cover rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-soft" loading="lazy">';
                } elseif ($icon) {
                    echo '<div class="relative w-full h-full bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-soft">';
                    echo '<i class="category ' . esc_attr($icon) . ' text-3xl text-primary"></i>';
                    echo '</div>';
                } else {
                    echo '<div class="relative w-full h-full bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-soft">';
                    echo '<svg class="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                    echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>';
                    echo '</svg>';
                    echo '</div>';
                }
                echo '</a>';
                echo '</div>';

                // Category info
                echo '<div class="space-y-3">';

                // Category name
                echo '<h3 class="text-lg font-bold text-text group-hover:text-primary transition-colors duration-300">';
                echo '<a href="' . esc_url($category_link) . '" class="hover:text-primary transition-colors duration-300">' . esc_html($category_name) . '</a>';
                echo '</h3>';

                // Category stats with modern design
                echo '<div class="flex items-center justify-center gap-2 text-sm text-gray-600 group-hover:text-primary transition-colors duration-300">';
                echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>';
                echo '</svg>';
                echo '<span class="font-medium">' . esc_html($total_coupons) . '</span>';
                echo '<span>' . esc_html__('deals', 'halacoupon') . '</span>';
                echo '</div>';

                echo '</div>'; // End category info
                echo '</div>'; // End content container

                // Hover arrow
                echo '<div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">';
                echo '<div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center shadow-lg">';
                echo '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>';
                echo '</svg>';
                echo '</div>';
                echo '</div>';

                echo '</div>'; // End category card
                echo '</div>'; // End featured-category-item
                echo '</li>';
            }

            echo '</ul>'; // Close splide__list
            echo '</div>'; // Close splide__track
            echo '</div>'; // Close splide
            echo '</div>'; // Close container
            echo '</div>'; // Close featured-categories-slider-container

            // Initialize Splide with category-specific configuration
            $config = halacoupon_get_splide_config('category-slider');
            echo halacoupon_generate_splide_script('#featured-categories-slider', $config);

        } else {
            echo '<div class="no-featured-categories">';
            echo '<p>' . esc_html__('No featured categories available.', 'halacoupon') . '</p>';
            echo '</div>';
        }
    }
}

// Mark this module as loaded
add_filter('halacoupon_module_category_slider_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'sliders/category-slider';
    return $modules;
});
