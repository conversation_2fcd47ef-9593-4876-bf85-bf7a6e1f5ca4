# HalaCoupon App - Modular Template Tags System

## Overview

The HalaCoupon theme has been refactored from a monolithic `template-tags.php` file into a modular app system. This provides better organization, maintainability, and performance.

## Directory Structure

```
inc/app/
├── loader.php              # Main loader file
├── README.md               # This documentation
├── utilities/              # Core utility functions
│   ├── helpers.php         # General helper functions
│   └── counters.php        # Statistics and counting functions
├── pagination/             # Pagination functionality
│   └── pagination.php      # Custom pagination with Semantic UI
├── navigation/             # Navigation components
│   └── breadcrumbs.php     # Breadcrumb navigation with schema
├── sliders/                # Slider and carousel components
│   ├── home-slider.php     # Main home page slider
│   ├── offer-slider.php    # Special offers slider
│   └── category-slider.php # Category icons slider
├── stores/                 # Store-related functionality
│   ├── store-functions.php # Core store functions
│   └── store-grid.php      # Store grid display
├── coupons/                # Coupon-related functionality
│   ├── coupon-functions.php # Core coupon functions
│   └── latest-coupons.php  # Latest coupons display
├── ajax/                   # AJAX handlers
│   └── store-filters.php   # Store filtering by character
├── forms/                  # Form components
│   ├── contact-form.php    # Contact form functionality
│   └── faqs.php           # FAQ display with accordion
├── social/                 # Social media components
│   └── social-profiles.php # Social media profiles display
└── localization/           # Localization and i18n
    ├── country-selector.php # Country/region selector
    └── hreflang.php        # SEO hreflang tags
```

## Key Features

### 🔧 Modular Architecture
- Each module is self-contained and focused on specific functionality
- Easy to maintain, debug, and extend
- Follows WordPress coding standards

### 📊 Performance Optimized
- Reduced memory footprint
- Faster file parsing
- Only loads required modules

### 🛡️ Error Handling
- Built-in error logging for missing modules
- Graceful fallbacks for missing functions
- Debug mode compatibility

### 🔄 Backward Compatibility
- All existing function calls continue to work
- No breaking changes to theme functionality
- Legacy template-tags.php acts as a loader

## Module Loading Order

The loader follows a specific order to ensure dependencies are met:

1. **Utilities** (helpers, counters)
2. **Navigation** (breadcrumbs)
3. **Pagination** (pagination functions)
4. **Sliders** (home, offer, category sliders)
5. **Stores & Coupons** (core functionality)
6. **Interactive Features** (AJAX, forms)
7. **Social & Localization** (social profiles, country selector)

## Usage Examples

### Adding a New Module

1. Create your module file in the appropriate directory
2. Add the module path to the `$modules` array in `loader.php`
3. Include the module loading filter at the end of your file:

```php
// Mark this module as loaded
add_filter('halacoupon_module_your_module_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'category/your-module';
    return $modules;
});
```

### Checking if a Module is Loaded

```php
if (halacoupon_is_module_loaded('pagination')) {
    // Module is loaded
}
```

### Getting App Information

```php
$app_info = halacoupon_get_app_info();
// Returns version, modules_path, loaded_modules
```

## Migration Benefits

### Before (Monolithic)
- ❌ 1,700+ lines in single file
- ❌ Difficult to maintain
- ❌ Hard to debug
- ❌ Performance overhead
- ❌ Code duplication

### After (Modular)
- ✅ Organized into logical modules
- ✅ Easy to maintain and debug
- ✅ Better performance
- ✅ Reusable components
- ✅ Clear separation of concerns

## Functions Moved

### Removed (Unused)
- `halacoupon_paging_nav()` - Replaced by `halacoupon_paginate_links()`
- `halacoupon_get_archive_title()` - WordPress native available
- `halacoupon_categorized_blog()` - Unused
- `halacoupon_comment()` & `halacoupon_comment_end()` - Unused
- And 10+ other unused functions

### Organized (Active)
- **Pagination**: `halacoupon_paginate_links()`
- **Navigation**: `halacoupon_breadcrumbs()`
- **Sliders**: `display_home_slider()`, `display_home_offer_slider()`, etc.
- **Stores**: `fetch_stores()`, `display_stores_grid()`, etc.
- **Coupons**: `get_latest_coupons()`, `halacoupon_get_coupon_counts_by_type()`
- **AJAX**: `filter_stores_by_character()`
- **Forms**: `halacoupon_contact_form()`, `display_coupon_store_faqs()`
- **Social**: `halacoupon_display_social_profiles()`
- **Localization**: `output_country_dropdown()`, `output_hreflang_tags()`

## Development Guidelines

1. **One responsibility per module**
2. **Follow WordPress coding standards**
3. **Include proper documentation**
4. **Add error handling**
5. **Use appropriate hooks and filters**
6. **Test thoroughly before deployment**

## Troubleshooting

### Module Not Loading
Check the debug log for error messages:
```
HalaCoupon App Module not found: /path/to/module.php
```

### Function Not Found
Ensure the module containing the function is loaded:
```php
if (!function_exists('your_function')) {
    // Function not available
}
```

## Version History

- **v2.0.0** - Initial modular system implementation
- **v1.0.0** - Legacy monolithic template-tags.php

---

**Author**: Abdullah G  
**Package**: HalaCoupon  
**License**: GPL v2 or later
