<?php
/**
 * Store Social Profiles & Page Links Display
 * Direct CMB2 implementation for store social profiles and page links
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display store social profiles and page links section
 *
 * @since 1.0.0
 * @param int $term_id Store term ID (optional, defaults to current queried object)
 * @return void
 */
function halacoupon_display_store_social_links_section($term_id = null) {
    
    // Get current store term ID if not provided
    if (!$term_id) {
        $term_id = get_queried_object_id();
    }
    
    // Get current store name for titles
    $store_term = get_term($term_id);
    $store_name = $store_term ? $store_term->name : '';
    
    // Get social profile URLs directly from CMB2 meta
    $facebook_url = get_term_meta($term_id, '_ags_store_fb', true);
    $twitter_url = get_term_meta($term_id, '_ags_store_x', true);
    $instagram_url = get_term_meta($term_id, '_ags_store_insta', true);
    $pinterest_url = get_term_meta($term_id, '_ags_store_pin', true);
    
    // Get page link URLs directly from CMB2 meta
    $contact_url = get_term_meta($term_id, '_ags_store_contact_url', true);
    $delivery_url = get_term_meta($term_id, '_ags_store_delivery_url', true);
    $returns_url = get_term_meta($term_id, '_ags_store_returns_policy_url', true);
    $faq_url = get_term_meta($term_id, '_ags_store_faq_url', true);
    
    // Check if we have any social profiles or page links
    $has_social = !empty($facebook_url) || !empty($twitter_url) || !empty($instagram_url) || !empty($pinterest_url);
    $has_page_links = !empty($contact_url) || !empty($delivery_url) || !empty($returns_url) || !empty($faq_url);
    
    // Only display if we have content
    if (!$has_social && !$has_page_links) {
        return;
    }
    ?>
    
    <!-- Store Social Profiles & Page Links -->
    <div class="relative group">
        <!-- Floating decorative elements -->
        <div class="absolute -inset-2 overflow-hidden pointer-events-none">
            <div class="absolute top-1 left-1 w-8 h-8 bg-secondary/10 rounded-full blur-lg animate-float-slow"></div>
            <div class="absolute bottom-1 right-1 w-6 h-6 bg-primary/15 rounded-full blur-md animate-float-fast" style="animation-delay: 2s;"></div>
        </div>

        <!-- Glass morphism container -->
        <div class="relative bg-white/80 backdrop-blur-md rounded-2xl shadow-soft border border-secondary/20 p-3 hover:bg-white/90 transition-all duration-300">
            
            <?php if ($has_social) : ?>
            <!-- Social Profiles Section -->
            <div class="mb-3">
                <h3 class="store-social-title text-sm font-semibold text-text mb-2 flex items-center gap-2">
                    <span class="w-1 h-5 bg-gradient-to-b from-primary to-secondary rounded-full"></span>
                    <?php 
                    if (!empty($store_name)) {
                        printf(esc_html__('%s Information', 'halacoupon'), esc_html($store_name));
                    } 
                    ?>
                </h3>
                <ul class="store-social-list flex flex-wrap gap-2 list-none m-0 p-0">
                    <?php if (!empty($facebook_url)) : ?>
                    <li class="store-social-item social-facebook">
                        <a href="<?php echo esc_url($facebook_url); ?>"
                           class="store-social-link flex items-center justify-center w-8 h-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-text hover:bg-primary/10 hover:border-primary/40 hover:-translate-y-0.5 transition-all duration-300"
                           target="_blank" 
                           rel="noopener noreferrer"
                           aria-label="<?php echo esc_attr(sprintf(__('Follow %s on Facebook', 'halacoupon'), $store_name)); ?>">
                            <span class="store-social-icon" aria-hidden="true">
                                <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if (!empty($twitter_url)) : ?>
                    <li class="store-social-item social-twitter">
                        <a href="<?php echo esc_url($twitter_url); ?>"
                           class="store-social-link flex items-center justify-center w-8 h-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-text hover:bg-primary/10 hover:border-primary/40 hover:-translate-y-0.5 transition-all duration-300"
                           target="_blank"
                           rel="noopener noreferrer"
                           aria-label="<?php echo esc_attr(sprintf(__('Follow %s on X (Twitter)', 'halacoupon'), $store_name)); ?>">
                            <span class="store-social-icon" aria-hidden="true">
                                <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                                </svg>
                            </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if (!empty($instagram_url)) : ?>
                    <li class="store-social-item social-instagram">
                        <a href="<?php echo esc_url($instagram_url); ?>"
                           class="store-social-link flex items-center justify-center w-8 h-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-text hover:bg-primary/10 hover:border-primary/40 hover:-translate-y-0.5 transition-all duration-300"
                           target="_blank"
                           rel="noopener noreferrer"
                           aria-label="<?php echo esc_attr(sprintf(__('Follow %s on Instagram', 'halacoupon'), $store_name)); ?>">
                            <span class="store-social-icon" aria-hidden="true">
                                <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                </svg>
                            </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if (!empty($pinterest_url)) : ?>
                    <li class="store-social-item social-pinterest">
                        <a href="<?php echo esc_url($pinterest_url); ?>"
                           class="store-social-link flex items-center justify-center w-8 h-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-text hover:bg-primary/10 hover:border-primary/40 hover:-translate-y-0.5 transition-all duration-300"
                           target="_blank"
                           rel="noopener noreferrer"
                           aria-label="<?php echo esc_attr(sprintf(__('Follow %s on Pinterest', 'halacoupon'), $store_name)); ?>">
                            <span class="store-social-icon" aria-hidden="true">
                                <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001 12.017.001z"/>
                                </svg>
                            </span>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
            <?php endif; ?>

            <?php if ($has_page_links) : ?>
            <!-- Store Page Links Section -->
            <div class="<?php echo $has_social ? 'border-t border-gray-200 pt-3' : ''; ?>">
                <ul class="store-links-list flex flex-col gap-2 list-none m-0 p-0">
                    <?php if (!empty($contact_url)) : ?>
                    <li class="store-link-item link-contact">
                        <a href="<?php echo esc_url($contact_url); ?>"
                           class="store-page-link flex items-center justify-between p-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-text hover:bg-primary/5 hover:border-primary/40 hover:-translate-y-0.5 transition-all duration-300"
                           target="_blank"
                           rel="noopener noreferrer"
                           aria-label="<?php echo esc_attr(sprintf(__('Visit %s Contact Us page', 'halacoupon'), $store_name)); ?>">
                            <span class="store-link-content flex items-center gap-2">
                                <span class="store-link-icon flex items-center justify-center w-6 h-6 bg-gradient-to-br from-green-400 to-green-600 rounded-md text-white">
                                    <svg viewBox="0 0 24 24" fill="currentColor" width="14" height="14">
                                        <path d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </span>
                                <span class="store-link-text">
                                    <span class="store-link-name text-xs font-semibold text-text">
                                        <?php
                                        if (!empty($store_name)) {
                                            printf(esc_html__('%s - Contact Us', 'halacoupon'), esc_html($store_name));
                                        } else {
                                            esc_html_e('Contact Us', 'halacoupon');
                                        }
                                        ?>
                                    </span>
                                </span>
                            </span>
                            <span class="store-link-external text-text/50" aria-hidden="true">
                                <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                                    <path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6M15 3h6v6M10 14L21 3" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if (!empty($delivery_url)) : ?>
                    <li class="store-link-item link-delivery">
                        <a href="<?php echo esc_url($delivery_url); ?>" 
                           class="store-page-link flex items-center justify-between p-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-text hover:bg-primary/5 hover:border-primary/40 hover:-translate-y-0.5 transition-all duration-300" 
                           target="_blank" 
                           rel="noopener noreferrer"
                           aria-label="<?php echo esc_attr(sprintf(__('Visit %s Delivery Information page', 'halacoupon'), $store_name)); ?>">
                            <span class="store-link-content flex items-center gap-3">
                                <span class="store-link-icon flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg text-white">
                                    <svg viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
                                        <path d="M1 3h15v9a4 4 0 11-8 0V9H1V3zm7 15.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zm11 0a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </span>
                                <span class="store-link-text">
                                    <span class="store-link-name text-sm font-semibold text-text">
                                        <?php 
                                        if (!empty($store_name)) {
                                            printf(esc_html__('%s - Delivery Information', 'halacoupon'), esc_html($store_name));
                                        } else {
                                            esc_html_e('Delivery Information', 'halacoupon');
                                        }
                                        ?>
                                    </span>
                                </span>
                            </span>
                            <span class="store-link-external text-text/50" aria-hidden="true">
                                <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                                    <path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6M15 3h6v6M10 14L21 3" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if (!empty($returns_url)) : ?>
                    <li class="store-link-item link-returns">
                        <a href="<?php echo esc_url($returns_url); ?>"
                           class="store-page-link flex items-center justify-between p-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-text hover:bg-primary/5 hover:border-primary/40 hover:-translate-y-0.5 transition-all duration-300"
                           target="_blank"
                           rel="noopener noreferrer"
                           aria-label="<?php echo esc_attr(sprintf(__('Visit %s Returns & Refunds page', 'halacoupon'), $store_name)); ?>">
                            <span class="store-link-content flex items-center gap-3">
                                <span class="store-link-icon flex items-center justify-center w-10 h-10 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg text-white">
                                    <svg viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
                                        <path d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </span>
                                <span class="store-link-text">
                                    <span class="store-link-name text-sm font-semibold text-text">
                                        <?php
                                        if (!empty($store_name)) {
                                            printf(esc_html__('%s - Returns & Refunds', 'halacoupon'), esc_html($store_name));
                                        } else {
                                            esc_html_e('Returns & Refunds', 'halacoupon');
                                        }
                                        ?>
                                    </span>
                                </span>
                            </span>
                            <span class="store-link-external text-text/50" aria-hidden="true">
                                <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                                    <path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6M15 3h6v6M10 14L21 3" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if (!empty($faq_url)) : ?>
                    <li class="store-link-item link-faq">
                        <a href="<?php echo esc_url($faq_url); ?>"
                           class="store-page-link flex items-center justify-between p-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-text hover:bg-primary/5 hover:border-primary/40 hover:-translate-y-0.5 transition-all duration-300"
                           target="_blank"
                           rel="noopener noreferrer"
                           aria-label="<?php echo esc_attr(sprintf(__('Visit %s FAQ page', 'halacoupon'), $store_name)); ?>">
                            <span class="store-link-content flex items-center gap-3">
                                <span class="store-link-icon flex items-center justify-center w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg text-white">
                                    <svg viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                                        <path d="M9.09 9a3 3 0 015.83 1c0 2-3 3-3 3" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M12 17h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </span>
                                <span class="store-link-text">
                                    <span class="store-link-name text-sm font-semibold text-text">
                                        <?php
                                        if (!empty($store_name)) {
                                            printf(esc_html__('%s - FAQ', 'halacoupon'), esc_html($store_name));
                                        } else {
                                            esc_html_e('FAQ', 'halacoupon');
                                        }
                                        ?>
                                    </span>
                                </span>
                            </span>
                            <span class="store-link-external text-text/50" aria-hidden="true">
                                <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                                    <path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6M15 3h6v6M10 14L21 3" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </span>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <?php
}
