<?php
/**
 * Latest Blog Posts Display
 * 
 * Functions for displaying latest blog posts in a special grid layout.
 * 
 * @package HalaCoupon
 * @subpackage App\Blog
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

if (!function_exists('halacoupon_latest_blogs')) {
    function halacoupon_latest_blogs() {
        $latest_posts_query = new WP_Query([
            'post_type'      => 'post',
            'posts_per_page' => 5,
            'post_status'    => 'publish',
            'orderby'        => 'date',
            'order'          => 'DESC',
            'ignore_sticky_posts' => 1
        ]);

        if (!$latest_posts_query->have_posts()) {
            return;
        }

        $is_rtl = is_rtl();

        $first_post = array_slice($latest_posts_query->posts, 0, 1);
        $other_posts = array_slice($latest_posts_query->posts, 1, 4);
        ?>
        <div class="lg:grid lg:grid-cols-2 lg:gap-8" dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">
            <!-- Latest Post (50%) -->
            <div class="mb-8 lg:mb-0">
                <?php
                if (!empty($first_post)) {
                    global $post;
                    $post = $first_post[0];
                    setup_postdata($post);
                    halacoupon_render_blog_card('large');
                    wp_reset_postdata();
                }
                ?>
            </div>

            <!-- Other 4 posts (50% in 2x2 grid) -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
            <?php
            if (!empty($other_posts)) {
                foreach ($other_posts as $post) {
                    setup_postdata($post);
                    halacoupon_render_blog_card('small');
                }
                wp_reset_postdata();
            }
            ?>
            </div>
        </div>
        <?php
    }
}

if (!function_exists('halacoupon_render_blog_card')) {
    function halacoupon_render_blog_card($size = 'small') {
        $post_id = get_the_ID();
        $post_title = get_the_title();
        $post_url = get_permalink();
        $featured_image_url = get_the_post_thumbnail_url($post_id, $size === 'large' ? 'large' : 'medium_large');
        
        $card_height = $size === 'large' ? 'h-96 lg:h-full min-h-[480px]' : 'h-64';
        $title_size = $size === 'large' ? 'text-2xl md:text-3xl' : 'text-xl';
        
        if (!$featured_image_url) {
            $featured_image_url = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'; // Transparent pixel
        }
        ?>
        <article class="blog-card group relative rounded-2xl overflow-hidden shadow-soft hover:shadow-float-lg transition-all duration-300 <?php echo esc_attr($card_height); ?>">
            <a href="<?php echo esc_url($post_url); ?>" class="absolute inset-0 z-10" aria-label="<?php echo esc_attr($post_title); ?>">
                <span class="visually-hidden"><?php echo esc_html($post_title); ?></span>
            </a>
            
            <!-- Background Image -->
            <div class="absolute inset-0 bg-gradient-to-br from-primary to-secondary">
                <img src="<?php echo esc_url($featured_image_url); ?>" 
                     alt="" 
                     aria-hidden="true"
                     class="w-full h-full object-cover transition-transform duration-500 ease-out group-hover:scale-105">
            </div>

            <!-- Gradient Overlay for text readability -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-80 group-hover:opacity-100 transition-opacity duration-300"></div>

            <!-- Content Area -->
            <div class="relative z-20 flex flex-col justify-end h-full p-6 md:p-8 text-white">
                <div class="relative">
                    <!-- Glass effect background for text on hover -->
                    <div class="absolute inset-x-[-1.5rem] inset-y-[-1rem] bg-black/20 backdrop-blur-md group-hover:bg-black/30 group-hover:backdrop-blur-lg rounded-xl transition-all duration-300 ease-out pointer-events-none"></div>
                    
                    <h3 class="relative font-bold <?php echo esc_attr($title_size); ?> leading-tight">
                        <a href="<?php echo esc_url($post_url); ?>" class="pretty-link">
                            <?php echo esc_html($post_title); ?>
                        </a>
                    </h3>
                </div>
            </div>
        </article>
        <?php
    }
}

if (!function_exists('halacoupon_latest_blogs_styles')) {
    function halacoupon_latest_blogs_styles() {
        $custom_css = "
        .blog-card .pretty-link {
            position: relative;
            text-decoration: none;
            color: white;
        }
        .blog-card .pretty-link::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: white;
            transition: width 0.3s ease-out;
        }
        .blog-card:hover .pretty-link::after {
            width: 100%;
        }
        ";
        wp_add_inline_style('halacoupon-main', $custom_css);
    }
}
add_action('wp_enqueue_scripts', 'halacoupon_latest_blogs_styles');


// Mark this module as loaded
add_filter('halacoupon_module_latest_blogs_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'blog/latest-blogs';
    return $modules;
}); 