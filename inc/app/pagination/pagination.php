<?php
/**
 * Pagination Functions
 * 
 * Custom pagination functionality for the theme
 * 
 * @package HalaCoupon
 * @subpackage App\Pagination
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Modern pagination links with Tailwind CSS and creative design
 * Features: Glass morphism, brand colors, hover effects, responsive design, RTL/LTR support
 */
function halacoupon_paginate_links( $args = '' ) {
	global $wp_query, $wp_rewrite;

	// Get RTL status
	$is_rtl = is_rtl();

	// Setting up default values based on the current URL.
	$pagenum_link = html_entity_decode( get_pagenum_link() );
	$url_parts    = explode( '?', $pagenum_link );

	// Get max pages and current page out of the current query, if available.
	$total   = isset( $wp_query->max_num_pages ) ? $wp_query->max_num_pages : 1;
	$current = get_query_var( 'paged' ) ? intval( get_query_var( 'paged' ) ) : 1;

	// Append the format placeholder to the base URL.
	$pagenum_link = trailingslashit( $url_parts[0] ) . '%_%';

	// URL base depends on permalink settings.
	$format  = $wp_rewrite->using_index_permalinks() && ! strpos( $pagenum_link, 'index.php' ) ? 'index.php/' : '';
	$format .= $wp_rewrite->using_permalinks() ? user_trailingslashit( $wp_rewrite->pagination_base . '/%#%', 'paged' ) : '?paged=%#%';

	// RTL-aware prev/next text with proper icon positioning
	$prev_icon_class = $is_rtl ? 'ml-2' : 'mr-2';
	$next_icon_class = $is_rtl ? 'mr-2' : 'ml-2';
	
	$prev_text = '<svg class="w-4 h-4 ' . $prev_icon_class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="' . ($is_rtl ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7') . '"></path></svg>' . esc_html__( 'Previous', 'halacoupon' );
	$next_text = esc_html__( 'Next', 'halacoupon' ) . '<svg class="w-4 h-4 ' . $next_icon_class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="' . ($is_rtl ? 'M15 19l-7-7 7-7' : 'M9 5l7 7-7 7') . '"></path></svg>';

	$defaults = array(
		'base' => $pagenum_link, // http://example.com/all_posts.php%_% : %_% is replaced by format (below)
		'format' => $format, // ?page=%#% : %#% is replaced by the page number
		'total' => $total,
		'current' => $current,
		'show_all' => false,
		'prev_next' => true,
		'prev_text' => $prev_text,
		'next_text' => $next_text,
		'end_size' => 1,
		'mid_size' => 2,
		'type' => 'plain',
		'add_args' => array(), // array of query args to add
		'add_fragment' => '',
		'before_page_number' => '',
		'after_page_number' => '',
	);

	$args = wp_parse_args( $args, $defaults );

	if ( ! is_array( $args['add_args'] ) ) {
		$args['add_args'] = array();
	}

	// Merge additional query vars found in the original URL into 'add_args' array.
	if ( isset( $url_parts[1] ) ) {
		// Find the format argument.
		$format_query = parse_url( str_replace( '%_%', $args['format'], $args['base'] ), PHP_URL_QUERY );
		wp_parse_str( $format_query, $format_arg );

		// Remove the format argument from the array of query arguments, to avoid overwriting custom format.
		wp_parse_str( remove_query_arg( array_keys( $format_arg ), $url_parts[1] ), $query_args );
		$args['add_args'] = array_merge( $args['add_args'], urlencode_deep( $query_args ) );
	}

	// Who knows what else people pass in $args
	$total = (int) $args['total'];
	if ( $total < 2 ) {
		return;
	}
	$current  = (int) $args['current'];
	$end_size = (int) $args['end_size']; // Out of bounds?  Make it the default.
	if ( $end_size < 1 ) {
		$end_size = 1;
	}
	$mid_size = (int) $args['mid_size'];
	if ( $mid_size < 0 ) {
		$mid_size = 2;
	}
	$add_args = $args['add_args'];
	$r = '';
	$page_links = array();
	$dots = false;

	// RTL-aware border radius classes
	$first_border_radius = $is_rtl ? 'rounded-r-xl' : 'rounded-l-xl';
	$last_border_radius = $is_rtl ? 'rounded-l-xl' : 'rounded-r-xl';

	if ( $args['prev_next'] && $current && 1 < $current ) :
		$link = str_replace( '%_%', 2 == $current ? '' : $args['format'], $args['base'] );
		$link = str_replace( '%#%', $current - 1, $link );
		if ( $add_args ) {
			$link = add_query_arg( $add_args, $link );
		}
		$link .= $args['add_fragment'];

		/**
		 * Filter the paginated links for the given archive pages.
		 *
		 * @since 3.0.0
		 *
		 * @param string $link The paginated link URL.
		 */
		$page_links[] = '<a class="inline-flex items-center px-4 py-2 bg-white border border-gray-300 text-sm font-medium text-gray-700 hover:bg-primary hover:text-white hover:border-primary ' . $first_border_radius . ' transition-all duration-300 shadow-soft hover:shadow-medium" style="' . ($is_rtl ? 'border-top-right-radius: 0.75rem !important; border-bottom-right-radius: 0.75rem !important; border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important;' : 'border-top-left-radius: 0.75rem !important; border-bottom-left-radius: 0.75rem !important; border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important;') . '" href="' . esc_url( apply_filters( 'paginate_links', $link ) ) . '">' . $args['prev_text'] . '</a>';
	endif;
	for ( $n = 1; $n <= $total; $n++ ) :
		if ( $n == $current ) :
			$page_links[] = "<div class='inline-flex items-center px-4 py-2 bg-gradient-primary text-white text-sm font-bold border border-primary shadow-soft relative z-10'>" . $args['before_page_number'] . number_format_i18n( $n ) . $args['after_page_number'] . '</div>';
			$dots = true;
		else :
			if ( $args['show_all'] || ( $n <= $end_size || ( $current && $n >= $current - $mid_size && $n <= $current + $mid_size ) || $n > $total - $end_size ) ) :
				$link = str_replace( '%_%', 1 == $n ? '' : $args['format'], $args['base'] );
				$link = str_replace( '%#%', $n, $link );
				if ( $add_args ) {
					$link = add_query_arg( $add_args, $link );
				}
				$link .= $args['add_fragment'];

				/** This filter is documented in wp-includes/general-template.php */
				$page_links[] = "<a class='inline-flex items-center px-4 py-2 bg-white border border-gray-300 text-sm font-medium text-gray-700 hover:bg-primary hover:text-white hover:border-primary transition-all duration-300 shadow-soft hover:shadow-medium' href='" . esc_url( apply_filters( 'paginate_links', $link ) ) . "'>" . $args['before_page_number'] . number_format_i18n( $n ) . $args['after_page_number'] . '</a>';
				$dots = true;
			elseif ( $dots && ! $args['show_all'] ) :
				$page_links[] = '<div class="inline-flex items-center px-4 py-2 bg-white border border-gray-300 text-sm font-medium text-gray-500">' . esc_html__( '&hellip;', 'halacoupon' ) . '</div>';
				$dots = false;
			endif;
		endif;
	endfor;
	if ( $args['prev_next'] && $current && ( $current < $total || -1 == $total ) ) :
		$link = str_replace( '%_%', $args['format'], $args['base'] );
		$link = str_replace( '%#%', $current + 1, $link );
		if ( $add_args ) {
			$link = add_query_arg( $add_args, $link );
		}
		$link .= $args['add_fragment'];

		/** This filter is documented in wp-includes/general-template.php */
		$page_links[] = '<a class="inline-flex items-center px-4 py-2 bg-white border border-gray-300 text-sm font-medium text-gray-700 hover:bg-primary hover:text-white hover:border-primary ' . $last_border_radius . ' transition-all duration-300 shadow-soft hover:shadow-medium" style="' . ($is_rtl ? 'border-top-left-radius: 0.75rem !important; border-bottom-left-radius: 0.75rem !important; border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important;' : 'border-top-right-radius: 0.75rem !important; border-bottom-right-radius: 0.75rem !important; border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important;') . '" href="' . esc_url( apply_filters( 'paginate_links', $link ) ) . '">' . $args['next_text'] . '</a>';
	endif;
	
	// RTL-aware container classes
	$container_classes = 'flex items-center justify-center space-x-0 overflow-hidden';
	if ($is_rtl) {
		$container_classes .= ' rtl';
	}
	
	switch ( $args['type'] ) {
		case 'array':
			return $page_links;

		case 'list':
			$r .= "<ul class='" . $container_classes . "' dir='" . ($is_rtl ? 'rtl' : 'ltr') . "'>\n\t<li>";
			$r .= join( "</li>\n\t<li>", $page_links );
			$r .= "</li>\n</ul>\n";
			break;

		default:
			$r = '<div class="' . $container_classes . '" dir="' . ($is_rtl ? 'rtl' : 'ltr') . '">' . join( "", $page_links ) . '</div>';
			break;
	}
	return $r;
}

// Mark this module as loaded
add_filter('halacoupon_module_pagination_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'pagination/pagination';
    return $modules;
});
