<?php
/**
 * Reviews Functions
 * 
 * Core functions for handling reviews display, filtering, and pagination
 * Optimized for performance with proper caching and RTL/LTR support
 *
 * @package HalaCoupon
 * @version 1.0.0 - Reviews System
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display reviews with filtering and pagination
 * 
 * @param array $args Query arguments
 * @return void
 */
function halacoupon_display_reviews($args = array()) {
    $defaults = array(
        'posts_per_page' => 12,
        'post_type' => 'review',
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC',
        'meta_query' => array(),
        'tax_query' => array(),
        'paged' => get_query_var('paged') ? get_query_var('paged') : 1,
        'show_filters' => false, // Disabled rating filters
        'show_pagination' => true,
        'layout' => 'grid', // grid, list, masonry
        'columns' => 3, // for grid layout
    );
    
    $args = wp_parse_args($args, $defaults);
    
    // Handle filtering (only category and sort filters, no rating filters)
    $args = halacoupon_apply_review_filters($args);
    
    // Create query
    $reviews_query = new WP_Query($args);
    
    $is_rtl = is_rtl();
    
    ?>
    <div class="reviews-section" dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">
        
        <?php if ($args['show_filters']) : ?>
        <!-- Reviews Filters (Simplified - No Rating Filters) -->
        <div class="reviews-filters mb-8 p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/30">
            <div class="flex flex-wrap items-center gap-4 <?php echo $is_rtl ? 'flex-row-reverse' : ''; ?>">
                
                <!-- Category Filter -->
                <?php
                $review_categories = get_terms(array(
                    'taxonomy' => 'review_category',
                    'hide_empty' => true,
                ));
                if (!empty($review_categories)) :
                ?>
                <div class="filter-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <?php esc_html_e('Category', 'halacoupon'); ?>
                    </label>
                    <select name="category_filter" class="category-filter px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary">
                        <option value=""><?php esc_html_e('All Categories', 'halacoupon'); ?></option>
                        <?php foreach ($review_categories as $category) : ?>
                        <option value="<?php echo esc_attr($category->slug); ?>" <?php selected(isset($_GET['category_filter']) ? $_GET['category_filter'] : '', $category->slug); ?>>
                            <?php echo esc_html($category->name); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php endif; ?>
                
                <!-- Sort Order -->
                <div class="filter-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <?php esc_html_e('Sort By', 'halacoupon'); ?>
                    </label>
                    <select name="sort_filter" class="sort-filter px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary">
                        <option value="date_desc" <?php selected(isset($_GET['sort_filter']) ? $_GET['sort_filter'] : '', 'date_desc'); ?>><?php esc_html_e('Newest First', 'halacoupon'); ?></option>
                        <option value="date_asc" <?php selected(isset($_GET['sort_filter']) ? $_GET['sort_filter'] : '', 'date_asc'); ?>><?php esc_html_e('Oldest First', 'halacoupon'); ?></option>
                        <option value="title_asc" <?php selected(isset($_GET['sort_filter']) ? $_GET['sort_filter'] : '', 'title_asc'); ?>><?php esc_html_e('Title A-Z', 'halacoupon'); ?></option>
                        <option value="title_desc" <?php selected(isset($_GET['sort_filter']) ? $_GET['sort_filter'] : '', 'title_desc'); ?>><?php esc_html_e('Title Z-A', 'halacoupon'); ?></option>
                    </select>
                </div>
                
            </div>
        </div>
        <?php endif; ?>
        
        <?php if ($reviews_query->have_posts()) : ?>
        
        <!-- Reviews Grid -->
        <div class="reviews-grid grid grid-cols-1 <?php echo $args['columns'] === 2 ? 'md:grid-cols-2' : ($args['columns'] === 3 ? 'md:grid-cols-2 lg:grid-cols-3' : 'md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'); ?> gap-6 md:gap-8">
            
            <?php
            while ($reviews_query->have_posts()) :
                $reviews_query->the_post();
                
                // Get review data using our new article rating system
                $review_id = get_the_ID();
                $article_rating = get_post_meta($review_id, '_halacoupon_article_average_rating', true) ?: 0;
                $article_rating_count = get_post_meta($review_id, '_halacoupon_article_total_ratings', true) ?: 0;
                $author_name = get_the_author();
                $store_id = get_post_meta($review_id, '_halacoupon_review_store_id', true);
                
                // Get associated store info
                $store_name = '';
                $store_url = '';
                if ($store_id) {
                    $store = get_term($store_id, 'coupon_store');
                    if ($store && !is_wp_error($store)) {
                        $store_name = $store->name;
                        $store_url = get_term_link($store);
                    }
                }
                
                // Use the consistent article card function
                halacoupon_display_article_card(array(
                    'article_id' => $review_id,
                    'article_rating' => $article_rating,
                    'article_rating_count' => $article_rating_count,
                    'author_name' => $author_name,
                    'store_name' => $store_name,
                    'store_url' => $store_url,
                    'show_excerpt' => true,
                    'excerpt_length' => 20,
                    'show_interactive_rating' => false,
                ));
                
            endwhile;
            wp_reset_postdata();
            ?>
            
        </div>
        
        <?php if ($args['show_pagination'] && $reviews_query->max_num_pages > 1) : ?>
        <!-- Enhanced Pagination -->
        <div class="reviews-pagination mt-12">
            <?php
            // Use the theme's custom pagination function
            echo halacoupon_paginate_links(array(
                'total' => $reviews_query->max_num_pages,
                'current' => max(1, get_query_var('paged')),
                'format' => '?paged=%#%',
                'show_all' => false,
                'type' => 'list',
                'end_size' => 2,
                'mid_size' => 1,
                'prev_next' => true,
                'prev_text' => '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>' . esc_html__('Previous', 'halacoupon'),
                'next_text' => esc_html__('Next', 'halacoupon') . '<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>',
                'add_args' => array(
                    'category_filter' => isset($_GET['category_filter']) ? $_GET['category_filter'] : '',
                    'sort_filter' => isset($_GET['sort_filter']) ? $_GET['sort_filter'] : '',
                ),
            ));
            ?>
        </div>
        <?php endif; ?>
        
        <?php else : ?>
        
        <!-- No Reviews Found -->
        <div class="no-reviews-found text-center py-16">
            <div class="max-w-md mx-auto">
                <div class="mb-6">
                    <svg class="w-24 h-24 text-gray-300 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php esc_html_e('No Review Articles Found', 'halacoupon'); ?></h3>
                <p class="text-gray-600 mb-6"><?php esc_html_e('No review articles match your current filters. Try adjusting your search criteria.', 'halacoupon'); ?></p>
                <a href="<?php echo esc_url(get_post_type_archive_link('review')); ?>" 
                   class="inline-flex items-center gap-2 px-6 py-3 bg-primary text-white font-medium rounded-lg hover:bg-primary-600 transition-colors duration-200">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                    </svg>
                    <?php esc_html_e('View All Articles', 'halacoupon'); ?>
                </a>
            </div>
        </div>
        
        <?php endif; ?>
        
    </div>
    
    <?php if ($args['show_filters']) : ?>
    <script>
    jQuery(document).ready(function($) {
        // Handle filter changes
        $('.category-filter, .sort-filter').on('change', function() {
            var form = $('<form method="get"></form>');
            var currentUrl = window.location.href.split('?')[0];
            
            // Add current filters
            $('.category-filter, .sort-filter').each(function() {
                var value = $(this).val();
                if (value) {
                    form.append('<input type="hidden" name="' + $(this).attr('name') + '" value="' + value + '">');
                }
            });
            
            // Add current page if exists
            var urlParams = new URLSearchParams(window.location.search);
            var paged = urlParams.get('paged');
            if (paged) {
                form.append('<input type="hidden" name="paged" value="' + paged + '">');
            }
            
            form.attr('action', currentUrl);
            $('body').append(form);
            form.submit();
        });
    });
    </script>
    <?php endif; ?>
    
    <?php
}

/**
 * Apply filters to review query arguments
 * 
 * @param array $args Query arguments
 * @return array Modified query arguments
 */
function halacoupon_apply_review_filters($args) {
    // Category filter
    if (isset($_GET['category_filter']) && !empty($_GET['category_filter'])) {
        $args['tax_query'][] = array(
            'taxonomy' => 'review_category',
            'field' => 'slug',
            'terms' => sanitize_text_field($_GET['category_filter'])
        );
    }
    
    // Sort filter
    if (isset($_GET['sort_filter']) && !empty($_GET['sort_filter'])) {
        $sort = sanitize_text_field($_GET['sort_filter']);
        switch ($sort) {
            case 'date_asc':
                $args['orderby'] = 'date';
                $args['order'] = 'ASC';
                break;
            case 'title_asc':
                $args['orderby'] = 'title';
                $args['order'] = 'ASC';
                break;
            case 'title_desc':
                $args['orderby'] = 'title';
                $args['order'] = 'DESC';
                break;
            default: // date_desc
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
                break;
        }
    }
    
    return $args;
}

/**
 * Display individual review card with glass morphism design
 *
 * @param array $args Review card arguments
 * @return void
 */
function halacoupon_display_review_card($args = array()) {
    $defaults = array(
        'review_id' => 0,
        'rating' => 0,
        'reviewer_name' => '',
        'store_name' => '',
        'store_url' => '',
        'coupon_title' => '',
        'coupon_url' => '',
        'is_verified' => false,
        'show_excerpt' => true,
        'excerpt_length' => 20,
    );

    $args = wp_parse_args($args, $defaults);
    extract($args);

    if (!$review_id) {
        return;
    }

    $is_rtl = is_rtl();
    $review_date = get_the_date('', $review_id);
    $review_content = get_the_content(null, false, $review_id);
    $review_title = get_the_title($review_id);
    $review_url = get_permalink($review_id);

    // Get reviewer avatar
    $reviewer_email = get_post_meta($review_id, '_halacoupon_reviewer_email', true);
    $avatar_url = $reviewer_email ? get_avatar_url($reviewer_email, array('size' => 48)) : '';

    ?>
    <article class="review-card group relative bg-white/95 backdrop-blur-xl rounded-2xl border border-white/30 hover:border-primary/40 transition-all duration-500 hover:shadow-float-lg hover:scale-[1.02] overflow-hidden animate-fade-in-up"
             itemscope itemtype="https://schema.org/Review"
             dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">

        <!-- Decorative Hover Element -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>

        <!-- Decorative Corner Accent -->
        <div class="absolute top-0 <?php echo $is_rtl ? 'left-0' : 'right-0'; ?> w-0 h-0 border-t-[20px] border-<?php echo $is_rtl ? 'l' : 'r'; ?>-[20px] border-t-primary/10 border-<?php echo $is_rtl ? 'l' : 'r'; ?>-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <!-- Card Content -->
        <div class="relative p-6 h-full flex flex-col <?php echo $is_rtl ? 'text-right' : 'text-left'; ?>">

            <!-- Header with Rating and Verification -->
            <div class="flex items-start justify-between mb-4 <?php echo $is_rtl ? 'flex-row-reverse' : ''; ?>">

                <!-- Rating Display -->
                <div class="flex items-center gap-2 <?php echo $is_rtl ? 'flex-row-reverse' : ''; ?>">
                    <div class="flex items-center gap-1 <?php echo $is_rtl ? 'flex-row-reverse' : ''; ?>">
                        <?php for ($i = 1; $i <= 5; $i++) : ?>
                        <svg class="w-4 h-4 <?php echo $i <= $rating ? 'text-primary' : 'text-gray-300'; ?>" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                        <?php endfor; ?>
                    </div>
                    <span class="text-sm font-medium text-gray-700"><?php echo number_format($rating, 1); ?></span>
                </div>

                <!-- Verification Badge -->
                <?php if ($is_verified) : ?>
                <span class="inline-flex items-center gap-1 px-2 py-1 bg-green-100 border border-green-300 text-green-800 text-xs font-semibold rounded-full">
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <?php esc_html_e('Verified', 'halacoupon'); ?>
                </span>
                <?php endif; ?>

            </div>

            <!-- Review Title -->
            <?php if ($review_title) : ?>
            <h3 class="text-lg font-bold text-gray-900 mb-3 leading-tight group-hover:text-primary transition-colors duration-300" itemprop="name">
                <a href="<?php echo esc_url($review_url); ?>" class="hover:underline">
                    <?php echo esc_html($review_title); ?>
                </a>
            </h3>
            <?php endif; ?>

            <!-- Review Content -->
            <?php if ($show_excerpt && $review_content) : ?>
            <div class="review-content flex-grow mb-4 text-gray-700 leading-relaxed" itemprop="reviewBody">
                <?php
                if (strlen($review_content) > ($excerpt_length * 10)) {
                    echo '<p class="line-clamp-3">' . wp_trim_words($review_content, $excerpt_length, '...') . '</p>';
                } else {
                    echo '<p>' . esc_html($review_content) . '</p>';
                }
                ?>
            </div>
            <?php endif; ?>

            <!-- Associated Store/Coupon -->
            <?php if ($store_name || $coupon_title) : ?>
            <div class="associated-items mb-4 p-3 bg-gray-50/80 rounded-lg border border-gray-100">
                <?php if ($store_name) : ?>
                <div class="flex items-center gap-2 mb-2 <?php echo $is_rtl ? 'flex-row-reverse' : ''; ?>">
                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                    </svg>
                    <span class="text-sm text-gray-600"><?php esc_html_e('Store:', 'halacoupon'); ?></span>
                    <?php if ($store_url) : ?>
                    <a href="<?php echo esc_url($store_url); ?>" class="text-sm font-medium text-primary hover:text-primary-600 transition-colors duration-200">
                        <?php echo esc_html($store_name); ?>
                    </a>
                    <?php else : ?>
                    <span class="text-sm font-medium text-gray-900"><?php echo esc_html($store_name); ?></span>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <?php if ($coupon_title) : ?>
                <div class="flex items-center gap-2 <?php echo $is_rtl ? 'flex-row-reverse' : ''; ?>">
                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                    </svg>
                    <span class="text-sm text-gray-600"><?php esc_html_e('Coupon:', 'halacoupon'); ?></span>
                    <?php if ($coupon_url) : ?>
                    <a href="<?php echo esc_url($coupon_url); ?>" class="text-sm font-medium text-primary hover:text-primary-600 transition-colors duration-200">
                        <?php echo esc_html(wp_trim_words($coupon_title, 6)); ?>
                    </a>
                    <?php else : ?>
                    <span class="text-sm font-medium text-gray-900"><?php echo esc_html(wp_trim_words($coupon_title, 6)); ?></span>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- Footer with Reviewer Info and Date -->
            <div class="review-footer flex items-center justify-between pt-4 border-t border-gray-100 mt-auto <?php echo $is_rtl ? 'flex-row-reverse' : ''; ?>">

                <!-- Reviewer Info -->
                <div class="reviewer-info flex items-center gap-3 <?php echo $is_rtl ? 'flex-row-reverse' : ''; ?>">
                    <?php if ($avatar_url) : ?>
                    <img src="<?php echo esc_url($avatar_url); ?>"
                         alt="<?php echo esc_attr($reviewer_name); ?>"
                         class="w-8 h-8 rounded-full border border-gray-200"
                         loading="lazy">
                    <?php else : ?>
                    <div class="w-8 h-8 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                        <svg class="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <?php endif; ?>
                    <div>
                        <div class="text-sm font-medium text-gray-900" itemprop="author"><?php echo esc_html($reviewer_name); ?></div>
                        <div class="text-xs text-gray-500" itemprop="datePublished" content="<?php echo esc_attr(get_the_date('c', $review_id)); ?>">
                            <?php echo esc_html($review_date); ?>
                        </div>
                    </div>
                </div>

                <!-- Read More Link -->
                <a href="<?php echo esc_url($review_url); ?>"
                   class="inline-flex items-center gap-1 text-primary font-medium text-sm hover:text-primary-600 transition-colors duration-200 group/cta">
                    <?php esc_html_e('Read More', 'halacoupon'); ?>
                    <svg class="w-4 h-4 <?php echo $is_rtl ? 'group-hover/cta:-translate-x-1' : 'group-hover/cta:translate-x-1'; ?> transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $is_rtl ? 'M7 16l-4-4m0 0l4-4m-4 4h18' : 'M17 8l4 4m0 0l-4 4m4-4H3'; ?>"/>
                    </svg>
                </a>

            </div>

        </div>

        <!-- Schema Markup -->
        <div class="review-schema" style="display: none;">
            <div itemprop="reviewRating" itemscope itemtype="https://schema.org/Rating">
                <meta itemprop="ratingValue" content="<?php echo esc_attr($rating); ?>">
                <meta itemprop="bestRating" content="5">
            </div>
            <?php if ($store_name) : ?>
            <div itemprop="itemReviewed" itemscope itemtype="https://schema.org/Organization">
                <meta itemprop="name" content="<?php echo esc_attr($store_name); ?>">
            </div>
            <?php endif; ?>
        </div>

    </article>
    <?php
}

/**
 * Display featured review articles for front page
 * Shows latest review articles with high user ratings
 *
 * @return void
 */
function halacoupon_featured_reviews() {
    $is_rtl = is_rtl();

    // Get featured review articles (latest 6 with high article ratings)
    $featured_articles = new WP_Query(array(
        'post_type' => 'review',
        'post_status' => 'publish',
        'posts_per_page' => 6,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_halacoupon_article_average_rating',
                'value' => 3.5,
                'compare' => '>=',
                'type' => 'DECIMAL'
            ),
            array(
                'key' => '_halacoupon_article_total_ratings',
                'value' => 1,
                'compare' => '>=',
                'type' => 'NUMERIC'
            )
        ),
        'orderby' => 'date',
        'order' => 'DESC'
    ));

    // If no highly rated articles, get latest articles
    if (!$featured_articles->have_posts()) {
        wp_reset_postdata();
        $featured_articles = new WP_Query(array(
            'post_type' => 'review',
            'post_status' => 'publish',
            'posts_per_page' => 6,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
    }

    if (!$featured_articles->have_posts()) {
        // Fallback: Show a simple message or placeholder
        ?>
        <div class="featured-reviews-fallback text-center py-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-white/30">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2"><?php esc_html_e('Reviews Coming Soon', 'halacoupon'); ?></h3>
                <p class="text-gray-600"><?php esc_html_e('We\'re working on bringing you the best reviews. Check back soon!', 'halacoupon'); ?></p>
            </div>
        </div>
        <?php
        return;
    }

    // Generate unique slider ID
    $slider_id = 'featured-reviews-slider-' . wp_rand(1000, 9999);

    // Enqueue Splide assets conditionally
    halacoupon_enqueue_splide_assets();

    ?>
    <div class="featured-reviews-slider-wrapper" dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">
        <div id="<?php echo esc_attr($slider_id); ?>" class="splide featured-reviews-splide">
            <div class="splide__track">
                <ul class="splide__list">
                    <?php
                    $article_index = 0;
                    while ($featured_articles->have_posts()) :
                        $featured_articles->the_post();
                        $article_index++;

                        // Get article data
                        $article_id = get_the_ID();
                        $article_rating = get_post_meta($article_id, '_halacoupon_article_average_rating', true) ?: 0;
                        $article_rating_count = get_post_meta($article_id, '_halacoupon_article_total_ratings', true) ?: 0;
                        $store_id = get_post_meta($article_id, '_halacoupon_review_store_id', true);
                        $author_name = get_the_author();

                        // Get store info
                        $store_name = '';
                        $store_url = '';
                        if ($store_id) {
                            $store = get_term($store_id, 'coupon_store');
                            if ($store && !is_wp_error($store)) {
                                $store_name = $store->name;
                                $store_url = get_term_link($store);
                            }
                        }
                        ?>
                        <li class="splide__slide">
                            <div class="review-slide-card h-full">
                                <?php
                                halacoupon_display_article_card(array(
                                    'article_id' => $article_id,
                                    'article_rating' => $article_rating,
                                    'article_rating_count' => $article_rating_count,
                                    'author_name' => $author_name,
                                    'store_name' => $store_name,
                                    'store_url' => $store_url,
                                    'show_excerpt' => false, // Disable excerpt for this compact view
                                    'show_interactive_rating' => false,
                                ));
                                ?>
                            </div>
                        </li>
                        <?php
                    endwhile;
                    wp_reset_postdata();
                    ?>
                </ul>
            </div>
        </div>
    </div>

    <?php
    // Use the theme's predefined Splide configuration for reviews
    $reviews_config = halacoupon_get_splide_config('reviews-slider');

    // Generate and output the initialization script using the theme's system
    echo halacoupon_generate_splide_script('#' . $slider_id, $reviews_config);

    // Add custom CSS for reviews slider
    ?>
    <style>
    .featured-reviews-splide .splide__slide {
        height: auto;
    }

    .featured-reviews-splide .review-slide-card {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .featured-reviews-splide .splide__arrow {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(235, 191, 67, 0.3);
        color: #EBBF43;
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .featured-reviews-splide .splide__arrow:hover {
        background: #EBBF43;
        color: white;
        border-color: #EBBF43;
    }

    .featured-reviews-splide .splide__pagination__page {
        background: rgba(235, 191, 67, 0.3);
        width: 12px;
        height: 12px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .featured-reviews-splide .splide__pagination__page.is-active {
        background: #000;
        transform: scale(1.2);
    }

    /* Ensure proper spacing and alignment */
    .featured-reviews-slider-wrapper {
        position: relative;
        margin: 0 auto;
    }

    /* Debug styles - remove in production */
    .featured-reviews-splide {
        border-radius: 12px;
        padding: 1rem;
		padding-bottom: 3rem;
    }

	 .featured-reviews-splide .splide__pagination {
		bottom: 1.5em;
	 }



    </style>
    <?php
}

/**
 * Display individual article card for review articles
 *
 * @param array $args Article card arguments
 * @return void
 */
function halacoupon_display_article_card($args = array()) {
    $defaults = array(
        'article_id' => 0,
        'article_rating' => 0,
        'article_rating_count' => 0,
        'author_name' => '',
        'store_name' => '',
        'store_url' => '',
        'show_excerpt' => true,
        'excerpt_length' => 20,
        'show_interactive_rating' => false,
    );

    $args = wp_parse_args($args, $defaults);
    extract($args);

    if (!$article_id) {
        return;
    }

    $is_rtl = is_rtl();
    $article_date = get_the_date('', $article_id);
    $article_content = get_the_content(null, false, $article_id);
    $article_title = get_the_title($article_id);
    $article_url = get_permalink($article_id);
    $featured_image = get_the_post_thumbnail_url($article_id, 'medium_large');

    // Get author avatar
    $author_id = get_post_field('post_author', $article_id);
    $author_avatar = get_avatar_url($author_id, array('size' => 48));
    
    if (!$featured_image) {
        $featured_image = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'; // Transparent pixel
    }

    ?>
    <article class="review-article-card group relative rounded-2xl overflow-hidden shadow-soft hover:shadow-float-lg transition-all duration-300 h-80"
             itemscope itemtype="https://schema.org/Article"
             dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">
        
        <a href="<?php echo esc_url($article_url); ?>" class="absolute inset-0 z-10" aria-label="<?php echo esc_attr($article_title); ?>"></a>

        <!-- Background Image -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary to-secondary">
            <img src="<?php echo esc_url($featured_image); ?>" 
                 alt="" 
                 aria-hidden="true"
                 class="w-full h-full object-cover transition-transform duration-500 ease-out group-hover:scale-105">
        </div>

        <!-- Gradient Overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>

        <!-- Content -->
        <div class="relative z-20 flex flex-col justify-between h-full p-6 text-white">
            
            <!-- Rating and Author -->
            <div class="flex justify-between items-start">
                <!-- Rating Stars -->
                <div class="flex items-center gap-1 bg-black/20 backdrop-blur-md rounded-full px-3 py-1 text-sm">
                    <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span class="font-bold"><?php echo esc_html(number_format($article_rating, 1)); ?></span>
                    <span class="text-white/70">(<?php echo esc_html($article_rating_count); ?>)</span>
                </div>
                <!-- Author Avatar -->
                <?php if ($author_avatar) : ?>
                    <img src="<?php echo esc_url($author_avatar); ?>" alt="<?php echo esc_attr($author_name); ?>" class="w-10 h-10 rounded-full border-2 border-white/50 shadow-md">
                <?php endif; ?>
            </div>

            <!-- Title -->
            <div class="relative">
                <!-- Glass effect background for text -->
                <div class="absolute inset-x-[-1.5rem] inset-y-[-1rem] bg-black/20 backdrop-blur-md group-hover:bg-black/30 group-hover:backdrop-blur-lg rounded-xl transition-all duration-300 ease-out pointer-events-none"></div>

                <h3 class="relative font-bold text-xl leading-tight">
                    <a href="<?php echo esc_url($article_url); ?>" class="pretty-link">
                        <?php echo esc_html($article_title); ?>
                    </a>
                </h3>
            </div>
        </div>
        
        <meta itemprop="headline" content="<?php echo esc_attr($article_title); ?>">
        <span itemprop="author" itemscope itemtype="https://schema.org/Person">
            <meta itemprop="name" content="<?php echo esc_attr($author_name); ?>">
        </span>
    </article>
    <?php
}

// Note: halacoupon_get_reading_time() function is already declared in inc/template-tags.php
