<?php
/**
 * Coupon Functions
 * 
 * Core coupon-related functionality
 * 
 * @package HalaCoupon
 * @subpackage App\Coupons
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get coupon query arguments for store or category pages
 *
 * @param int $term_id The term ID (store or category)
 * @param string $type Coupon type filter ('all', 'code', 'sale')
 * @param array $meta_query Additional meta query parameters
 * @param array $extra_args Additional query arguments
 * @param string $taxonomy Taxonomy name ('coupon_store' or 'coupon_category')
 * @return array WP_Query arguments
 */
if (!function_exists('halacoupon_get_coupon_query_args')) {
    function halacoupon_get_coupon_query_args($term_id, $type = 'all', $meta_query = array(), $extra_args = array(), $taxonomy = 'coupon_store') {
        // Base query arguments
        $args = array(
            'post_type' => 'coupon',
            'post_status' => 'publish',
            'posts_per_page' => 20,
            'orderby' => 'menu_order date',
            'order' => 'DESC',
            'tax_query' => array(
                array(
                    'taxonomy' => $taxonomy,
                    'field'    => 'term_id',
                    'terms'    => $term_id,
                ),
            ),
        );

        // Add coupon type filter if specified
        if ($type !== 'all' && in_array($type, array('code', 'sale'))) {
            $args['meta_query'] = array(
                array(
                    'key'     => '_ags_coupon_type',
                    'value'   => $type,
                    'compare' => '='
                ),
            );
        }

        // Merge additional meta query if provided
        if (!empty($meta_query)) {
            if (isset($args['meta_query'])) {
                $args['meta_query'] = array_merge($args['meta_query'], $meta_query);
            } else {
                $args['meta_query'] = $meta_query;
            }
        }

        // Merge additional arguments
        if (!empty($extra_args)) {
            $args = array_merge($args, $extra_args);
        }

        // Apply filters for customization
        return apply_filters('halacoupon_get_coupon_query_args', $args, $term_id, $type, $meta_query, $extra_args, $taxonomy);
    }
}

// Mark this module as loaded
add_filter('halacoupon_module_coupon_functions_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'coupons/coupon-functions';
    return $modules;
});
