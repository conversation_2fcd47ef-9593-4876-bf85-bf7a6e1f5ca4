<?php
/**
 * Latest Coupons Display
 * 
 * Functions for displaying latest coupons on home page
 * 
 * @package HalaCoupon
 * @subpackage App\Coupons
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get and display latest coupons in modern grid layout
 *
 * Displays latest 20 coupons in a 4-per-row grid (5 columns) with simplified cards
 *
 * @param int $limit Number of coupons to display (default: 20)
 * @param bool $show_all_types Whether to show all coupon types or just codes (default: true)
 * @return void
 */
if (!function_exists('get_latest_coupons')) {
    function get_latest_coupons($limit = 20, $show_all_types = true) {
        // Build query arguments for latest coupons
        $args = [
            'post_type'      => 'coupon',
            'posts_per_page' => absint($limit),
            'orderby'        => 'date',
            'order'          => 'DESC',
            'post_status'    => 'publish',
            'no_found_rows'  => true, // Performance optimization
            'update_post_meta_cache' => true,
            'update_post_term_cache' => true,
        ];

        // Add meta query to filter by coupon type if needed
        if (!$show_all_types) {
            $args['meta_query'] = [
                [
                    'key'     => '_ags_coupon_type',
                    'value'   => 'code',
                    'compare' => '='
                ],
            ];
        }

        // Execute the query
        $coupon_query = new WP_Query($args);

        if (!$coupon_query->have_posts()) {
            halacoupon_render_no_coupons_message();
            return;
        }

        // Render the coupons grid
        halacoupon_render_coupons_grid($coupon_query, $limit);

        // Clean up
        wp_reset_postdata();
    }
}

/**
 * Render coupons in a modern grid layout
 *
 * @param WP_Query $coupon_query The coupon query object
 * @param int $limit Number of coupons being displayed
 * @return void
 */
if (!function_exists('halacoupon_render_coupons_grid')) {
    function halacoupon_render_coupons_grid($coupon_query, $limit) {
        $total_coupons = $coupon_query->found_posts;
        $is_rtl = is_rtl();

        ?>
        <!-- Latest Coupons Grid Container -->
        <section class="latest-coupons-grid relative"
                 role="region"
                 aria-label="<?php esc_attr_e('Latest coupon offers', 'halacoupon'); ?>"
                 dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">

            <!-- Responsive Grid Layout -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 lg:gap-6"
                 role="list"
                 aria-label="<?php esc_attr_e('Coupon offers grid', 'halacoupon'); ?>">

                <?php
                $coupon_index = 0;
                while ($coupon_query->have_posts()) :
                    $coupon_query->the_post();
                    $coupon_index++;

                    // Setup coupon data
                    halacoupon_setup_coupon();

                    // Render individual coupon card
                    ?>
                    <div class="coupon-grid-item animate-fade-in-up"
                         style="animation-delay: <?php echo ($coupon_index * 0.1); ?>s;"
                         role="listitem">
                        <?php get_template_part('loop/loop-today-coupon'); ?>
                    </div>
                    <?php
                endwhile;
                ?>

            </div>

            <!-- Load More Section (if needed) -->
            <?php if ($total_coupons > $limit) : ?>
            <div class="text-center mt-8 lg:mt-12">
                <a href="<?php echo esc_url(get_post_type_archive_link('coupon')); ?>"
                   class="inline-flex items-center gap-3 bg-gradient-to-r from-primary to-secondary text-white px-8 py-3 rounded-xl font-semibold shadow-float hover:shadow-float-lg transform hover:scale-105 transition-all duration-300">
                    <span><?php esc_html_e('Explore All Coupons', 'halacoupon'); ?></span>
                    <svg class="w-5 h-5 <?php echo $is_rtl ? 'rotate-180' : ''; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                </a>
            </div>
            <?php endif; ?>

            <!-- Schema Markup for SEO -->
            <script type="application/ld+json">
            {
                "@context": "https://schema.org",
                "@type": "ItemList",
                "name": "<?php echo esc_js(__('Latest Coupon Offers', 'halacoupon')); ?>",
                "description": "<?php echo esc_js(__('Latest coupon codes and deals from top stores', 'halacoupon')); ?>",
                "numberOfItems": <?php echo absint(min($limit, $total_coupons)); ?>,
                "url": "<?php echo esc_js(home_url('/')); ?>"
            }
            </script>
        </section>
        <?php
    }
}

/**
 * Render no coupons available message
 *
 * @return void
 */
if (!function_exists('halacoupon_render_no_coupons_message')) {
    function halacoupon_render_no_coupons_message() {
        ?>
        <div class="no-coupons-message text-center py-12 lg:py-16">
            <div class="max-w-md mx-auto">
                <!-- Icon -->
                <div class="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                </div>

                <!-- Message -->
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                    <?php esc_html_e('No Coupons Available', 'halacoupon'); ?>
                </h3>
                <p class="text-gray-600 mb-6">
                    <?php esc_html_e('We are working hard to bring you the best deals. Check back soon for amazing offers!', 'halacoupon'); ?>
                </p>

                <!-- CTA Button -->
                <a href="<?php echo esc_url(home_url('/')); ?>"
                   class="inline-flex items-center gap-2 bg-gradient-to-r from-primary to-secondary text-white px-6 py-3 rounded-lg font-medium hover:from-secondary hover:to-primary transition-all duration-300 transform hover:scale-105">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    <span><?php esc_html_e('Back to Home', 'halacoupon'); ?></span>
                </a>
            </div>
        </div>
        <?php
    }
}

/**
 * Get latest coupons with advanced filtering options
 *
 * @param array $args Custom query arguments
 * @return WP_Query
 */
if (!function_exists('halacoupon_get_latest_coupons_query')) {
    function halacoupon_get_latest_coupons_query($args = []) {
        $default_args = [
            'post_type'      => 'coupon',
            'posts_per_page' => 20,
            'orderby'        => 'date',
            'order'          => 'DESC',
            'post_status'    => 'publish',
            'no_found_rows'  => false,
            'update_post_meta_cache' => true,
            'update_post_term_cache' => true,
        ];

        $query_args = wp_parse_args($args, $default_args);

        // Apply filters for customization
        $query_args = apply_filters('halacoupon_latest_coupons_query_args', $query_args);

        return new WP_Query($query_args);
    }
}

/**
 * Display latest coupons with custom parameters (shortcode support)
 *
 * @param array $atts Shortcode attributes
 * @return string
 */
if (!function_exists('halacoupon_latest_coupons_shortcode')) {
    function halacoupon_latest_coupons_shortcode($atts = []) {
        $atts = shortcode_atts([
            'limit' => 20,
            'type' => 'all', // 'all', 'code', 'sale'
            'store' => '',
            'category' => '',
            'class' => '',
        ], $atts, 'latest_coupons');

        ob_start();

        // Build query args based on shortcode attributes
        $query_args = [];

        if ($atts['type'] !== 'all') {
            $query_args['meta_query'] = [
                [
                    'key'     => '_ags_coupon_type',
                    'value'   => sanitize_text_field($atts['type']),
                    'compare' => '='
                ]
            ];
        }

        if (!empty($atts['store'])) {
            $query_args['tax_query'] = [
                [
                    'taxonomy' => 'coupon_store',
                    'field'    => 'slug',
                    'terms'    => sanitize_text_field($atts['store'])
                ]
            ];
        }

        if (!empty($atts['category'])) {
            if (!isset($query_args['tax_query'])) {
                $query_args['tax_query'] = [];
            }
            $query_args['tax_query']['relation'] = 'AND';
            $query_args['tax_query'][] = [
                'taxonomy' => 'coupon_category',
                'field'    => 'slug',
                'terms'    => sanitize_text_field($atts['category'])
            ];
        }

        // Add custom class wrapper
        if (!empty($atts['class'])) {
            echo '<div class="' . esc_attr($atts['class']) . '">';
        }

        // Get and display coupons
        $coupon_query = halacoupon_get_latest_coupons_query($query_args);

        if ($coupon_query->have_posts()) {
            halacoupon_render_coupons_grid($coupon_query, absint($atts['limit']));
        } else {
            halacoupon_render_no_coupons_message();
        }

        wp_reset_postdata();

        if (!empty($atts['class'])) {
            echo '</div>';
        }

        return ob_get_clean();
    }
}

// Register shortcode
add_shortcode('latest_coupons', 'halacoupon_latest_coupons_shortcode');

/**
 * Add custom CSS for latest coupons grid animations
 *
 * @return void
 */
if (!function_exists('halacoupon_latest_coupons_styles')) {
    function halacoupon_latest_coupons_styles() {
        $custom_css = "
        /* Latest Coupons Grid Animations */
        .latest-coupons-grid .coupon-grid-item {
            opacity: 0;
            transform: translateY(20px);
        }

        .latest-coupons-grid .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Grid responsive enhancements */
        .latest-coupons-grid .grid {
            gap: 1rem;
        }

        @media (min-width: 1024px) {
            .latest-coupons-grid .grid {
                gap: 1.5rem;
            }
        }

        /* Hover effects for grid items */
        .latest-coupons-grid .coupon-grid-item:hover {
            z-index: 10;
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .latest-coupons-grid .animate-fade-in-up,
            .latest-coupons-grid .coupon-grid-item {
                animation: none !important;
                opacity: 1 !important;
                transform: none !important;
            }
        }

        /* Loading state */
        .latest-coupons-grid.loading .coupon-grid-item {
            opacity: 0.5;
            pointer-events: none;
        }
        ";

        wp_add_inline_style('halacoupon-main', $custom_css);
    }
}

// Hook to add custom styles
add_action('wp_enqueue_scripts', 'halacoupon_latest_coupons_styles', 20);

/**
 * Cache latest coupons query for performance
 *
 * @param array $args Query arguments
 * @return WP_Query
 */
if (!function_exists('halacoupon_get_cached_latest_coupons')) {
    function halacoupon_get_cached_latest_coupons($args = []) {
        $cache_key = 'halacoupon_latest_coupons_' . md5(serialize($args));
        $cached_query = wp_cache_get($cache_key, 'halacoupon_coupons');

        if (false === $cached_query) {
            $cached_query = halacoupon_get_latest_coupons_query($args);

            // Cache for 15 minutes
            wp_cache_set($cache_key, $cached_query, 'halacoupon_coupons', 15 * MINUTE_IN_SECONDS);
        }

        return $cached_query;
    }
}

/**
 * Clear latest coupons cache when coupon is updated
 *
 * @param int $post_id Post ID
 * @return void
 */
if (!function_exists('halacoupon_clear_latest_coupons_cache')) {
    function halacoupon_clear_latest_coupons_cache($post_id) {
        if (get_post_type($post_id) === 'coupon') {
            wp_cache_flush_group('halacoupon_coupons');
        }
    }
}

// Hook to clear cache when coupons are updated
add_action('save_post', 'halacoupon_clear_latest_coupons_cache');
add_action('delete_post', 'halacoupon_clear_latest_coupons_cache');

/**
 * Add structured data for latest coupons
 *
 * @return void
 */
if (!function_exists('halacoupon_latest_coupons_structured_data')) {
    function halacoupon_latest_coupons_structured_data() {
        if (!is_front_page()) {
            return;
        }

        $coupons_query = halacoupon_get_latest_coupons_query(['posts_per_page' => 5]);

        if (!$coupons_query->have_posts()) {
            return;
        }

        $structured_data = [
            '@context' => 'https://schema.org',
            '@type' => 'ItemList',
            'name' => get_bloginfo('name') . ' - ' . __('Latest Coupons', 'halacoupon'),
            'description' => __('Latest coupon codes and deals from top stores', 'halacoupon'),
            'url' => home_url('/'),
            'itemListElement' => []
        ];

        $position = 1;
        while ($coupons_query->have_posts()) {
            $coupons_query->the_post();
            halacoupon_setup_coupon();

            $structured_data['itemListElement'][] = [
                '@type' => 'ListItem',
                'position' => $position,
                'item' => [
                    '@type' => 'Offer',
                    'name' => get_the_title(),
                    'description' => wp_strip_all_tags(get_the_excerpt()),
                    'url' => get_permalink(),
                    'seller' => [
                        '@type' => 'Organization',
                        'name' => halacoupon_store()->get_display_name()
                    ]
                ]
            ];

            $position++;
        }

        wp_reset_postdata();

        echo '<script type="application/ld+json">' . wp_json_encode($structured_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";
    }
}

// Add structured data to wp_head
add_action('wp_head', 'halacoupon_latest_coupons_structured_data', 30);

// Mark this module as loaded
add_filter('halacoupon_module_latest_coupons_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'coupons/latest-coupons';
    return $modules;
});

// Add action hook for theme customization
do_action('halacoupon_latest_coupons_loaded');
