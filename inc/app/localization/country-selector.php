<?php
/**
 * Country Selector
 * 
 * Country/region selector for multisite setup
 * 
 * @package HalaCoupon
 * @subpackage App\Localization
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Output country dropdown selector
 */
if (!function_exists('output_country_dropdown')) {
    function output_country_dropdown() {
        if (!is_multisite()) {
            return;
        }

        $host = esc_attr($_SERVER['HTTP_HOST']); // Sanitize the host for security

        $sites = get_sites([
            'public' => 1,
            'archived' => 0,
            'deleted' => 0,
        ]);

        $subdomain_country_map = [];

        foreach ( $sites as $site ) {
            $domain = $site->domain;
            $country_code = strtoupper( get_blog_option( $site->blog_id, 'country_code', explode('.', $domain)[0] ) );
            $subdomain_country_map[$domain] = $country_code;
        }

        // Caching country code for the subdomain
        $transient_key = 'current_country_code_' . md5($host);
        $current_country_code = get_transient($transient_key);

        if ($current_country_code === false) {
            $current_country_code = isset($subdomain_country_map[$host]) ? $subdomain_country_map[$host] : 'SA';
            set_transient($transient_key, $current_country_code, 31536000); // Cache for a year
        }

        // Country flags mapping
        $country_flags = [
            'SA' => 'sa',
            'EG' => 'eg',
            'BH' => 'bh',
            'JO' => 'jo',
            'KW' => 'kw',
            'MA' => 'ma',
            'OM' => 'om',
            'QA' => 'qa',
            'AE' => 'ae',
        ];

        ?>
        <div class="country-selector-wrapper" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
            <button class="selected-country-flag group relative flex items-center justify-center w-12 h-12 rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 hover:bg-white/90 hover:border-primary/20 transition-all duration-300 hover:scale-105 shadow-soft hover:shadow-glow" aria-label="<?php esc_attr_e('Select country', 'halacoupon'); ?>" aria-expanded="false">
                <i class="<?php echo esc_attr(isset($country_flags[$current_country_code]) ? $country_flags[$current_country_code] : strtolower($current_country_code)); ?> flag text-lg"></i>
                <span class="country-code-desktop hidden lg:inline-block <?php echo is_rtl() ? 'mr-2' : 'ml-2'; ?> text-sm font-medium text-gray-700 group-hover:text-primary transition-colors duration-300"><?php echo esc_html($current_country_code); ?></span>
                <svg class="w-3 h-3 text-gray-500 group-hover:text-primary transition-colors duration-300 <?php echo is_rtl() ? 'mr-1' : 'ml-1'; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>

            <div class="country-popup absolute top-full <?php echo is_rtl() ? 'left-0' : 'right-0'; ?> mt-3 bg-white/95 backdrop-blur-xl rounded-2xl shadow-float-lg border border-white/30 py-3 min-w-32 z-50 opacity-0 invisible transition-all duration-300" id="country-popup">
                <div class="popup-content space-y-1">
                    <?php foreach ($subdomain_country_map as $domain => $country_code) : ?>
                        <div class="popup-item group flex items-center px-4 py-3 cursor-pointer hover:bg-primary/10 hover:text-primary rounded-xl transition-all duration-300 <?php echo $domain === $host ? 'bg-primary/10 text-primary pointer-events-none' : (is_rtl() ? 'hover:-translate-x-1' : 'hover:translate-x-1'); ?>" data-url="https://<?php echo esc_attr($domain); ?>">
                            <i class="<?php echo esc_attr(isset($country_flags[$country_code]) ? $country_flags[$country_code] : strtolower($country_code)); ?> flag text-lg <?php echo is_rtl() ? 'ml-3' : 'mr-3'; ?>"></i>
                            <span class="text-sm text-black/80 group-hover:text-primary transition-colors duration-300 font-medium"><?php echo esc_html($country_code); ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <script>
            jQuery(document).ready(function($) {
                const $wrapper = $('.country-selector-wrapper');
                const $button = $('.selected-country-flag');
                const $popup = $('#country-popup');

                // Toggle country popup with modern animations
                $button.on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const isVisible = !$popup.hasClass('opacity-0');

                    if (isVisible) {
                        // Hide popup
                        $popup.addClass('opacity-0 invisible').removeClass('opacity-100 visible');
                        $button.attr('aria-expanded', 'false');
                    } else {
                        // Show popup
                        $popup.removeClass('opacity-0 invisible').addClass('opacity-100 visible');
                        $button.attr('aria-expanded', 'true');
                    }
                });

                // Handle country selection
                $('.popup-item').on('click', function(e) {
                    e.preventDefault();
                    const url = $(this).data('url');
                    if (url) {
                        // Add loading state
                        $(this).addClass('opacity-50 pointer-events-none');
                        window.location.href = url;
                    }
                });

                // Close popup when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('.country-selector-wrapper').length) {
                        $popup.addClass('opacity-0 invisible').removeClass('opacity-100 visible');
                        $button.attr('aria-expanded', 'false');
                    }
                });

                // Close popup on Escape key
                $(document).on('keydown', function(e) {
                    if (e.key === 'Escape') {
                        $popup.addClass('opacity-0 invisible').removeClass('opacity-100 visible');
                        $button.attr('aria-expanded', 'false');
                        $button.focus();
                    }
                });
            });
        </script>

        <style>
            /* Modern Country Selector Styles with RTL Support */
            .country-selector-wrapper {
                position: relative;
                display: inline-block;
            }

            /* Enhanced button styling for glass effect integration */
            .selected-country-flag {
                outline: none;
                border: none;
                background: none;
                cursor: pointer;
            }

            /* Ensure flag icons display properly */
            .selected-country-flag .flag {
                display: inline-block;
                width: 1.2em;
                height: auto;
            }

            /* RTL support for country code text */
            [dir="rtl"] .country-code-desktop {
                margin-right: 0;
                margin-left: 0.5rem;
            }

            [dir="ltr"] .country-code-desktop {
                margin-left: 0.5rem;
                margin-right: 0;
            }

            /* RTL support for dropdown arrow */
            [dir="rtl"] .selected-country-flag svg {
                margin-right: 0.25rem;
                margin-left: 0;
            }

            [dir="ltr"] .selected-country-flag svg {
                margin-left: 0.25rem;
                margin-right: 0;
            }

            /* Enhanced popup styling */
            .country-popup {
                max-height: 300px;
                overflow-y: auto;
                scrollbar-width: thin;
                scrollbar-color: rgba(213, 96, 48, 0.3) transparent;
            }

            .country-popup::-webkit-scrollbar {
                width: 4px;
            }

            .country-popup::-webkit-scrollbar-track {
                background: transparent;
            }

            .country-popup::-webkit-scrollbar-thumb {
                background: rgba(213, 96, 48, 0.3);
                border-radius: 2px;
            }

            .country-popup::-webkit-scrollbar-thumb:hover {
                background: rgba(213, 96, 48, 0.5);
            }

            /* RTL support for popup items */
            [dir="rtl"] .popup-item {
                flex-direction: row-reverse;
            }

            [dir="rtl"] .popup-item .flag {
                margin-left: 0.75rem;
                margin-right: 0;
            }

            [dir="ltr"] .popup-item .flag {
                margin-right: 0.75rem;
                margin-left: 0;
            }

            /* Mobile responsive adjustments */
            @media screen and (max-width: 1023px) {
                .country-code-desktop {
                    display: none !important;
                }

                .selected-country-flag {
                    width: 3rem !important;
                    padding: 0.75rem !important;
                }

                .country-popup {
                    min-width: 8rem;
                }
            }

            /* High contrast mode support */
            @media (prefers-contrast: high) {
                .selected-country-flag {
                    border: 2px solid #000 !important;
                    background: #fff !important;
                }

                .country-popup {
                    border: 2px solid #000 !important;
                    background: #fff !important;
                }
            }
        </style>
        <?php
    }
}

// Mark this module as loaded
add_filter('halacoupon_module_country_selector_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'localization/country-selector';
    return $modules;
});
?>
