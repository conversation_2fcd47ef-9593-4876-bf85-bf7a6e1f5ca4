<?php
/**
 * Hreflang Tags
 * 
 * SEO hreflang tags for multisite/multilingual setup
 * 
 * @package HalaCoupon
 * @subpackage App\Localization
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Output hreflang tags for SEO
 */
if (!function_exists('output_hreflang_tags')) {
    function output_hreflang_tags() {
        if (!is_multisite()) {
            return; 
        }
        
        // Define subdomains and their corresponding hreflang codes
        $country_subdomains = [
            'https://saudi.halacoupon.com'   => 'ar-SA',
            'https://bahrain.halacoupon.com' => 'ar-BH',
            'https://egypt.halacoupon.com'   => 'ar-EG',
            'https://jordan.halacoupon.com'  => 'ar-JO',
            'https://kuwait.halacoupon.com'  => 'ar-KW',
            'https://morocco.halacoupon.com' => 'ar-MA',
            'https://oman.halacoupon.com'    => 'ar-OM',
            'https://qatar.halacoupon.com'   => 'ar-QA',
            'https://uae.halacoupon.com'     => 'ar-AE'
        ];

        // Get the current URL path
        $current_path = esc_attr($_SERVER['REQUEST_URI']);

        // Output hreflang for general Arabic (ar) only for Saudi subdomain
        echo '<link rel="alternate" href="https://saudi.halacoupon.com' . esc_url($current_path) . '" hreflang="ar" />' . "\n";

        // Loop through the subdomains and generate region-specific hreflang tags
        foreach ($country_subdomains as $url => $lang_code) {
            if ($url !== 'https://saudi.halacoupon.com') {
                // Output the region-specific hreflang without the general "ar" duplication
                echo '<link rel="alternate" href="' . esc_url($url . $current_path) . '" hreflang="' . esc_attr($lang_code) . '" />' . "\n";
            }
        }

        // Add x-default for Saudi Arabia as the default domain
        echo '<link rel="alternate" href="https://saudi.halacoupon.com' . esc_url($current_path) . '" hreflang="x-default" />' . "\n";
    }
}

// Hook the function to wp_head
add_action('wp_head', 'output_hreflang_tags');

// Mark this module as loaded
add_filter('halacoupon_module_hreflang_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'localization/hreflang';
    return $modules;
});












