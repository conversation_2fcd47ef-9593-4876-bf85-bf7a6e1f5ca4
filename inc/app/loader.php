<?php
/**
 * App Loader - Template Tags Modular System
 * 
 * This file loads all modular template tag files from the app directory.
 * Replaces the monolithic inc/template-tags.php file with organized modules.
 * 
 * @package HalaCoupon
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get the app directory path
 */
function halacoupon_get_app_path($file = '') {
    $app_path = get_template_directory() . '/inc/app/';
    return $file ? $app_path . ltrim($file, '/') : $app_path;
}

/**
 * Load a specific app module
 */
function halacoupon_load_app_module($module_path) {
    $file_path = halacoupon_get_app_path($module_path);
    
    if (file_exists($file_path)) {
        require_once $file_path;
        return true;
    }
    
    // Log error in debug mode
    if (WP_DEBUG) {
        error_log("HalaCoupon App Module not found: {$file_path}");
    }
    
    return false;
}

/**
 * Load all app modules in the correct order
 */
function halacoupon_load_app_modules() {
    // Define loading order - utilities first, then others
    $modules = [
        // Core utilities (load first)
        'utilities/helpers.php',
        'utilities/counters.php',
        'utilities/splide-manager.php',
        
        // Navigation and structure
        'navigation/breadcrumbs.php',
        'pagination/pagination.php',
        
        // Content display
        'sliders/home-slider.php',
        'sliders/offer-slider.php',
        'sliders/category-slider.php',
        'sliders/featured-stores-slider.php',
        
        // Blog
        'blog/latest-blogs.php',
        
        // Stores and coupons
        'stores/store-functions.php',
        'stores/store-grid.php',
        'store-social-links-display.php',
        'coupons/coupon-functions.php',
        'coupons/latest-coupons.php',
        
        // Interactive features
        'ajax/store-filters.php',
        'forms/contact-form.php',
        'forms/faqs.php',
        
        // Social and localization
        'social/social-profiles.php',
        'localization/country-selector.php',
        // 'localization/hreflang.php',
    ];
    
    // Load each module
    foreach ($modules as $module) {
        halacoupon_load_app_module($module);
    }
    
    // Hook for additional modules
    do_action('halacoupon_app_modules_loaded');
}

/**
 * Initialize the app system
 */
function halacoupon_init_app() {
    // Load all modules
    halacoupon_load_app_modules();
    
    // Add any initialization hooks here
    do_action('halacoupon_app_initialized');
}

// Initialize the app system
halacoupon_init_app();

/**
 * Helper function to check if a module is loaded
 */
function halacoupon_is_module_loaded($module_name) {
    return apply_filters("halacoupon_module_{$module_name}_loaded", false);
}

/**
 * Get app module info
 */
function halacoupon_get_app_info() {
    return [
        'version' => '1.0.0',
        'modules_path' => halacoupon_get_app_path(),
        'loaded_modules' => apply_filters('halacoupon_loaded_modules', []),
    ];
}
