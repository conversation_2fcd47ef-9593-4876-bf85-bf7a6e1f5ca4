<?php
/**
 * Splide.js Asset Manager
 * 
 * Manages conditional loading of Splide.js slider library
 * Replaces OWL Carousel with modern, lightweight alternative
 * 
 * @package HalaCoupon
 * @subpackage App\Utilities
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Track if Splide assets are enqueued to prevent duplicates
 */
global $halacoupon_splide_enqueued;
$halacoupon_splide_enqueued = false;

/**
 * Conditionally enqueue Splide.js assets
 * Only loads when sliders are actually rendered
 */
if (!function_exists('halacoupon_enqueue_splide_assets')) {
    function halacoupon_enqueue_splide_assets() {
        global $halacoupon_splide_enqueued;
        
        // Prevent duplicate loading
        if ($halacoupon_splide_enqueued) {
            return;
        }
        
        $theme_version = wp_get_theme()->get('Version');
        
        // Enqueue Splide CSS
        wp_enqueue_style(
            'halacoupon-splide-css',
            get_template_directory_uri() . '/assets/css/splide.min.css',
            [],
            $theme_version
        );
        
        // Enqueue Splide JS
        wp_enqueue_script(
            'halacoupon-splide-js',
            get_template_directory_uri() . '/assets/js/splide.min.js',
            ['jquery'],
            $theme_version,
            true
        );
        
        // Enqueue custom Splide styles
        wp_enqueue_style(
            'halacoupon-splide-custom',
            get_template_directory_uri() . '/assets/css/splide-custom.css',
            ['halacoupon-splide-css'],
            $theme_version
        );
        
        $halacoupon_splide_enqueued = true;
        
        // Add action hook for other components
        do_action('halacoupon_splide_assets_enqueued');
        
        if (WP_DEBUG) {
            error_log('HalaCoupon: Splide assets enqueued conditionally');
        }
    }
}

/**
 * Check if Splide assets are loaded
 */
if (!function_exists('halacoupon_is_splide_loaded')) {
    function halacoupon_is_splide_loaded() {
        global $halacoupon_splide_enqueued;
        return $halacoupon_splide_enqueued;
    }
}

/**
 * Get Splide configuration for different slider types
 */
if (!function_exists('halacoupon_get_splide_config')) {
    function halacoupon_get_splide_config($type = 'default') {
		$is_rtl = is_rtl() ? 'rtl' : 'ltr';
        $configs = [
            'home-slider' => [
                'type' => 'loop',
                'direction' => $is_rtl,
                'autoplay' => true,
                'interval' => 5000,
                'pauseOnHover' => true,
                'pauseOnFocus' => true,
                'arrows' => true,
                'pagination' => true,
                'drag' => true,
                'keyboard' => true,
                'accessibility' => ['autoplay' => true],
                'breakpoints' => [
                    768 => ['arrows' => false]
                ]
            ],
            'category-slider' => [
                'type' => 'loop',
                'direction' => $is_rtl,
                'autoplay' => true,
                'interval' => 4000,
                'pauseOnHover' => true,
                'arrows' => true,
                'pagination' => false,
                'perPage' => 6,
                'perMove' => 1,
                'gap' => '0.265rem',
                'breakpoints' => [
                    1024 => ['perPage' => 6],
                    768 => ['perPage' => 3],
                    480 => ['perPage' => 2]
                ]
            ],
            'store-slider' => [
                'type' => 'loop',
                'direction' => $is_rtl,
                'autoplay' => true,
                'interval' => 4500,
                'pauseOnHover' => true,
                'arrows' => true,
                'pagination' => false,
                'perPage' => 5,
                'perMove' => 1,
                'gap' => '0',
                'breakpoints' => [
                    1200 => ['perPage' => 3],
                    768 => ['perPage' => 2],
                    480 => ['perPage' => 1]
                ]
            ],
            'reviews-slider' => [
                'type' => 'loop',
				'direction' => $is_rtl,
                'autoplay' => true,
                'interval' => 4000,
                'pauseOnHover' => true,
                'pauseOnFocus' => true,
                'arrows' => true,
                'pagination' => true,
                'perPage' => 3,
                'perMove' => 1,
                'gap' => '2rem',
                'breakpoints' => [
                    1280 => ['perPage' => 3, 'gap' => '1.5rem'],
                    1024 => ['perPage' => 2, 'gap' => '1.5rem'],
                    768 => ['perPage' => 1, 'gap' => '1rem']
                ]
            ],
            'offer-slider' => [
                'type' => 'loop',
                'direction' => $is_rtl,
                'autoplay' => true,
                'interval' => 3000,
                'pauseOnHover' => true,
                'arrows' => false,
                'pagination' => false,
                'perPage' => 3,
                'perMove' => 1
            ]
        ];
        
        return isset($configs[$type]) ? $configs[$type] : $configs['default'] ?? [];
    }
}

/**
 * Generate Splide initialization script
 */
if (!function_exists('halacoupon_generate_splide_script')) {
    function halacoupon_generate_splide_script($selector, $config = []) {
        $config_json = json_encode($config, JSON_UNESCAPED_SLASHES);
        
        return "
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof Splide !== 'undefined') {
                try {
                    new Splide('{$selector}', {$config_json}).mount();
                    console.log('✅ Splide initialized: {$selector}');
                } catch (error) {
                    console.error('❌ Splide initialization failed for {$selector}:', error);
                }
            } else {
                console.error('❌ Splide library not loaded for {$selector}');
            }
        });
        </script>";
    }
}


// Mark this module as loaded
add_filter('halacoupon_module_splide_manager_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'utilities/splide-manager';
    return $modules;
});
