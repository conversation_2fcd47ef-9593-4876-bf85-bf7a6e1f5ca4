<?php
/**
 * Counter and Statistics Functions
 * 
 * Functions for counting coupons, stores, and other statistics
 * 
 * @package HalaCoupon
 * @subpackage App\Utilities
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Ensure MINUTE_IN_SECONDS is defined (for non-standard WP contexts)
if ( ! defined('MINUTE_IN_SECONDS') ) define('MINUTE_IN_SECONDS', 60);

/**
 * Get coupon counts by type for a specific term
 */
function halacoupon_get_coupon_counts_by_type($id, $taxonomy = 'coupon_store') {
    $types = halacoupon_get_coupon_types();
    $counts = array('all' => 0);

    foreach ($types as $type_key => $type_name) {
        $args = array(
            'post_type' => 'coupon',
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_ags_coupon_type',
                    'value' => $type_key,
                    'compare' => '='
                ),
            ),
            'tax_query' => array(
                array(
                    'taxonomy' => $taxonomy,
                    'field'    => 'term_id',
                    'terms'    => $id,
                ),
            ),
        );

        $query = new WP_Query($args);
        $counts[$type_key] = $query->found_posts;
        $counts['all'] += $query->found_posts;
        wp_reset_postdata();
    }

    return $counts;
}

/**
 * Get total coupon uses today for a store term (optimized, cached)
 *
 * @param int $term_id Store term ID (coupon_store)
 * @return int Total uses today
 * @note Requires WordPress context (get_transient, set_transient, get_post_meta, current_time, halacoupon_get_store_coupons)
 */
function get_total_used_today_for_term($term_id) {
    if (!function_exists('get_transient') || !function_exists('set_transient') || !function_exists('get_post_meta') || !function_exists('current_time') || !function_exists('halacoupon_get_store_coupons')) {
        return 0; // Defensive: required WP functions missing
    }
    $today = current_time('Y-m-d');
    // No caching: always calculate real-time
    $coupons = halacoupon_get_store_coupons($term_id, -1);
    if (empty($coupons)) {
        return 0;
    }
    $total = 0;
    foreach ($coupons as $coupon_post) {
        $coupon_id = is_object($coupon_post) ? $coupon_post->ID : intval($coupon_post);
        $today_meta = get_post_meta($coupon_id, '_ags_today', true);
        if (is_array($today_meta) && isset($today_meta['date'], $today_meta['used'])) {
            if ($today_meta['date'] === $today) {
                $total += intval($today_meta['used']);
            }
        }
    }
    return $total;
}

// Mark this module as loaded
add_filter('halacoupon_module_counters_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'utilities/counters';
    return $modules;
});
