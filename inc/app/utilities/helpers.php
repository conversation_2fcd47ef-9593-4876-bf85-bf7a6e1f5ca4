<?php
/**
 * Utility Helper Functions
 * 
 * General helper functions used across the theme
 * 
 * @package HalaCoupon
 * @subpackage App\Utilities
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * HTML5 shim for IE9
 */
function halacoupon_theme_html5() {
    echo '<!--[if lt IE 9]>';
    echo '<script src="' . esc_url(get_template_directory_uri() . '/js/html5.js') . '"></script>';
    echo '<![endif]-->';
}
add_action('wp_head', 'halacoupon_theme_html5');

/**
 * Get total published coupons count
 */
if (!function_exists('halacoupon_get_total_published_coupons')) {
    function halacoupon_get_total_published_coupons() {
        // Define the query arguments to count published coupons
        $args = array(
            'post_type'      => 'coupon',
            'post_status'    => 'publish',
            'posts_per_page' => -1,
            'fields'         => 'ids',
        );

        // Execute the query to get the total count of published coupons
        $query = new WP_Query($args);
        $total_count = $query->found_posts;

        // Reset the post data to avoid conflicts with other queries
        wp_reset_postdata();

        // Return the total count
        return $total_count;
    }
}

/**
 * Get total published stores count
 */
if (!function_exists('halacoupon_get_total_published_stores')) {
    function halacoupon_get_total_published_stores() {
        // Define the query arguments to count terms in the 'coupon_store' taxonomy
        $args = array(
            'taxonomy'   => 'coupon_store',
            'hide_empty' => false,
        );

        // Get the terms in the 'coupon_store' taxonomy
        $stores = get_terms($args);

        // Check if the result is an array and not a WP_Error
        if (is_array($stores)) {
            $total_count = count($stores);
        } else {
            $total_count = 0;
        }

        // Return the total count
        return $total_count;
    }
}

/**
 * Get total published categories count
 */
if (!function_exists('halacoupon_get_total_published_categories')) {
    function halacoupon_get_total_published_categories() {
        // Define the query arguments to count terms in the 'coupon_category' taxonomy
        $args = array(
            'taxonomy'   => 'coupon_category',
            'hide_empty' => false,
        );

        // Get the terms in the 'coupon_category' taxonomy
        $categories = get_terms($args);

        // Check if the result is an array and not a WP_Error
        if (is_array($categories)) {
            $total_count = count($categories);
        } else {
            $total_count = 0;
        }

        // Return the total count
        return $total_count;
    }
}



/**
 * Display coupon/store thumbnail
 *
 * This function outputs the thumbnail for the current coupon or its store.
 * It's a wrapper around the coupon object's get_thumb() method.
 *
 * @param bool $placeholder Whether to show placeholder if no thumbnail
 * @param string $size Image size to display
 */
if (!function_exists('halacoupon_thumb')) {
    function halacoupon_thumb($placeholder = false, $size = 'halacoupon_medium-thumb') {
        // Check if we have a coupon object
        if (function_exists('halacoupon_coupon') && halacoupon_coupon()) {
            echo halacoupon_coupon()->get_thumb($size, $placeholder);
        } else {
            // Fallback: try to get post thumbnail
            if (has_post_thumbnail()) {
                echo get_the_post_thumbnail(get_the_ID(), $size, array(
                    'class' => 'ui Tiny circular image',
                    'loading' => 'lazy',
                    'width' => '80px',
                    'height' => '80px',
                ));
            } elseif ($placeholder) {
                // Show placeholder if requested
                echo '<div class="placeholder-thumb ui Tiny circular image" style="width: 80px; height: 80px; background: #f0f0f0; display: flex; align-items: center; justify-content: center; border-radius: 50%;">';
                echo '<i class="image icon" style="color: #ccc;"></i>';
                echo '</div>';
            }
        }
    }
}

/**
 * Get current month information
 *
 * @param string $format The format to return (current_day_full, current_month, etc.)
 * @return string Formatted date information
 */
if (!function_exists('get_current_month_info')) {
    function get_current_month_info($format = 'current_day_full') {
        // Set timezone to WordPress timezone
        $timezone = wp_timezone();
        $current_date = new DateTime('now', $timezone);

        switch ($format) {
            case 'current_day_full':
                // Return full date format like "December 17, 2024"
                return $current_date->format('F j, Y');

            case 'current_month':
                // Return current month name
                return $current_date->format('F');

            case 'current_day':
                // Return current day number
                return $current_date->format('j');

            case 'current_year':
                // Return current year
                return $current_date->format('Y');

            case 'current_month_year':
                // Return month and year like "December 2024"
                return $current_date->format('F Y');

            case 'current_date_short':
                // Return short date format like "Dec 17, 2024"
                return $current_date->format('M j, Y');

            case 'current_weekday':
                // Return current weekday name
                return $current_date->format('l');

            case 'timestamp':
                // Return timestamp
                return $current_date->getTimestamp();

            default:
                // Default to full date
                return $current_date->format('F j, Y');
        }
    }
}

/**
 * Get formatted date for verification display
 *
 * @return string Formatted verification date
 */
if (!function_exists('halacoupon_get_verification_date')) {
    function halacoupon_get_verification_date() {
        return get_current_month_info('current_day_full');
    }
}

/**
 * Get localized date format
 *
 * @param string $format Date format
 * @return string Localized date
 */
if (!function_exists('halacoupon_get_localized_date')) {
    function halacoupon_get_localized_date($format = 'F j, Y') {
        $timezone = wp_timezone();
        $current_date = new DateTime('now', $timezone);

        // Use WordPress date_i18n for proper localization
        return date_i18n($format, $current_date->getTimestamp());
    }
}

// Mark this module as loaded
add_filter('halacoupon_module_helpers_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'utilities/helpers';
    return $modules;
});
