<?php
/**
 * Social Profiles
 * 
 * Social media profiles display functionality
 * 
 * @package HalaCoupon
 * @subpackage App\Social
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display social media profiles
 */
if (!function_exists('halacoupon_display_social_profiles')) {
    function halacoupon_display_social_profiles() {
        $icons = halacoupon_get_option('profile_social_icon', array());
        $urls = halacoupon_get_option('profile_social_url', array());

        if (!empty($icons) && is_array($icons) && !empty($urls) && is_array($urls)) {
            echo '<div class="flex flex-wrap gap-2 sm:gap-3 md:gap-4">'; // Responsive flex container
            foreach ($icons as $index => $icon) {
                if (isset($urls[$index]) && !empty($urls[$index])) {
                    $url = esc_url($urls[$index]);

                    // Define icon colors and hover effects
                    $icon_classes = [
                        'facebook' => 'text-blue-600 hover:text-blue-700',
                        'twitter' => 'text-blue-400 hover:text-blue-500',
                        'instagram' => 'text-pink-600 hover:text-pink-700',
                        'linkedin' => 'text-blue-700 hover:text-blue-800',
                        'youtube' => 'text-red-600 hover:text-red-700',
                        'pinterest' => 'text-red-500 hover:text-red-600',
                        'snapchat' => 'text-yellow-400 hover:text-yellow-500',
                        'tiktok' => 'text-gray-900 hover:text-gray-800',
                    ];

                    $color_class = isset($icon_classes[$icon]) ? $icon_classes[$icon] : 'text-gray-400 hover:text-gray-500';

                    // Responsive size classes for button and icon
                    $button_size = 'w-9 h-9 sm:w-10 sm:h-10 md:w-12 md:h-12';
                    $icon_size = 'w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6';

                    echo '<a href="' . $url . '" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center rounded-full bg-white/10 ' . $button_size . ' ' . $color_class . ' transition-all duration-200 hover:bg-white/20 hover:scale-110" aria-label="' . esc_attr(ucfirst($icon)) . '">';

                    // Custom icons for better design
                    switch ($icon) {
                        case 'facebook':
                            echo '<svg class="' . $icon_size . '" fill="currentColor" viewBox="0 0 24 24"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>';
                            break;
                        case 'twitter':
                            echo '<svg class="' . $icon_size . '" fill="currentColor" viewBox="0 0 24 24"><path d="M14.095479,10.316482L22.286354,1h-1.940718l-7.115352,8.087682L7.551414,1H1l8.589488,12.231093L1,23h1.940717l7.509372-8.542861L16.448587,23H23L14.095479,10.316482z M11.436522,13.338465l-0.871624-1.218704l-6.924311-9.68815h2.981339l5.58978,7.82155l0.867949,1.218704l7.26506,10.166271h-2.981339L11.436522,13.338465z"/></svg>';
                            break;
                        case 'instagram':
                            echo '<svg class="' . $icon_size . '" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323c.875-.897 2.026-1.387 3.323-1.387s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"/></svg>';
                            break;
                        case 'linkedin':
                            echo '<svg class="' . $icon_size . '" fill="currentColor" viewBox="0 0 24 24"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg>';
                            break;
                        case 'youtube':
                            echo '<svg class="' . $icon_size . '" fill="currentColor" viewBox="0 0 24 24"><path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/></svg>';
                            break;
                        case 'pinterest':
                            echo '<svg class="' . $icon_size . '" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.221.085.342-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/></svg>';
                            break;
                        case 'snapchat':
                            echo '<svg class="' . $icon_size . '" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323c.875-.897 2.026-1.387 3.323-1.387s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"/></svg>';
                            break;
                        case 'tiktok':
                            echo '<svg class="' . $icon_size . '" fill="currentColor" viewBox="0 0 24 24"><path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/></svg>';
                            break;
                        default:
                            echo '<svg class="' . $icon_size . '" fill="currentColor" viewBox="0 0 24 24"><path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16c-.169 1.858-.896 3.605-2.068 4.777-1.172 1.172-2.92 1.899-4.777 2.068C9.33 15.184 8.17 15.184 6.777 15.005c-1.858-.169-3.605-.896-4.777-2.068C.828 11.765.101 10.018-.068 8.16-.247 6.767-.247 5.607-.068 4.214.101 2.356.828.609 2 .437 3.393.258 4.553.258 5.946.437c1.858.169 3.605.896 4.777 2.068C11.895 3.677 13.642 4.404 15.5 4.573c1.393.179 2.553.179 3.946 0 1.858-.169 3.605-.896 4.777-2.068C25.395 1.333 26.122-.414 26.291-2.272c.179-1.393.179-2.553 0-3.946-.169-1.858-.896-3.605-2.068-4.777C23.051-12.167 21.304-12.894 19.446-13.063c-1.393-.179-2.553-.179-3.946 0-1.858.169-3.605.896-4.777 2.068C9.551-9.823 8.824-8.076 8.655-6.218 8.476-4.825 8.476-3.665 8.655-2.272c.169 1.858.896 3.605 2.068 4.777C11.895-.323 13.642.404 15.5.573c1.393.179 2.553.179 3.946 0 1.858-.169 3.605-.896 4.777-2.068C25.395 1.677 26.122 3.424 26.291 5.282c.179 1.393.179 2.553 0 3.946-.169 1.858-.896 3.605-2.068 4.777C23.051 15.177 21.304 15.904 19.446 16.073c-1.393.179-2.553.179-3.946 0z"/></svg>';
                    }

                    echo '</a>';
                }
            }
            echo '</div>';
        }
    }
}

// Mark this module as loaded
add_filter('halacoupon_module_social_profiles_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'social/social-profiles';
    return $modules;
});
