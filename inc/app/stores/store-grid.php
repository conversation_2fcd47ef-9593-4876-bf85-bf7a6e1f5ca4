<?php
/**
 * Enhanced Store Grid Display
 *
 * Modern grid layout for displaying stores with glass morphism design
 *
 * @package HalaCoupon
 * @subpackage App\Stores
 * @version 2.0.0 - Complete Refactor
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display stores in a modern grid layout with enhanced design
 *
 * @param int $limit Number of stores to display (default: 12)
 * @param bool $enhanced_layout Whether to use enhanced layout (default: true)
 * @return void
 */
if (!function_exists('display_stores_grid')) {
    function display_stores_grid($limit = 12, $enhanced_layout = true) {
        // Fetch stores using the fetch_stores function
        $stores = fetch_stores($limit);

        if (empty($stores)) {
            halacoupon_render_no_stores_message();
            return;
        }

        // Render the stores grid
        halacoupon_render_stores_grid($stores, $enhanced_layout);
    }
}

/**
 * Render stores in a modern grid layout
 *
 * @param array $stores Array of store term objects
 * @param bool $enhanced_layout Whether to use enhanced layout
 * @return void
 */
if (!function_exists('halacoupon_render_stores_grid')) {
    function halacoupon_render_stores_grid($stores, $enhanced_layout = true) {
        $total_stores = count($stores);
        $is_rtl = is_rtl();

        ?>
        <!-- Enhanced Stores Grid Container -->
        <section class="stores-grid-section relative"
                 role="region"
                 aria-label="<?php esc_attr_e('Partner stores grid', 'halacoupon'); ?>"
                 dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">

            <?php if ($enhanced_layout) : ?>
            <!-- Grid Header with Stats -->
            <div class="flex items-center justify-between mb-6 lg:mb-8">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-secondary to-accent rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">
                            <?php esc_html_e('Featured Stores', 'halacoupon'); ?>
                        </h3>
                        <p class="text-sm text-gray-600">
                            <?php
                            printf(
                                esc_html__('Showing %d verified partner stores', 'halacoupon'),
                                $total_stores
                            );
                            ?>
                        </p>
                    </div>
                </div>

                <!-- View All Link -->
                <a href="<?php echo esc_url(get_permalink(halacoupon_get_option('all_stores_page'))); ?>"
                   class="inline-flex items-center gap-2 text-secondary hover:text-accent transition-colors duration-200 text-sm font-medium">
                    <span><?php esc_html_e('View All', 'halacoupon'); ?></span>
                    <svg class="w-4 h-4 <?php echo $is_rtl ? 'rotate-180' : ''; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
            <?php endif; ?>

            <!-- Responsive Grid Layout -->
            <div class="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 lg:gap-6"
                 role="list"
                 aria-label="<?php esc_attr_e('Partner stores grid', 'halacoupon'); ?>">

                <?php
                $store_index = 0;
                foreach ($stores as $store) :
                    $store_index++;

                    // Get store data
                    $store_data = halacoupon_get_store_data($store);

                    // Render individual store card
                    ?>
                    <div class="store-grid-item animate-fade-in-up"
                         style="animation-delay: <?php echo ($store_index * 0.1); ?>s;"
                         role="listitem">
                        <?php halacoupon_render_store_card($store_data, $enhanced_layout); ?>
                    </div>
                    <?php
                endforeach;
                ?>

            </div>

            <!-- Schema Markup for SEO -->
            <script type="application/ld+json">
            {
                "@context": "https://schema.org",
                "@type": "ItemList",
                "name": "<?php echo esc_js(__('Partner Stores', 'halacoupon')); ?>",
                "description": "<?php echo esc_js(__('Verified partner stores offering exclusive deals and coupons', 'halacoupon')); ?>",
                "numberOfItems": <?php echo absint($total_stores); ?>,
                "url": "<?php echo esc_js(home_url('/')); ?>"
            }
            </script>
        </section>
        <?php
    }
}

/**
 * Get store data with proper sanitization and fallbacks
 *
 * @param object $store Store term object
 * @return array Sanitized store data
 */
if (!function_exists('halacoupon_get_store_data')) {
    function halacoupon_get_store_data($store) {
        // Get store meta data
        $store_image = get_term_meta($store->term_id, '_ags_store_image', true);
        $store_description = get_term_meta($store->term_id, '_ags_store_description', true);
        $store_link = get_term_link($store->term_id, 'coupon_store');

        // Get coupon counts
        $coupon_counts = halacoupon_get_coupon_counts_by_type($store->term_id, 'coupon_store');
        $total_coupons = ($coupon_counts['code'] ?? 0) + ($coupon_counts['sale'] ?? 0);

        return [
            'id' => $store->term_id,
            'name' => $store->name,
            'slug' => $store->slug,
            'image' => $store_image ? esc_url($store_image) : '',
            'description' => $store_description ? wp_kses_post($store_description) : '',
            'link' => is_wp_error($store_link) ? '#' : esc_url($store_link),
            'coupon_counts' => $coupon_counts,
            'total_coupons' => $total_coupons,
            'code_count' => $coupon_counts['code'] ?? 0,
            'sale_count' => $coupon_counts['sale'] ?? 0,
        ];
    }
}

/**
 * Get latest coupon for a specific store
 *
 * @param int $store_id Store term ID
 * @return array|null Latest coupon data or null if none found
 */
if (!function_exists('halacoupon_get_latest_store_coupon')) {
    function halacoupon_get_latest_store_coupon($store_id) {
        // Check cache first
        $cache_key = 'latest_store_coupon_' . $store_id;
        $cached_result = get_transient($cache_key);

        if (false !== $cached_result) {
            return $cached_result === 'null' ? null : $cached_result;
        }

        // First try to get a code-type coupon
        $args = [
            'post_type' => 'coupon',
            'posts_per_page' => 1,
            'orderby' => 'date',
            'order' => 'DESC',
            'post_status' => 'publish',
            'meta_query' => [
                [
                    'key' => '_ags_coupon_type',
                    'value' => 'code',
                    'compare' => '='
                ]
            ],
            'tax_query' => [
                [
                    'taxonomy' => 'coupon_store',
                    'field' => 'term_id',
                    'terms' => $store_id
                ]
            ]
        ];

        $coupon_query = new WP_Query($args);

        // If no code coupon found, try any coupon type
        if (!$coupon_query->have_posts()) {
            wp_reset_postdata();
            $args['meta_query'] = []; // Remove coupon type restriction
            $coupon_query = new WP_Query($args);
        }
        if ($coupon_query->have_posts()) {
            $coupon_query->the_post();
            $coupon_id = get_the_ID();

            // Get coupon meta data using correct field names
            $coupon_code = get_post_meta($coupon_id, '_ags_coupon_type_code', true);
            $coupon_type = get_post_meta($coupon_id, '_ags_coupon_type', true);
            $discount_save = get_post_meta($coupon_id, '_ags_coupon_save', true);

            wp_reset_postdata();

            // Parse discount save field (format: "30% خصم|نص إضافي")
            $discount_parts = explode('|', $discount_save);
            $discount_value = isset($discount_parts[0]) ? trim($discount_parts[0]) : '';
            $discount_text = isset($discount_parts[1]) ? trim($discount_parts[1]) : '';

            // Only return code if it's a code-type coupon and has a code
            $final_code = ($coupon_type === 'code' && !empty($coupon_code)) ? $coupon_code : '';

            $coupon_data = [
                'id' => $coupon_id,
                'title' => get_the_title($coupon_id),
                'code' => $final_code,
                'type' => $coupon_type,
                'discount_value' => $discount_value,
                'discount_text' => $discount_text,
                'discount_save' => $discount_save,
            ];

            wp_reset_postdata();

            // Cache result for 30 minutes
            set_transient($cache_key, $coupon_data, 30 * MINUTE_IN_SECONDS);

            return $coupon_data;
        }

        wp_reset_postdata();

        // Cache null result for 30 minutes
        set_transient($cache_key, 'null', 30 * MINUTE_IN_SECONDS);

        return null;
    }
}

/**
 * Get display content based on priority hierarchy
 *
 * @param array|null $coupon_data Latest coupon data
 * @param string $store_name Store name as fallback
 * @return string Content to display
 */
if (!function_exists('halacoupon_get_store_display_content')) {
    function halacoupon_get_store_display_content($coupon_data, $store_name) {
        if (empty($coupon_data)) {
            // Third Priority: Store name fallback
            return $store_name;
        }

        // First Priority: Full discount save text (already formatted)
        if (!empty($coupon_data['discount_save'])) {
            // Remove the pipe separator part if exists
            $discount_parts = explode('|', $coupon_data['discount_save']);
            $main_discount = trim($discount_parts[0]);
            if (!empty($main_discount)) {
                return $main_discount;
            }
        }

        // Alternative: Discount value and text combined
        if (!empty($coupon_data['discount_value']) && !empty($coupon_data['discount_text'])) {
            return $coupon_data['discount_value'] . ' ' . $coupon_data['discount_text'];
        }

        // Alternative: Just discount value
        if (!empty($coupon_data['discount_value'])) {
            return $coupon_data['discount_value'];
        }

        // Alternative: Just discount text
        if (!empty($coupon_data['discount_text'])) {
            return $coupon_data['discount_text'];
        }

        // Second Priority: Coupon title
        if (!empty($coupon_data['title'])) {
            return $coupon_data['title'];
        }

        // Third Priority: Store name fallback
        return $store_name;
    }
}

/**
 * Render minimal store card with interactive coupon code input
 *
 * @param array $store_data Store data from halacoupon_get_store_data()
 * @param bool $enhanced_layout Whether to use enhanced layout
 * @return void
 */
if (!function_exists('halacoupon_render_store_card')) {
    function halacoupon_render_store_card($store_data, $enhanced_layout = true) {
        $has_image = !empty($store_data['image']);
        $placeholder_letter = !empty($store_data['name']) ? strtoupper(substr($store_data['name'], 0, 1)) : '?';

        // Get latest coupon data for this store
        $latest_coupon = halacoupon_get_latest_store_coupon($store_data['id']);
        $display_content = halacoupon_get_store_display_content($latest_coupon, $store_data['name']);

        ?>
        <article class="store-card group relative bg-white/80 backdrop-blur-sm rounded-xl shadow-soft border border-white/20 hover:border-primary/30 transition-all duration-300 hover:shadow-float hover:scale-105 overflow-hidden"
                 itemscope
                 itemtype="https://schema.org/Store"
                 data-store-id="<?php echo esc_attr($store_data['id']); ?>">

            <!-- Main Store Link Area -->
            <a href="<?php echo esc_url($store_data['link']); ?>"
               class="block p-3 store-main-link"
               aria-label="<?php echo esc_attr(sprintf(__('Visit %s store', 'halacoupon'), $store_data['name'])); ?>">

                <!-- Compact Store Logo -->
                <div class="w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                    <div class="w-full h-full rounded-lg overflow-hidden bg-gray-50 border border-gray-200 flex items-center justify-center group-hover:border-primary/30 transition-colors duration-300">
                        <?php if ($has_image) : ?>
                            <img src="<?php echo esc_url($store_data['image']); ?>"
                                 alt="<?php echo esc_attr($store_data['name']); ?>"
                                 class="w-full h-full object-contain p-1 group-hover:scale-110 transition-transform duration-300"
                                 loading="lazy"
                                 itemprop="logo">
                        <?php else : ?>
                            <div class="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center text-white font-bold text-sm">
                                <?php echo esc_html($placeholder_letter); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Dynamic Content Display -->
                <div class="text-center mb-3">
                    <h3 class="text-xs font-semibold text-gray-900 line-clamp-2 group-hover:text-primary transition-colors duration-200 leading-tight"
                        itemprop="name">
                        <?php echo esc_html($display_content); ?>
                    </h3>
                </div>
            </a>

            <!-- Interactive Coupon Code Input -->
            <?php if (!empty($latest_coupon['code'])) : ?>
            <div class="px-3 pb-3" onclick="event.stopPropagation();">
                <div class="coupon-input-container relative">
                    <!-- Coupon Code Input -->
                    <input type="text"
                           value="<?php echo esc_attr($latest_coupon['code']); ?>"
                           readonly
                           class="coupon-code-input w-full text-xs font-mono font-bold text-center bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-lg py-2 px-3 text-primary cursor-pointer hover:from-primary/20 hover:to-secondary/20 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary/30"
                           data-coupon-code="<?php echo esc_attr($latest_coupon['code']); ?>"
                           data-coupon-id="<?php echo esc_attr($latest_coupon['id']); ?>"
                           data-store-name="<?php echo esc_attr($store_data['name']); ?>"
                           onclick="halacouponCopyCode(this)"
                           title="<?php esc_attr_e('Click to copy coupon code', 'halacoupon'); ?>"
                           aria-label="<?php echo esc_attr(sprintf(__('Copy coupon code %s', 'halacoupon'), $latest_coupon['code'])); ?>">

                    <!-- Copy Icon -->
                    <div class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
                        <svg class="w-3 h-3 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </div>

                    <!-- Success Message (Hidden by default) -->
                    <div class="success-message absolute inset-0 bg-gradient-to-r from-secondary to-accent text-white text-xs font-bold rounded-lg flex items-center justify-center hide" style="opacity: 0 !important; visibility: hidden !important; pointer-events: none !important; transform: scale(0.95) !important;">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <?php esc_html_e('Successfully copied!', 'halacoupon'); ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Hidden Schema.org metadata -->
            <meta itemprop="description" content="<?php echo esc_attr(sprintf(__('%s store with %d coupons and deals', 'halacoupon'), $store_data['name'], $store_data['total_coupons'])); ?>">
            <meta itemprop="url" content="<?php echo esc_attr($store_data['link']); ?>">

            <?php if (!empty($latest_coupon['code'])) : ?>
            <meta itemprop="offers" itemscope itemtype="https://schema.org/Offer">
            <meta itemprop="priceSpecification" content="<?php echo esc_attr($latest_coupon['code']); ?>">
            <?php endif; ?>
        </article>
        <?php
    }
}

/**
 * Render no stores available message
 *
 * @return void
 */
if (!function_exists('halacoupon_render_no_stores_message')) {
    function halacoupon_render_no_stores_message() {
        ?>
        <div class="no-stores-message text-center py-12 lg:py-16">
            <div class="max-w-md mx-auto">
                <!-- Icon -->
                <div class="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>

                <!-- Message -->
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                    <?php esc_html_e('No Stores Available', 'halacoupon'); ?>
                </h3>
                <p class="text-gray-600 mb-6">
                    <?php esc_html_e('We are working hard to bring you the best partner stores. Check back soon for amazing deals!', 'halacoupon'); ?>
                </p>

                <!-- CTA Button -->
                <a href="<?php echo esc_url(home_url('/')); ?>"
                   class="inline-flex items-center gap-2 bg-gradient-to-r from-secondary to-accent text-white px-6 py-3 rounded-lg font-medium hover:from-accent hover:to-secondary transition-all duration-300 transform hover:scale-105">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    <span><?php esc_html_e('Back to Home', 'halacoupon'); ?></span>
                </a>
            </div>
        </div>
        <?php
    }
}

/**
 * Add CSS and JavaScript for interactive store grid
 *
 * @return void
 */
if (!function_exists('halacoupon_store_grid_styles')) {
    function halacoupon_store_grid_styles() {
        $custom_css = "
        /* Store Grid Animations */
        .stores-grid-section .store-grid-item {
            opacity: 0;
            transform: translateY(20px);
        }

        .stores-grid-section .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Store Card */
        .store-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .store-card:hover {
            transform: translateY(-4px) scale(1.02);
        }

        /* Coupon Input Styling */
        .coupon-code-input {
            transition: all 0.3s ease;
        }

        .coupon-code-input:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(213, 96, 48, 0.15);
        }

        .coupon-code-input:active {
            transform: translateY(0);
        }

        /* Success Message Animation - FORCE HIDDEN by default */
        .success-message,
        .coupon-input-container .success-message,
        div.success-message {
            transition: all 0.3s ease !important;
            opacity: 0 !important;
            transform: scale(0.95) !important;
            pointer-events: none !important;
            visibility: hidden !important;
            display: flex !important;
        }

        .success-message.show {
            opacity: 1 !important;
            transform: scale(1) !important;
            pointer-events: auto !important;
            z-index: 10 !important;
            visibility: visible !important;
        }

        .success-message.hide {
            opacity: 0 !important;
            transform: scale(0.95) !important;
            pointer-events: none !important;
            visibility: hidden !important;
        }

        /* Input field animation */
        .coupon-code-input {
            transition: all 0.3s ease;
        }

        /* Ensure success message is visible when shown */
        .coupon-input-container {
            position: relative;
        }

        .coupon-input-container .success-message {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Grid responsive enhancements */
        .stores-grid-section .grid {
            gap: 1rem;
        }

        @media (min-width: 1024px) {
            .stores-grid-section .grid {
                gap: 1.5rem;
            }
        }

        /* Line clamp utilities */
        .line-clamp-1 {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Hover effects for store cards */
        .stores-grid-section .store-grid-item:hover {
            z-index: 10;
        }

        /* Loading state */
        .stores-grid-section.loading .store-grid-item {
            opacity: 0.5;
            pointer-events: none;
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .stores-grid-section .animate-fade-in-up,
            .stores-grid-section .store-grid-item,
            .stores-grid-section .store-card,
            .coupon-code-input,
            .success-message {
                animation: none !important;
                opacity: 1 !important;
                transform: none !important;
                transition: opacity 0.2s ease !important;
            }
        }

        /* RTL Support */
        [dir='rtl'] .stores-grid-section .grid {
            direction: rtl;
        }

        [dir='rtl'] .store-card {
            text-align: center;
        }

        [dir='rtl'] .coupon-input-container .absolute.right-2 {
            right: auto;
            left: 0.5rem;
        }
        ";

        wp_add_inline_style('halacoupon-main', $custom_css);

        // Add JavaScript for copy functionality using existing theme functions
        $custom_js = "
        console.log('🚀 STORE CARD: Loading copy functions...');

        // IMMEDIATELY hide all success messages on page load
        (function() {
            console.log('🔒 STORE CARD: Force hiding all success messages...');
            var allSuccessMessages = document.querySelectorAll('.success-message');
            allSuccessMessages.forEach(function(msg) {
                msg.style.opacity = '0';
                msg.style.visibility = 'hidden';
                msg.style.pointerEvents = 'none';
                msg.style.transform = 'scale(0.95)';
                msg.classList.remove('show');
                msg.classList.add('hide');
            });
            console.log('✅ STORE CARD: Hidden', allSuccessMessages.length, 'success messages');
        })();

        jQuery(document).ready(function($) {
            console.log('📦 STORE CARD: jQuery ready, initializing functions...');

            // Double-check: hide all success messages again
            $('.success-message').each(function() {
                $(this).css({
                    'opacity': '0',
                    'visibility': 'hidden',
                    'pointer-events': 'none',
                    'transform': 'scale(0.95)'
                }).removeClass('show').addClass('hide');
            });
            console.log('🔒 STORE CARD: jQuery double-check completed');

            // Ensure functions are available globally
        window.halacouponCopyCode = function(inputElement) {
            console.log('halacouponCopyCode called', inputElement);

            const couponCode = inputElement.dataset.couponCode;
            const couponId = inputElement.dataset.couponId;
            const storeName = inputElement.dataset.storeName;
            const container = inputElement.closest('.coupon-input-container');
            const successMessage = container.querySelector('.success-message');

            console.log('Copy data:', { couponCode, couponId, storeName });

            // Use existing theme copy function
            if (typeof window.copyToClipboard === 'function') {
                console.log('Using theme copyToClipboard function');

                // Hide input and show success message
                window.showStoreCardSuccessMessage(inputElement, successMessage);

                // Use theme's copy function (without redirect)
                window.copyToClipboard(couponCode, null);

                // Track usage
                window.trackStoreCardCouponCopy(couponId, storeName);
            } else {
                console.log('Theme copyToClipboard not available, using fallback');
                // Fallback if theme function not available
                window.fallbackCopyTextToClipboard(couponCode, inputElement, successMessage, couponId, storeName);
            }
        };

        window.fallbackCopyTextToClipboard = function(text, inputElement, successMessage, couponId, storeName) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showStoreCardSuccessMessage(inputElement, successMessage);
                    // Use theme's toast function if available
                    if (typeof showToast === 'function') {
                        showToast('Code copied to clipboard!', 'success');
                    }
                    trackStoreCardCouponCopy(couponId, storeName);
                }
            } catch (err) {
                console.error('Fallback: Unable to copy', err);
            }

            document.body.removeChild(textArea);
        };

        window.showStoreCardSuccessMessage = function(inputElement, successMessage) {
            console.log('🎬 STORE CARD: Starting success message animation');
            console.log('Input element:', inputElement);
            console.log('Success message element:', successMessage);

            if (!inputElement || !successMessage) {
                console.error('❌ STORE CARD: Missing elements for success animation');
                return;
            }

            // Force initial hidden state
            successMessage.classList.remove('show');
            successMessage.classList.add('hide');
            successMessage.style.opacity = '0';
            successMessage.style.transform = 'scale(0.95)';
            successMessage.style.pointerEvents = 'none';
            successMessage.style.visibility = 'hidden';

            // Hide input with animation
            inputElement.style.transition = 'all 0.3s ease';
            inputElement.style.opacity = '0';
            inputElement.style.transform = 'scale(0.95)';
            console.log('🔽 STORE CARD: Input hidden');

            // Show success message after input is hidden
            setTimeout(function() {
                console.log('🎯 STORE CARD: Showing success message...');

                // Force show styles
                successMessage.classList.remove('hide');
                successMessage.classList.add('show');
                successMessage.style.opacity = '1';
                successMessage.style.transform = 'scale(1)';
                successMessage.style.pointerEvents = 'auto';
                successMessage.style.zIndex = '10';
                successMessage.style.visibility = 'visible';

                console.log('✅ STORE CARD: Success message shown');
                console.log('Success message computed styles:', window.getComputedStyle(successMessage).opacity);
            }, 300);

            // Hide success message and restore input after 3 seconds
            setTimeout(function() {
                console.log('⏰ STORE CARD: 3 seconds elapsed, hiding success message');

                // Force hide styles
                successMessage.classList.remove('show');
                successMessage.classList.add('hide');
                successMessage.style.opacity = '0';
                successMessage.style.transform = 'scale(0.95)';
                successMessage.style.pointerEvents = 'none';
                successMessage.style.visibility = 'hidden';

                // Restore input after success message is hidden
                setTimeout(function() {
                    inputElement.style.opacity = '1';
                    inputElement.style.transform = 'scale(1)';
                    console.log('🔼 STORE CARD: Input restored');
                }, 300);
            }, 3000);
        };

        window.trackStoreCardCouponCopy = function(couponId, storeName) {
            console.log('📊 STORE CARD: Tracking coupon usage:', couponId, 'Store:', storeName);

            // Use existing theme AJAX tracking system
            if (typeof $ !== 'undefined' && typeof ST !== 'undefined') {
                $.ajax({
                    url: ST.ajax_url,
                    cache: false,
                    data: {
                        coupon_id: couponId,
                        action: 'halacoupon_coupon_ajax',
                        st_doing: 'tracking_coupon',
                        _coupon_nonce: ST._coupon_nonce,
                        _wpnonce: ST._wpnonce,
                    },
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        console.log('✅ STORE CARD: Coupon usage tracked successfully');

                        // Update usage count in UI if element exists
                        var usageCountElement = $('.today-used .count');
                        if (usageCountElement.length && response.success && response.data && response.data.new_count) {
                            usageCountElement.text(response.data.new_count);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.warn('⚠️ STORE CARD: Failed to track coupon usage:', error);
                    }
                });
            } else {
                console.warn('⚠️ STORE CARD: jQuery or ST object not available for tracking');
            }

            // Track with Google Analytics if available
            if (typeof gtag !== 'undefined') {
                gtag('event', 'coupon_copy', {
                    'coupon_id': couponId,
                    'store_name': storeName,
                    'event_category': 'engagement',
                    'event_label': 'store_card'
                });
                console.log('📈 STORE CARD: Google Analytics event tracked');
            }

            // Custom event for theme integration
            const event = new CustomEvent('halacoupon:coupon:copied', {
                detail: { couponId: couponId, storeName: storeName, source: 'store_card' }
            });
            document.dispatchEvent(event);
            console.log('🎯 STORE CARD: Custom event dispatched');
        };

        console.log('✅ STORE CARD: All copy functions loaded successfully!');
        console.log('🔍 STORE CARD: halacouponCopyCode available:', typeof window.halacouponCopyCode);

        }); // End jQuery document ready
        ";

        // Try multiple methods to ensure script loads
        wp_add_inline_script('halacoupon_coupons', $custom_js, 'after');

        // Also add to footer as fallback with higher priority
        add_action('wp_footer', function() use ($custom_js) {
            echo '<script type="text/javascript">';
            echo '/* Store Card Copy Functions - Fallback */';
            echo $custom_js;
            echo '</script>';
        }, 999);
    }
}

// Hook to add custom styles
add_action('wp_enqueue_scripts', 'halacoupon_store_grid_styles', 25);

// Mark this module as loaded
add_filter('halacoupon_module_store_grid_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'stores/store-grid';
    return $modules;
});

// Add action hook for theme customization
do_action('halacoupon_store_grid_loaded');
