<?php
/**
 * Store Functions
 * 
 * Core store-related functionality
 * 
 * @package HalaCoupon
 * @subpackage App\Stores
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Fetch stores with optional filtering (enhanced to filter stores with code-type coupons)
 */
if (!function_exists('fetch_stores')) {
    function fetch_stores($number_of_stores = 0, $featured = null, $only_with_codes = true) {
        // Base query args
        $args = array(
            'taxonomy'   => 'coupon_store',
            'hide_empty' => false,
        );

        // Add featured filter if specified
        if ($featured !== null) {
            $args['meta_query'] = array(
                array(
                    'key'     => '_ags_featured',
                    'value'   => $featured ? 'on' : 'off',
                    'compare' => '='
                )
            );
        }

        // Get all stores first (without limit to filter properly)
        $stores = get_terms($args);

        // Return empty array if no stores or error
        if (empty($stores) || is_wp_error($stores)) {
            return array();
        }

        // Filter stores that have code-type coupons if requested
        if ($only_with_codes) {
            $filtered_stores = array();
            foreach ($stores as $store) {
                if (halacoupon_store_has_code_coupons($store->term_id)) {
                    $filtered_stores[] = $store;
                }
            }
            $stores = $filtered_stores;
        }

        // Apply limit after filtering
        if ($number_of_stores > 0 && count($stores) > $number_of_stores) {
            $stores = array_slice($stores, 0, $number_of_stores);
        }

        return $stores;
    }
}

/**
 * Check if a store has any code-type coupons (with caching)
 *
 * @param int $store_id Store term ID
 * @return bool True if store has code-type coupons, false otherwise
 */
if (!function_exists('halacoupon_store_has_code_coupons')) {
    function halacoupon_store_has_code_coupons($store_id) {
        // Check cache first
        $cache_key = 'store_has_codes_' . $store_id;
        $cached_result = get_transient($cache_key);

        if (false !== $cached_result) {
            return (bool) $cached_result;
        }

        $args = [
            'post_type' => 'coupon',
            'posts_per_page' => 1,
            'post_status' => 'publish',
            'fields' => 'ids', // Only get IDs for performance
            'meta_query' => [
                [
                    'key' => '_ags_coupon_type',
                    'value' => 'code',
                    'compare' => '='
                ]
            ],
            'tax_query' => [
                [
                    'taxonomy' => 'coupon_store',
                    'field' => 'term_id',
                    'terms' => $store_id
                ]
            ]
        ];

        $coupon_query = new WP_Query($args);
        $has_codes = $coupon_query->have_posts();
        wp_reset_postdata();

        // Cache result for 30 minutes
        set_transient($cache_key, $has_codes ? 1 : 0, 30 * MINUTE_IN_SECONDS);

        return $has_codes;
    }
}

/**
 * Get featured stores
 */
if (!function_exists('halacoupon_get_featured_stores')) {
    function halacoupon_get_featured_stores($number = 12) {
        return fetch_stores($number, true);
    }
}

/**
 * Render featured store of the month
 */
if (!function_exists('render_featured_store_of_the_month')) {
    function render_featured_store_of_the_month() {
        // Get the featured store ID from theme options
        $featured_store_id = halacoupon_get_option('featured_store_of_the_month');
        
        if (!$featured_store_id) {
            return;
        }

        // Get the store term
        $store = get_term($featured_store_id, 'coupon_store');
        
        if (is_wp_error($store) || !$store) {
            return;
        }

        // Get store meta data
        $store_image = get_term_meta($store->term_id, '_ags_store_image', true);
        $store_description = get_term_meta($store->term_id, '_ags_store_description', true);
        $store_link = get_term_link($store);
        
        // Get coupon counts
        $coupon_counts = halacoupon_get_coupon_counts_by_type($store->term_id, 'coupon_store');

        echo '<div class="featured-store-of-month">';
        echo '<div class="store-header">';
        echo '<h2 class="section-title">' . esc_html__('Featured Store of the Month', 'halacoupon') . '</h2>';
        echo '</div>';
        
        echo '<div class="featured-store-card">';
        echo '<div class="store-image">';
        if ($store_image) {
            echo '<img src="' . esc_url($store_image) . '" alt="' . esc_attr($store->name) . '" class="store-logo">';
        }
        echo '</div>';
        
        echo '<div class="store-content">';
        echo '<h3 class="store-name"><a href="' . esc_url($store_link) . '">' . esc_html($store->name) . '</a></h3>';
        
        if ($store_description) {
            echo '<p class="store-description">' . esc_html($store_description) . '</p>';
        }
        
        echo '<div class="store-stats">';
        echo '<span class="coupon-count">' . esc_html($coupon_counts['code']) . ' ' . esc_html__('Coupons', 'halacoupon') . '</span>';
        echo '<span class="deal-count">' . esc_html($coupon_counts['sale']) . ' ' . esc_html__('Deals', 'halacoupon') . '</span>';
        echo '</div>';
        
        echo '<a href="' . esc_url($store_link) . '" class="view-store-btn">' . esc_html__('View Store', 'halacoupon') . '</a>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }
}

/**
 * List stores in a divided list for the sidebar.
 *
 * @param int $current_term_id Current store/category ID to exclude from the list.
 * @param string $stores_type Type of stores to filter by: 'default', 'pinned', or 'featured'.
 * @param int $limit Number of stores to display.
 */
if (!function_exists('list_stores_in_divided_list')) {
    function list_stores_in_divided_list($current_term_id = 0, $stores_type = 'default', $limit = 10) {
        $is_rtl = is_rtl();
        $args = array(
            'taxonomy'   => 'coupon_store',
            'hide_empty' => true,
            'number'     => $limit,
            'exclude'    => array($current_term_id),
        );

        // Add meta query based on store type
        if ($stores_type == 'pinned') {
            $args['meta_query'] = array(
                array(
                    'key'     => '_ags_pinned_store',
                    'value'   => 'on',
                    'compare' => '=',
                ),
            );
        } elseif ($stores_type == 'featured') {
            $args['meta_query'] = array(
                array(
                    'key'     => '_ags_is_featured',
                    'value'   => 'on',
                    'compare' => '=',
                ),
            );
        }

        $stores = get_terms($args);

        if (!empty($stores) && !is_wp_error($stores)) {
            echo '<div class="related-stores-list-modern">';
            echo '<div class="flex flex-col gap-3">';

            foreach ($stores as $store) {
                $store_image = get_term_meta($store->term_id, '_ags_store_image', true);
                $store_link = get_term_link($store, 'coupon_store');
                $coupon_counts = halacoupon_get_coupon_counts_by_type($store->term_id, 'coupon_store');
                $total_coupons = ($coupon_counts['code'] ?? 0) + ($coupon_counts['sale'] ?? 0);
                $dir_row = $is_rtl ? 'flex-row-reverse' : 'flex-row';
                $dir_text = $is_rtl ? 'text-right' : 'text-left';
                $dir_icon = $is_rtl ? 'rotate-180 ml-2' : 'mr-2';

                echo '<div class="group flex items-center gap-3 p-3 rounded-xl glass-card border border-white/20 shadow-sm hover:shadow-lg transition-all duration-200 cursor-pointer hover:-translate-y-1">';
                // Store logo or placeholder
                echo '<a href="' . esc_url($store_link) . '" class="block flex-shrink-0">';
                if ($store_image) {
                    echo '<img src="' . esc_url($store_image) . '" alt="' . esc_attr($store->name) . '" class="w-12 h-12 rounded-lg object-cover bg-white border border-gray-100 shadow group-hover:border-primary transition-colors duration-200" loading="lazy">';
                } else {
                    echo '<div class="w-12 h-12 rounded-lg bg-gradient-to-br from-primary/10 to-secondary/10 border border-primary/10 flex items-center justify-center text-primary font-bold text-lg shadow">' . esc_html(mb_substr($store->name, 0, 2)) . '</div>';
                }
                echo '</a>';
                // Store info
                echo '<div class="flex-1 ' . $dir_text . '">';
                echo '<a href="' . esc_url($store_link) . '" class="block font-semibold text-text text-sm group-hover:text-primary transition-colors duration-200 leading-tight line-clamp-1">' . esc_html($store->name) . '</a>';
                echo '<div class="text-xs text-gray-500 mt-1">' . esc_html($total_coupons) . ' ' . esc_html__('offers', 'halacoupon') . '</div>';
                echo '</div>';
                // Arrow icon
                echo '<a href="' . esc_url($store_link) . '" class="flex items-center justify-center w-8 h-8 rounded-full bg-primary/90 text-white opacity-80 group-hover:opacity-100 transition-all duration-200 ' . $dir_icon . '" aria-label="' . esc_attr__('View store', 'halacoupon') . '">';
                echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"/></svg>';
                echo '</a>';
                echo '</div>';
            }

            echo '</div>';
            echo '</div>';
        } else {
            echo '<div class="no-related-stores"><p class="text-gray-500 italic text-center py-6">' . esc_html__('No related stores found.', 'halacoupon') . '</p></div>';
        }
    }
}


/**
 * Output categories related to a store based on shared coupons
 *
 * @param int $store_term_id Store term ID
 * @param int $limit Max number of categories to show (default 10)
 */
function halacoupon_store_related_categories_tags($store_term_id, $limit = 10) {
    if (!$store_term_id) return;
    $is_rtl = is_rtl();
    // Get all coupons for this store
    $coupon_args = array(
        'post_type'      => 'coupon',
        'posts_per_page' => -1,
        'post_status'    => 'publish',
        'fields'         => 'ids',
        'tax_query'      => array(
            array(
                'taxonomy' => 'coupon_store',
                'field'    => 'term_id',
                'terms'    => $store_term_id,
            ),
        ),
    );
    $coupon_query = new WP_Query($coupon_args);
    $coupon_ids = $coupon_query->posts;
    wp_reset_postdata();
    if (empty($coupon_ids)) return;
    // Get all categories assigned to these coupons
    $all_cat_ids = array();
    foreach ($coupon_ids as $cid) {
        $cats = wp_get_post_terms($cid, 'coupon_category', array('fields' => 'ids'));
        if (!empty($cats) && !is_wp_error($cats)) {
            $all_cat_ids = array_merge($all_cat_ids, $cats);
        }
    }
    $all_cat_ids = array_unique($all_cat_ids);
    if (empty($all_cat_ids)) return;
    // Get category terms, order by count descending
    $cat_terms = get_terms(array(
        'taxonomy'   => 'coupon_category',
        'include'    => array_slice($all_cat_ids, 0, $limit),
        'hide_empty' => true,
    ));
    if (empty($cat_terms) || is_wp_error($cat_terms)) return;
    // Output as tag cloud style
    echo '<div class="store-related-categories-tags mt-4">';
    echo '<h4 class="text-xs font-bold text-text/70 mb-2 px-1">' . esc_html__('Popular Categories for this Store', 'halacoupon') . '</h4>';
    echo '<div class="flex flex-wrap gap-2">';
    foreach ($cat_terms as $cat) {
        $cat_link = get_term_link($cat);
        echo '<a href="' . esc_url($cat_link) . '" class="inline-block px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary hover:bg-primary hover:text-white transition-colors duration-200" style="direction:' . ($is_rtl ? 'rtl' : 'ltr') . '">' . esc_html($cat->name) . '</a>';
    }
    echo '</div>';
    echo '</div>';
}

/**
 * Clear store-related caches when content is updated
 */
function halacoupon_clear_store_caches() {
    global $wpdb;

    // Clear store has codes cache
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_store_has_codes_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_store_has_codes_%'");

    // Clear latest store coupon cache
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_latest_store_coupon_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_latest_store_coupon_%'");
}

// Clear cache when coupons are updated
add_action('save_post_coupon', 'halacoupon_clear_store_caches');
add_action('delete_post', function($post_id) {
    if (get_post_type($post_id) === 'coupon') {
        halacoupon_clear_store_caches();
    }
});

// Clear cache when stores are updated
add_action('created_coupon_store', 'halacoupon_clear_store_caches');
add_action('edited_coupon_store', 'halacoupon_clear_store_caches');
add_action('delete_coupon_store', 'halacoupon_clear_store_caches');

// Mark this module as loaded
add_filter('halacoupon_module_store_functions_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'stores/store-functions';
    return $modules;
});
