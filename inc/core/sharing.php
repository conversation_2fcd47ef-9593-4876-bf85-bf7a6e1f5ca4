<?php

/**
 * Class halacoupon_Socials
 *
 * Socials class
 *
 */
class halacoupon_Socials {

    /**
     * Display Facebook share button
     */
    public static function facebook_share($args = array()) {
        $args = wp_parse_args($args, array(
            'title'   => '',
            'url'     => '',
            'image'   => '',
            'class'   => 'btn btn-outline share-facebook',
            'icon'    => '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>',
            'label'   => __('Facebook', 'halacoupon')
        ));

        $url = urlencode(esc_url($args['url']));

        return "<button class='{$args['class']} text-blue-600 border-blue-600 hover:bg-blue-600 hover:text-white' onclick=\"window.open('https://www.facebook.com/sharer/sharer.php?u={$url}', 'sharer', 'toolbar=0,status=0,width=548,height=325,noopener,noreferrer'); return false;\" href=\"javascript:void(0);\">{$args['icon']} <span class=\"ml-2\">{$args['label']}</span></button>";
    }

    /**
     * Display X (Twitter) share button
     */
    public static function x_share($args = array()) {
        $args = wp_parse_args($args, array(
            'title'   => '',
            'url'     => '',
            'image'   => '',
            'class'   => 'btn btn-outline share-twitter',
            'icon'    => '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M14.095479,10.316482L22.286354,1h-1.940718l-7.115352,8.087682L7.551414,1H1l8.589488,12.231093L1,23h1.940717  l7.509372-8.542861L16.448587,23H23L14.095479,10.316482z M11.436522,13.338465l-0.871624-1.218704l-6.924311-9.68815h2.981339  l5.58978,7.82155l0.867949,1.218704l7.26506,10.166271h-2.981339L11.436522,13.338465z"/></svg>',
            'label'   => __('X (Twitter)', 'halacoupon')
        ));

        $title = urlencode($args['title'] . ' ' . $args['url']);

        return "<button class='{$args['class']} text-gray-900 border-gray-900 hover:bg-gray-900 hover:text-white' onclick=\"window.open('https://twitter.com/intent/tweet?text={$title}', 'sharer', 'toolbar=0,status=0,width=548,height=325,noopener,noreferrer'); return false;\" href=\"javascript:void(0);\">{$args['icon']} <span class=\"ml-2\">{$args['label']}</span></button>";
    }

    /**
     * Display WhatsApp share button
     */
    public static function whatsapp_share($args = array()) {
        $args = wp_parse_args($args, array(
            'title'   => '',
            'url'     => '',
            'image'   => '',
            'class'   => 'btn btn-outline share-whatsapp',
            'icon'    => '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.085"/></svg>',
            'label'   => __('WhatsApp', 'halacoupon')
        ));

        $text = urlencode($args['title'] . ' ' . $args['url']);

        return "<a class='{$args['class']} text-green-500 border-green-500 hover:bg-green-500 hover:text-white' href='https://api.whatsapp.com/send?text={$text}' target='_blank' rel='noopener noreferrer'>{$args['icon']} <span class=\"ml-2\">{$args['label']}</span></a>";
    }

    /**
     * Display Telegram share button
     */
    public static function telegram_share($args = array()) {
        $args = wp_parse_args($args, array(
            'title'   => '',
            'url'     => '',
            'image'   => '',
            'class'   => 'btn btn-outline share-telegram',
            'icon'    => '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/></svg>',
            'label'   => __('Telegram', 'halacoupon')
        ));

        $text = urlencode($args['title'] . ' ' . $args['url']);

        return "<a class='{$args['class']} text-blue-500 border-blue-500 hover:bg-blue-500 hover:text-white' href='https://t.me/share/url?url={$args['url']}&text={$text}' target='_blank' rel='noopener noreferrer'>{$args['icon']} <span class=\"ml-2\">{$args['label']}</span></a>";
    }
}


// Remove Open Graph and Twitter meta tags if Yoast SEO is active
function halacoupon_remove_YOAST_SEO_og_twitter() {
    if (is_plugin_active('wordpress-seo/wp-seo.php')) {
        if (is_tax('coupon_store') || is_tax('coupon_category')) {
            remove_action('wpseo_head', array('WPSEO_Twitter', 'get_instance'), 40);
            remove_all_actions('wpseo_opengraph');
        }
    }
}
add_action('wp', 'halacoupon_remove_YOAST_SEO_og_twitter');

// Remove Open Graph and Twitter meta tags if Rank Math is active
function halacoupon_remove_rank_math_og_twitter() {
    if (is_plugin_active('seo-by-rank-math/rank-math.php')) {
        if (is_tax('coupon_store') || is_tax('coupon_category')) {
            remove_all_filters('rank_math/opengraph/facebook');
            remove_all_filters('rank_math/opengraph/twitter');
        }
    }
}
add_action('wp', 'halacoupon_remove_rank_math_og_twitter');

// Utility function to check if a plugin is active
if (!function_exists('is_plugin_active')) {
    include_once(ABSPATH . 'wp-admin/includes/plugin.php');
}




function halacoupon_open_graph() {
    $data = array();
    $default_image = get_template_directory_uri() . '/assets/images/logo.png';

    if (is_tax('coupon_store') || is_tax('coupon_category')) {
        $term = get_queried_object();
        halacoupon_setup_store($term);
        $data['og:title'] = wp_strip_all_tags(halacoupon_store()->get_single_store_name(), true);
        $description = halacoupon_store()->get_content(false);
        $data['og:description'] = wp_trim_words($description, 30, '...') ?: __('Check out our latest offers and discounts!', 'halacoupon');
        $image = halacoupon_store()->get_thumbnail('full', true) ?: $default_image;

        $data['og:image'] = $image;
        $data['og:type'] = 'website';
        $url = halacoupon_store()->get_url();

        if (get_query_var('coupon_id') > 0) {
            $post = get_post(get_query_var('coupon_id'));
            if ($post->post_type == 'coupon') {
                halacoupon_coupon($post);
                $url = halacoupon_coupon()->get_href();
                $data['og:type'] = 'article';
                $data['og:url'] = $url;
                $data['og:title'] = $post->post_title;
                $data['og:description'] = wp_trim_words(get_the_excerpt($post), 30, '...');
                $image = halacoupon_coupon()->get_thumb('full', true, true) ?: halacoupon_store()->get_thumbnail('full', true) ?: $default_image;
                $data['og:image'] = $image;
                wp_reset_postdata();
            }
        }

        $data['og:url'] = $url;
        $data['og:site_name'] = get_bloginfo('name');
        $data['og:locale'] = get_locale();

        // Set image dimensions if available
        if ($image) {
            // Try to get image dimensions safely
            $image_path = $image;

            // If it's a URL, convert to file path for getimagesize
            if (strpos($image, get_template_directory_uri()) === 0) {
                $image_path = str_replace(get_template_directory_uri(), get_template_directory(), $image);
            } elseif (strpos($image, home_url()) === 0) {
                $image_path = str_replace(home_url(), ABSPATH, $image);
            }

            // Only try getimagesize if the file exists
            if (file_exists($image_path)) {
                $image_size = @getimagesize($image_path);
                if ($image_size && isset($image_size[0]) && isset($image_size[1])) {
                    $data['og:image:width'] = $image_size[0];
                    $data['og:image:height'] = $image_size[1];
                }
            }

            $data['og:image:alt'] = esc_attr($data['og:title']) . ' - ' . get_bloginfo('name');
        }
    }

    // Output Open Graph meta tags
    foreach ($data as $k => $v) {
        echo '<meta property="' . esc_attr($k) . '" content="' . esc_attr($v) . '" />' . "\n";
    }

    // Twitter card tags
    $twitter_keys = array(
        'twitter:title' => 'og:title',
        'twitter:url' => 'og:url',
        'twitter:description' => 'og:description',
        'twitter:image' => 'og:image',
    );

    if (!empty($data)) {
        echo "\n<meta name=\"twitter:card\" content=\"summary_large_image\" />\n";
        echo "\n<meta name=\"twitter:site\" content=\"@YourTwitterHandle\" />\n"; // Replace with your Twitter handle
        echo "\n<meta name=\"twitter:creator\" content=\"@YourTwitterHandle\" />\n"; // Replace with your Twitter handle

        foreach ($twitter_keys as $k => $id) {
            if (isset($data[$id])) {
                echo "<meta name=\"{$k}\" content=\"" . esc_attr($data[$id]) . "\" />\n";
            }
        }
    }
}

add_action('wp_head', 'halacoupon_open_graph', 3);
