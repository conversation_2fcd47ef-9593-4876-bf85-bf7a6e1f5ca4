<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */





function halacoupon_remove_class_filter( $tag, $class_name = '', $method_name = '', $priority = 10 ) {
	global $wp_filter;

	// Check that filter actually exists first
	if ( ! isset( $wp_filter[ $tag ] ) ) {
		return false;
	}

	/**
	 * If filter config is an object, means we're using WordPress 4.7+ and the config is no longer
	 * a simple array, rather it is an object that implements the ArrayAccess interface.
	 *
	 * To be backwards compatible, we set $callbacks equal to the correct array as a reference (so $wp_filter is updated)
	 *
	 * @see https://make.wordpress.org/core/2016/09/08/wp_hook-next-generation-actions-and-filters/
	 */
	if ( is_object( $wp_filter[ $tag ] ) && isset( $wp_filter[ $tag ]->callbacks ) ) {
		// Create $fob object from filter tag, to use below
		$fob = $wp_filter[ $tag ];
		$callbacks = &$wp_filter[ $tag ]->callbacks;
	} else {
		$callbacks = &$wp_filter[ $tag ];
	}

	// Exit if there aren't any callbacks for specified priority
	if ( ! isset( $callbacks[ $priority ] ) || empty( $callbacks[ $priority ] ) ) {
		return false;
	}

	// Loop through each filter for the specified priority, looking for our class & method
	foreach ( (array) $callbacks[ $priority ] as $filter_id => $filter ) {

		// Filter should always be an array - array( $this, 'method' ), if not goto next
		if ( ! isset( $filter['function'] ) || ! is_array( $filter['function'] ) ) {
			continue;
		}

		// If first value in array is not an object, it can't be a class
		if ( ! is_object( $filter['function'][0] ) ) {
			continue;
		}

		// Method doesn't match the one we're looking for, goto next
		if ( $filter['function'][1] !== $method_name ) {
			continue;
		}

		// Method matched, now let's check the Class
		if ( get_class( $filter['function'][0] ) === $class_name ) {

			// WordPress 4.7+ use core remove_filter() since we found the class object
			if ( isset( $fob ) ) {
				// Handles removing filter, reseting callback priority keys mid-iteration, etc.
				$fob->remove_filter( $tag, $filter['function'], $priority );

			} else {
				// Use legacy removal process (pre 4.7)
				unset( $callbacks[ $priority ][ $filter_id ] );
				// and if it was the only filter in that priority, unset that priority
				if ( empty( $callbacks[ $priority ] ) ) {
					unset( $callbacks[ $priority ] );
				}
				// and if the only filter for that tag, set the tag to an empty array
				if ( empty( $callbacks ) ) {
					$callbacks = array();
				}
				// Remove this filter from merged_filters, which specifies if filters have been sorted
				unset( $GLOBALS['merged_filters'][ $tag ] );
			}

			return true;
		}
	}

	return false;
}

/**
 * Remove Class Action Without Access to Class Object
 *
 * In order to use the core WordPress remove_action() on an action added with the callback
 * to a class, you either have to have access to that class object, or it has to be a call
 * to a static method.  This method allows you to remove actions with a callback to a class
 * you don't have access to.
 *
 * Works with WordPress 1.2+ (4.7+ support added 9-19-2016)
 *
 * @param string $tag         Action to remove
 * @param string $class_name  Class name for the action's callback
 * @param string $method_name Method name for the action's callback
 * @param int    $priority    Priority of the action (default 10)
 *
 * @return bool               Whether the function is removed.
 */
function halacoupon_remove_class_action( $tag, $class_name = '', $method_name = '', $priority = 10 ) {
	halacoupon_remove_class_filter( $tag, $class_name, $method_name, $priority );
}


add_action( 'init', 'flush_rewrite_rules' );


/**
 * Add rewrite for go out store
 *
 * domain-name.com/out/123
 *
 * @since 1.0.0
 */
function halacoupon_add_rewrite_rules() {
	// Blog post feed links
	add_rewrite_rule( '^blog/(feed|rdf|rss|rss2|atom)/?$', 'index.php?feed=feed', 'top' );
	add_rewrite_rule( '^blog/feed/(feed|rdf|rss|rss2|atom)/?$', 'index.php?feed=$matches[1]', 'top' );
	// Change default feed link to feed coupons
	add_rewrite_rule( '^(feed|rdf|rss|rss2|atom)/?$', 'index.php?home_feed=coupon&feed=$matches[1]', 'top' );
	// Redirect to coupon site
	$slug = halacoupon_get_option( 'go_out_slug', 'out' );
	add_rewrite_rule( '^' . $slug . '/([0-9]+)/?', 'index.php?out=$matches[1]', 'top' );
	// Go to store redirect
	$slug = halacoupon_get_option( 'go_store_slug', 'go-store' );
	add_rewrite_rule( '^' . $slug . '/([0-9]+)/?', 'index.php?go_store_id=$matches[1]', 'top' );

	$store_slug = trim( halacoupon_get_option( 'rewrite_store_slug', '' ) );
	if ( ! $store_slug ) {
		$store_slug = 'discount-codes';
	}

	// [store/([^/]+)/page/?([0-9]{1,})/?$] => index.php?coupon_store=$matches[1]&paged=$matches[2]
	// [store/([^/]+)/?$] => index.php?coupon_store=$matches[1]
	// Store with page number in url
	add_rewrite_rule( '^' . $store_slug . '/([^/]+)/page/?([0-9]{1,})/?', 'index.php?coupon_store=$matches[1]&paged=$matches[2]', 'top' );

	// Store Feed link
	add_rewrite_rule( '^' . $store_slug . '/([^/]+)/(feed|rdf|rss|rss2|atom)/?', 'index.php?coupon_store=$matches[1]&feed=$matches[1]', 'top' );

	// Share url
	add_rewrite_rule( '^' . $store_slug . '/([^/]+)/share/([0-9]+)/?', 'index.php?coupon_store=$matches[1]&share_id=$matches[2]', 'top' );

	if ( halacoupon_get_option( 'enable_single_coupon', false ) ) {
		// Single coupon link
		add_rewrite_rule( '^' . $store_slug . '/([^/]+)/([^/]+)/?', 'index.php?coupon=$matches[2]', 'top' );
	} else {
		// Open coupon modal
		add_rewrite_rule( '^' . $store_slug . '/([^/]+)/([^/]+)/?', 'index.php?coupon_store=$matches[1]&coupon_id=$matches[2]', 'top' );
	}

}

/**
 * Add new query vars
 *
 * @see get_query_var()
 * @since 1.0.0
 */
function halacoupon_rewrite_tags() {
	add_rewrite_tag( '%home_feed%', '([^&]+)' );
	add_rewrite_tag( '%out%', '([^&]+)' );
	add_rewrite_tag( '%go_store_id%', '([^&]+)' );
	add_rewrite_tag( '%share_id%', '([^&]+)' );
	add_rewrite_tag( '%coupon_id%', '([^&]+)' );
}

/**
 * Init rewrite setup
 */
add_action( 'init', 'halacoupon_add_rewrite_rules', 11, 0 );
add_action( 'init', 'halacoupon_rewrite_tags', 11, 0 );


add_action( 'init', 'halacoupon_request_uri_setup' );
/**
 * Do set up with request uri
 */
function halacoupon_request_uri_setup() {
	$GLOBALS['st_paged'] = 0;
	global $wp_rewrite;
	$matches = false;
	if ( $wp_rewrite->using_permalinks() ) {
		preg_match( '/page\/([0-9]+)/', $_SERVER['REQUEST_URI'], $matches );
	} else {
		preg_match( '/paged=([0-9]+)/', $_SERVER['REQUEST_URI'], $matches );
	}
	if ( $matches ) {
		$GLOBALS['st_paged'] = $matches[1];
	}

	if ( preg_match( '/(feed|rdf|rss|rss2|atom)/', $_SERVER['REQUEST_URI'], $matches_2 ) ) {

	}

}



/**
 * Get paged number
 */
function halacoupon_get_paged() {
	global $paged;
	if ( ! $paged ) {
		return intval( $GLOBALS['st_paged'] ) > 0 ? intval( $GLOBALS['st_paged'] ) : 1;
	}

	return $paged;
}


/**
 * Get registered_sidebars for setting options
 */
function halacoupon_get_registered_sidebars() {
	global $wp_registered_sidebars;

	// st_debug( $wp_registered_sidebars );
	$a = array();
	foreach ( $wp_registered_sidebars as $k => $s ) {
		$a[ $k ] = $s['name'];
	}
	return $a;
}


function halacoupon_change_coupon_feed_link( $link ) {
	$post = get_post();
	// if not a coupon return the link
	if ( get_post_type( $post ) != 'coupon' ) {
		return $link;
	}
	$link = halacoupon_coupon( $post )->get_href();
	return $link;
}
add_filter( 'the_permalink_rss', 'halacoupon_change_coupon_feed_link', 55 );

function halacoupon_optimize_wp_cleanup() {
    // Remove WordPress version number
    remove_action('wp_head', 'wp_generator');
    
    // Remove RSD link
    remove_action('wp_head', 'rsd_link');
    
    // Remove Windows Live Writer manifest link
    remove_action('wp_head', 'wlwmanifest_link');
    
    // Remove shortlink
    remove_action('wp_head', 'wp_shortlink_wp_head', 10, 0);
    
    // Remove oEmbed discovery links
    remove_action('wp_head', 'wp_oembed_add_discovery_links');
    
    // Remove oEmbed-specific JavaScript from the front-end
    remove_action('wp_head', 'wp_oembed_add_host_js');
    
    // Remove adjacent post links for better SEO control
    remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0);
    
    // Remove Emoji scripts and styles
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');

    // Remove feed links
    remove_action('wp_head', 'feed_links_extra', 3); // Category feeds
    remove_action('wp_head', 'feed_links', 2);       // General feeds

    // Remove comments feed links
    remove_action('wp_head', 'wp_resource_hints', 2);

    // Clean up REST API links (keeps functionality but hides the link tag)
    remove_action('wp_head', 'rest_output_link_wp_head', 10);
    remove_action('wp_head', 'wp_rest_api', 10);
}

add_action('init', 'halacoupon_optimize_wp_cleanup', 55);

/**
 * Query coupon feed
 *
 * @param $query
 * @see WP_Query
 * @see query_posts()
 */
function halacoupon_coupons_feed( $wp_query ) {
	if ( is_feed() ) {
		if (
			get_query_var( 'home_feed' ) == 'coupon'
			|| isset( $wp_query->query['coupon_store'] ) // Store feed link
			|| ( isset( $wp_query->query['taxonomy'] ) && $wp_query->query['taxonomy'] == 'coupon_store' ) // Store feed link
			|| isset( $wp_query->query['coupon_category'] ) // Category feed link
			|| ( isset( $wp_query->query['taxonomy'] ) && $wp_query->query['taxonomy'] == 'coupon_category' ) // Category feed link
		) {
			$wp_query->set( 'post_type', 'coupon' );
		}
	}
}
add_action( 'pre_get_posts', 'halacoupon_coupons_feed' );

/**
 * Change Blog posts feed link
 *
 * @see get_feed_link
 */
function halacoupon_change_feed_link( $link, $feed ) {
	if ( is_home() ) {
		global $wp_rewrite;
		if ( false === strpos( $link, 'comments' ) ) {
			$permalink = $wp_rewrite->get_feed_permastruct();
			if ( '' != $permalink ) {
				if ( get_default_feed() == $feed ) {
					$feed = '';
				}
				if ( ! $feed ) {
					$feed = '/' . $feed;
				}
				$link = trailingslashit( home_url( 'blog' . $feed ) );
			}
		}
	}
	return $link;
}

add_filter( 'feed_link', 'halacoupon_change_feed_link', 35, 2 );



