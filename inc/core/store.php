<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */


function halacoupon_get_stores( $args = array() ) {
	// get store id if in the loop and not set
	$default = array(
		'orderby'                => 'count',
		'taxonomy'               => 'coupon_store',
		'order'                  => 'DESC',
		'hide_empty'             => false,
		'include'                => array(),
		'exclude'                => array(),
		'exclude_tree'           => array(),
		'number'                 => '10',
		'hierarchical'           => false,
		'pad_counts'             => false,
		'child_of'               => 0,
		'childless'              => false,
		'cache_domain'           => 'core',
		'update_term_meta_cache' => true,
	);

	$args = wp_parse_args( $args, $default );

	return get_terms( $args );
}





/**
 * Get featured stores
 *
 * @since 1.0.0
 * @param null $store_id
 * @return object|null
 */
function halacoupon_get_featured_stores( $number = 8 ) {
	// get store id if in the loop and not set
	$args = array(
		'orderby'                => 'count',
		'order'                  => 'DESC',
		'hide_empty'             => false,
		'include'                => array(),
		'exclude'                => array(),
		'exclude_tree'           => array(),
		'number'                 => $number,
		'hierarchical'           => false,
		'pad_counts'             => false,
		'child_of'               => 0,
		'childless'              => false,
		'cache_domain'           => 'core',
		'update_term_meta_cache' => true,
		'meta_query'             => array(
			'relation' => 'AND',
			array(
				'key'     => '_ags_is_featured',
				'value'   => 'on',
				'compare' => '=',
			),
		),
	);

	return get_terms( 'coupon_store', $args );
}




/**
 * Class halacoupon_Store
 *
 * @see WP_Term
 */
class halacoupon_Store {


	/**
	 * Term ID.
	 *
	 * @since 4.4.0
	 * @access public
	 * @var int
	 */
	public $term_id;

	/**
	 * The term's name.
	 *
	 * @since 4.4.0
	 * @access public
	 * @var string
	 */
	public $name = '';

	/**
	 * The term's slug.
	 *
	 * @since 4.4.0
	 * @access public
	 * @var string
	 */
	public $slug = '';

	/**
	 * The term's term_group.
	 *
	 * @since 4.4.0
	 * @access public
	 * @var string
	 */
	public $term_group = '';

	/**
	 * Term Taxonomy ID.
	 *
	 * @since 4.4.0
	 * @access public
	 * @var int
	 */
	public $term_taxonomy_id = 0;

	/**
	 * The term's taxonomy name.
	 *
	 * @since 4.4.0
	 * @access public
	 * @var string
	 */
	public $taxonomy = '';

	/**
	 * The term's description.
	 *
	 * @since 4.4.0
	 * @access public
	 * @var string
	 */
	public $description = '';

	/**
	 * ID of a term's parent term.
	 *
	 * @since 4.4.0
	 * @access public
	 * @var int
	 */
	public $parent = 0;

	/**
	 * Cached object count for this term.
	 *
	 * @since 4.4.0
	 * @access public
	 * @var int
	 */
	public $count = 0;

	/**
     * Errors related to the store.
     *
     * @var array
     */
    protected $errors = array();

    /**
     * Data associated with errors.
     *
     * @var array
     */
    protected $error_data = array();


	public $filter;
	public $_ags_count_posts;
	public $_ags_store_url;
	public $_ags_store_aff_url;
	public $_ags_store_name;
	public $_ags_store_heading;
	public $_ags_is_featured;
	public $_ags_pinned_store;
	public $_ags_store_image_id;
	public $_ags_store_image;
	public $_ags_extra_info;
	public $_ags_coupon_code;
	public $_ags_coupon_print;
	public $_ags_coupon_sale;

	// Social profile properties
	public $_ags_store_fb;
	public $_ags_store_x;
	public $_ags_store_insta;
	public $_ags_store_pin;

	// Store page link properties
	public $_ags_store_contact_url;
	public $_ags_store_delivery_url;
	public $_ags_store_returns_policy_url;
	public $_ags_store_faq_url;



	/**
	 *  Construct function
	 *
	 * @param mixed  $p
	 * @param string $current_url
	 */
	function __construct( $term = null ) {

		if ( ! is_object( $term ) || ! ( $term instanceof WP_Term ) ) {
			$term = get_term( $term, 'coupon_store' );
		}

		if ( $term ) {
			foreach ( $term as $k => $v ) {
				$this->$k = $v;
			}
		}
		// default meta keys
		$default_meta = array(
			'_ags_store_url'       => '',
			'_ags_store_aff_url'   => '',
			'_ags_store_name'      => '',
			'_ags_store_heading'   => '',
			'_ags_is_featured'     => '',
			'_ags_pinned_store'    => '',
			'_ags_count_posts'     => 0,
			'_ags_extra_info'      => '',
			'_ags_store_image_id'  => '',
			'_ags_store_image'     => '',
			'_ags_coupon_code'     => 0,
			'_ags_coupon_print'    => 0,
			'_ags_coupon_sale'     => 0,
			// Social profile meta keys
			'_ags_store_fb'        => '',
			'_ags_store_x'         => '',
			'_ags_store_insta'     => '',
			'_ags_store_pin'       => '',
			// Store page link meta keys
			'_ags_store_contact_url'        => '',
			'_ags_store_delivery_url'       => '',
			'_ags_store_returns_policy_url' => '',
			'_ags_store_faq_url'            => '',
		);

		// Setup meta key as property
		foreach ( $default_meta as $key => $v ) {
			$this->{$key} = get_term_meta( $this->term_id, $key, true );
		}

		/**
		 * This wp embed if have not any posts
		 */
		global $post;
		if ( ! is_object( $post ) ) {
			$post = (object) array(
				'ID' => 999999,
			);
		}
	}

	/**
	 * Check if this post is store
	 *
	 * @return bool
	 */
	public function is_store() {
		return $this->term_id > 0 ? true : false;
	}

	/**
	 * If is featured store
	 *
	 * @return bool
	 */
	public function is_featured() {
		return strtolower( $this->_ags_is_featured ) == 'on' ? true : false;
	}

	





	/**
	 * If is pinned store
	 *
	 * @return bool
	 */
	public function is_pinned() {
		return strtolower( $this->_ags_pinned_store ) == 'on' ? true : false;
	}

	/**
	 * Get term url
	 *
	 * @return bool|string|WP_Error
	 */
	function get_url() {
		if ( ! $this->is_store() ) {
			return false;
		}
		$url = false;
		if ( ! $url && taxonomy_exists( 'coupon_store' ) ) {
			$url = get_term_link( $this );
		}
		return $url;
	}

	/**
	 * Get store home URL
	 *
	 * @since 1.0.0
	 * @param string $default
	 * @return string
	 */
	public function get_home_url( $default_store = true ) {
		if ( ! $this->is_store() ) {
			return false;
		}

		$url = (string) $this->_ags_store_url;
		if ( ! $url && $default_store ) {
			$url = $this->get_url();
		}
		if ( $url ) {
			return $url;
		} else {
			return false;
		}

	}

	/**
	 * Get store aff url / website URL
	 *
	 * @since 1.0.0
	 * @param string $default
	 * @return string
	 */
	public function get_website_url( $default_store = true, $aff_first = true ) {
		if ( ! $this->is_store() ) {
			return false;
		}

		if ( $aff_first ) {
			if ( $this->_ags_store_aff_url != '' ) {
				return (string) $this->_ags_store_aff_url;
			}
		}

		$url = (string) $this->_ags_store_url;
		if ( ! $url && $default_store ) {
			$url = $this->get_url();
		}
		if ( $url ) {
			return $url;
		} else {
			return false;
		}

	}

	function get_extra_info() {
		$content = '';
		if ( $this->_ags_extra_info != '' ) {
			$content = apply_filters( 'the_content', $this->_ags_extra_info );
		}
		return $content;
	}

	function get_content( $echo = false, $read_more = false ) {

		if ( $read_more ) {
			$content = halacoupon_toggle_content_more( $this->description );
		} else {
			$content = apply_filters( 'the_content', $this->description );
		}

		$content = apply_filters( 'halacoupon_get_store_content', $content, $read_more, $this );

		if ( $echo ) {
			echo $content;
		} else {
			return $content;
		}
	}

	function has_thumbnail() {
		return ( intval( $this->_ags_store_image_id ) > 0 ) ? $this->_ags_store_image_id : false;
	}

	/**
	 * Get store thumbnail
	 *
	 * @param string $size
	 * @param array  $atts array of html atts
	 * @return mixed|string|void
	 */
	function get_thumbnail( $size = 'halacoupon_small_thumb', $url_only = false ) {
		$image_alt = get_post_meta( $this->_ags_store_image_id, '_wp_attachment_image_alt', true );
		if ( $this->has_thumbnail() ) {
			if ( $url_only ) {
				$image = wp_get_attachment_image_src( $this->_ags_store_image_id, $size );
				if ( $image ) {
					return $image[0];
				} else {
					return false;
				}
			} else {
				$image_title = get_the_title( $this->_ags_store_image_id );
				return wp_get_attachment_image(
					$this->_ags_store_image_id,
					$size,
					false,
					array(
						'alt' => $image_alt,
						'title' => $image_title,
					)
				);
			}
		} elseif ( $this->_ags_store_url != '' ) {
			global $_wp_additional_image_sizes;
			if ( ! is_array( $size ) && isset( $_wp_additional_image_sizes[ $size ] ) ) {
				$_size = $_wp_additional_image_sizes[ $size ];
			} else {
				$_size = false;
			}
			$_size = wp_parse_args(
				$_size,
				array(
					'width' => 200,
					'height' => 115,
				)
			);

			$url = 'http://s.wordpress.com/mshots/v1/' . urlencode( $this->_ags_store_url ) . '?w=' . $_size['width'] . '&h=' . $_size['height'];
			$url = apply_filters( 'halacoupon_webshoot_url', $url, $this->_ags_store_url, $_size );

			if ( $url_only ) {
				return $url;
			}
			return '<img src="' . $url . '" alt="' . $image_alt . '">';
		} else {
			$image = get_template_directory_uri() . '/assets/images/store.png';
			if ( $url_only ) {
				return $image;
			}
			return '<img alt="" src="' . apply_filters( 'halacoupon_store_placeholder', $image ) . '" >';
		}
	}

	/**
	 * Get store display name
	 *
	 * @since 1.0.0
	 * @return string|void
	 */
	public function get_display_name() {
		return ( $this->name != '' ) ? $this->name : esc_html__( 'Untitled', 'halacoupon' );
	}


	/**
	 *  Get single store display name
	 *
	 * @return mixed|string|void
	 */
	public function get_single_store_name() {
		$heading = ( $this->_ags_store_heading ) ? $this->_ags_store_heading : halacoupon_get_option( 'store_heading' );
		if ( $heading != '' ) {
			return str_replace( '%store_name%', $this->get_display_name(), $heading );
		} else {
			return $this->get_display_name();
		}
	}


	/**
	 * Count coupon in this store
	 *
	 * @since 1.0.0
	 * @return array
	 */
	public function count_coupon() {

		if ( ! $this->term_id ) {
			return array();
		}

		$types_txt = halacoupon_get_coupon_types();
		$type = array();
		// inital count types.
		foreach ( $types_txt as $k => $t ) {
			$type[ $k ] = 0;
		}

		$cache_key = 'get_coupon_count:' . $this->term_id;
		$cache = wp_cache_get( $cache_key, 'coupons_count' );
		if ( false !== $cache ) {
			return wp_parse_args( $cache, $type );
		}

		global $wpdb;

		$querystr = "
            SELECT DISTINCT $wpdb->posts.ID FROM $wpdb->posts
            LEFT JOIN $wpdb->postmeta ON($wpdb->posts.ID = $wpdb->postmeta.post_id)
            LEFT JOIN $wpdb->term_relationships ON($wpdb->posts.ID = $wpdb->term_relationships.object_id)
            LEFT JOIN $wpdb->term_taxonomy ON($wpdb->term_relationships.term_taxonomy_id = $wpdb->term_taxonomy.term_taxonomy_id)
            LEFT JOIN $wpdb->terms ON( $wpdb->term_taxonomy.term_id = $wpdb->terms.term_id )
            WHERE $wpdb->terms.term_id = {$this->term_id}
            AND $wpdb->posts.post_status = 'publish'
            AND $wpdb->posts.post_type = 'coupon'
        ";

		$sql_count = "
         SELECT count( $wpdb->postmeta.post_id ) as count_number, $wpdb->postmeta.meta_value
          FROM $wpdb->postmeta
          WHERE $wpdb->postmeta.post_id IN ( $querystr  )
          AND $wpdb->postmeta.meta_key = '_ags_coupon_type'
          GROUP BY $wpdb->postmeta.meta_value
        ";

		$rows = $wpdb->get_results( $sql_count );
		if ( $rows ) {
			foreach ( $rows as $r ) {
				$type[ $r->meta_value ] = $r->count_number;
			}
		}
		$type = array_map( 'absint', $type );
		wp_cache_set( $cache_key, $type, 'coupons_count' );

		return $type;
	}

	/**
	 * Get coupons of this store
	 *
	 * @return null|object
	 */
	function get_coupons() {
		// get store id if in the loop and not set
		global $wp_query;

		$args = array(
			'post_type'      => 'coupon',
			'posts_per_page' => -1,
			'tax_query' => array(
				'relation' => 'AND',
				array(
					'taxonomy' => 'coupon_store',
					'field'    => 'term_id',
					'terms'    => array( $this->term_id ),
					'operator' => 'NOT',
				),
			),
			'orderby'         => 'date',
			'order'           => 'desc',
		);

		$wp_query = new WP_Query( $args );
		return $wp_query->get_posts();
	}

	/**
	 * Count coupons of this store
	 *
	 * @return array
	 */
	function get_coupon_count() {
		return $this->count_coupon();
	}

	/**
	 * Get Go Out store url
	 *
	 * @example: http://site.com/go-store/{coupon_id}
	 *
	 * @return string
	 */
	function get_go_store_url() {

		$slug = halacoupon_get_option( 'go_store_slug', 'go-store' );

		$home = trailingslashit( home_url() );
		// If permalink enable
		if ( get_option( 'permalink_structure' ) != '' ) {
			return $home . $slug . '/' . $this->term_id;
		} else {
			return $home . '?go_store_id=' . $this->term_id;
		}
	}


} // end class halacoupon_Store


/**
 * Parse store full description content into structured content blocks based on H2 headings
 *
 * This function processes store full description content and automatically separates it into
 * structured content blocks based on H2 headings. Each block contains an H2 and its associated content.
 *
 * @since 1.0.0
 * @param string $content HTML content string (typically from halacoupon_store()->get_extra_info())
 * @param array $args Optional arguments for customization
 * @return array Array of content blocks with heading, content, and formatted HTML
 */
function halacoupon_parse_store_content_blocks( $content = '', $args = array() ) {

    // Default arguments
    $defaults = array(
        'wrapper_class'     => 'store-content-block',
        'header_class'      => 'store-content-header',
        'content_class'     => 'store-content',
        'heading_tag'       => 'h2',
        'article_tag'       => 'article',
        'responsive'        => true,
        'add_ids'          => true,
        'sanitize'         => true,
    );

    $args = wp_parse_args( $args, $defaults );

    // If no content provided, try to get from current store
    if ( empty( $content ) && function_exists( 'halacoupon_store' ) ) {
        $store = halacoupon_store();
        if ( $store && $store->is_store() ) {
            $content = $store->get_extra_info();
        }
    }

    // Return empty array if no content
    if ( empty( $content ) ) {
        return array();
    }

    // Sanitize content if requested
    if ( $args['sanitize'] ) {
        $content = wp_kses_post( $content );
    }

    // Initialize DOMDocument for proper HTML parsing
    $dom = new DOMDocument();
    $dom->encoding = 'UTF-8';

    // Suppress errors for malformed HTML and load content
    libxml_use_internal_errors( true );

    // Add UTF-8 meta tag and wrap content to ensure proper parsing
    $html_content = '<!DOCTYPE html><html><head><meta charset="UTF-8"></head><body>' . $content . '</body></html>';
    $dom->loadHTML( $html_content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD );

    // Clear libxml errors
    libxml_clear_errors();

    // Get all H2 elements
    $h2_elements = $dom->getElementsByTagName( 'h2' );

    // If no H2 elements found, return entire content as single block
    if ( $h2_elements->length === 0 ) {
        return array(
            array(
                'heading'    => '',
                'content'    => $content,
                'block_html' => halacoupon_format_content_block( '', $content, $args ),
                'block_id'   => 'store-content-block-1',
                'has_heading' => false,
            )
        );
    }

    $blocks = array();
    $block_counter = 1;

    // Convert H2 NodeList to array for easier manipulation
    $h2_array = array();
    foreach ( $h2_elements as $h2 ) {
        $h2_array[] = $h2;
    }

    // Process each H2 and collect content until next H2
    for ( $i = 0; $i < count( $h2_array ); $i++ ) {
        $current_h2 = $h2_array[ $i ];
        $next_h2 = isset( $h2_array[ $i + 1 ] ) ? $h2_array[ $i + 1 ] : null;

        // Get heading text
        $heading_text = trim( $current_h2->textContent );

        // Create SEO-optimized block ID from heading
        $block_id = 'store-content-block-' . $block_counter;
        $seo_data = array();

        if ( $args['add_ids'] && ! empty( $heading_text ) ) {
            // Create SEO-optimized ID with proper encoding
            $seo_data = halacoupon_create_seo_block_id( $heading_text, $block_counter );
            $block_id = 'store-block-' . $seo_data['safe_id'];
        }

        // Collect content between current H2 and next H2
        $block_content = halacoupon_extract_content_between_h2( $dom, $current_h2, $next_h2 );

        // Format the complete block HTML with SEO data
        $format_args = $args;
        if ( ! empty( $seo_data['seo_slug'] ) ) {
            $format_args['seo_slug'] = $seo_data['seo_slug'];
            $format_args['seo_encoded'] = $seo_data['encoded'];
        }
        $block_html = halacoupon_format_content_block( $heading_text, $block_content, $format_args, $block_id );

        // Add to blocks array with SEO data
        $blocks[] = array(
            'heading'     => $heading_text,
            'content'     => $block_content,
            'block_html'  => $block_html,
            'block_id'    => $block_id,
            'has_heading' => true,
            'order'       => $block_counter,
            'seo_slug'    => ! empty( $seo_data['seo_slug'] ) ? $seo_data['seo_slug'] : '',
            'seo_encoded' => ! empty( $seo_data['encoded'] ) ? $seo_data['encoded'] : false,
            'original_text' => ! empty( $seo_data['original'] ) ? $seo_data['original'] : $heading_text,
        );

        $block_counter++;
    }

    // Apply filters to allow customization
    $blocks = apply_filters( 'halacoupon_store_content_blocks', $blocks, $content, $args );

    return $blocks;
}


/**
 * Extract content between two H2 elements
 *
 * @since 1.0.0
 * @param DOMDocument $dom The DOM document
 * @param DOMElement $start_h2 Starting H2 element
 * @param DOMElement|null $end_h2 Ending H2 element (null for last block)
 * @return string HTML content between H2 elements
 */
function halacoupon_extract_content_between_h2( $dom, $start_h2, $end_h2 = null ) {
    $content = '';
    $current_node = $start_h2->nextSibling;

    // Traverse siblings until we reach the next H2 or end of content
    while ( $current_node ) {
        // Stop if we reach the next H2
        if ( $end_h2 && $current_node->isSameNode( $end_h2 ) ) {
            break;
        }

        // Stop if we encounter another H2
        if ( $current_node->nodeType === XML_ELEMENT_NODE &&
             strtolower( $current_node->nodeName ) === 'h2' &&
             ! $current_node->isSameNode( $start_h2 ) ) {
            break;
        }

        // Add node content to our block content
        if ( $current_node->nodeType === XML_ELEMENT_NODE ||
             ( $current_node->nodeType === XML_TEXT_NODE && trim( $current_node->textContent ) !== '' ) ) {
            $content .= $dom->saveHTML( $current_node );
        }

        $current_node = $current_node->nextSibling;
    }

    return trim( $content );
}


/**
 * Format a content block with proper HTML structure and CSS classes
 *
 * @since 1.0.0
 * @param string $heading The heading text
 * @param string $content The content HTML
 * @param array $args Formatting arguments
 * @param string $block_id Optional block ID
 * @return string Formatted HTML block
 */
function halacoupon_format_content_block( $heading, $content, $args, $block_id = '' ) {

    // Generate block ID if not provided
    if ( empty( $block_id ) ) {
        $block_id = 'store-content-block-' . uniqid();
    }

    // Start building the HTML
    $html = '<' . esc_attr( $args['article_tag'] ) . ' class="' . esc_attr( $args['wrapper_class'] ) . '"';

    // Add primary ID attribute
    $html .= ' id="' . esc_attr( $block_id ) . '"';

    // Add SEO-friendly anchor if available
    if ( ! empty( $args['seo_slug'] ) ) {
        $html .= ' data-seo-anchor="' . esc_attr( $args['seo_slug'] ) . '"';

        // Add invisible SEO anchor for direct URL access
        $html .= '>';
        $html .= '<span id="' . esc_attr( $args['seo_slug'] ) . '" class="seo-anchor" aria-hidden="true"></span>';
    } else {
        // Add responsive attributes
        if ( $args['responsive'] ) {
            $html .= ' data-responsive="true"';
        }
        $html .= '>';
    }

    // Add header section if heading exists
    if ( ! empty( $heading ) ) {
        $html .= '<header class="' . esc_attr( $args['header_class'] ) . '">';
        $html .= '<' . esc_attr( $args['heading_tag'] ) . ' class="store-content-heading">';
        $html .= esc_html( $heading );
        $html .= '</' . esc_attr( $args['heading_tag'] ) . '>';
        $html .= '</header>';
    }

    // Add content section
    if ( ! empty( $content ) ) {
        $html .= '<div class="' . esc_attr( $args['content_class'] ) . '">';
        $html .= $content; // Content is already sanitized in main function
        $html .= '</div>';
    }

    $html .= '</' . esc_attr( $args['article_tag'] ) . '>';

    return $html;
}


/**
 * Render store content blocks with enhanced SEO, accessibility and table of contents
 *
 * Creates a complete content structure with collapsible table of contents,
 * proper semantic markup, and comprehensive accessibility features.
 *
 * @since 1.0.0
 * @param string $content Optional content to parse (defaults to current store extra info)
 * @param array $args Optional rendering arguments
 * @return string Rendered HTML output with full SEO and accessibility structure
 */
function halacoupon_render_store_content_blocks( $content = '', $args = array() ) {

    // Default rendering arguments with enhanced SEO and accessibility options
    $defaults = array(
        'container_class'   => 'store-content-blocks-container',
        'show_navigation'   => true,
        'navigation_class'  => 'store-content-navigation store-toc',
        'responsive'        => true,
        'add_schema'        => true,
        'wrapper_element'   => 'article',
        'add_breadcrumb'    => true,
        'add_reading_time'  => true,
        'add_print_styles'  => true,
    );

    $args = wp_parse_args( $args, $defaults );

    // Get content blocks
    $blocks = halacoupon_parse_store_content_blocks( $content, $args );

    // Return empty string if no blocks
    if ( empty( $blocks ) ) {
        return '';
    }

    // Generate unique container ID for accessibility
    $container_id = 'store-content-' . uniqid();

    // Calculate reading time for SEO
    $total_words = 0;
    foreach ( $blocks as $block ) {
        $total_words += str_word_count( strip_tags( $block['content'] ) );
    }
    $reading_time = max( 1, ceil( $total_words / 200 ) ); // Average 200 words per minute

    // Start building output with proper semantic structure
    $output = '<' . esc_attr( $args['wrapper_element'] ) . ' ';
    $output .= 'id="' . esc_attr( $container_id ) . '" ';
    $output .= 'class="' . esc_attr( $args['container_class'] ) . '" ';
    $output .= 'role="main" ';
    $output .= 'aria-labelledby="' . esc_attr( $container_id ) . '-title"';

    // Add comprehensive schema markup for SEO
    if ( $args['add_schema'] ) {
        $output .= ' itemscope itemtype="https://schema.org/Article"';
    }

    $output .= '>';

    // Add hidden main heading for screen readers and SEO
    $store_name = '';
    if ( function_exists( 'halacoupon_store' ) ) {
        $store = halacoupon_store();
        if ( $store && $store->is_store() ) {
            $store_name = $store->get_display_name();
        }
    }

    $output .= '<header class="store-content-header sr-only">';
    $output .= '<h1 id="' . esc_attr( $container_id ) . '-title" itemprop="headline">';
    $output .= esc_html( sprintf( __( 'Detailed Information About %s', 'halacoupon' ), $store_name ) );
    $output .= '</h1>';

    // Add reading time and metadata for SEO
    if ( $args['add_reading_time'] ) {
        $output .= '<div class="store-content-meta" itemprop="about">';
        $output .= '<span class="reading-time" aria-label="' . esc_attr( sprintf( __( 'Estimated reading time: %d minutes', 'halacoupon' ), $reading_time ) ) . '">';
        $output .= '<svg class="reading-time-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">';
        $output .= '<path d="M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z"/>';
        $output .= '<path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z"/>';
        $output .= '</svg>';
        $output .= esc_html( sprintf( _n( '%d min read', '%d min read', $reading_time, 'halacoupon' ), $reading_time ) );
        $output .= '</span>';
        $output .= '</div>';
    }

    $output .= '</header>';

    // Add table of contents navigation if requested and multiple blocks exist
    if ( $args['show_navigation'] && count( $blocks ) > 1 ) {
        $output .= halacoupon_render_content_blocks_navigation( $blocks, $args );
    }

    // Add main content wrapper with proper landmarks
    $output .= '<main class="store-content-blocks-wrapper" role="main" aria-label="' . esc_attr__( 'Store information content', 'halacoupon' ) . '">';

    // Add content blocks with enhanced structure
    foreach ( $blocks as $index => $block ) {
        $output .= $block['block_html'];
    }

    $output .= '</main>';

    // Add content end marker for skip links
    $output .= '<div id="store-content-end" class="sr-only" tabindex="-1">';
    $output .= esc_html__( 'End of store content', 'halacoupon' );
    $output .= '</div>';

    // Add structured data for SEO
    if ( $args['add_schema'] ) {
        $output .= '<script type="application/ld+json">';
        $output .= wp_json_encode( array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => sprintf( __( 'Information About %s', 'halacoupon' ), $store_name ),
            'description' => sprintf( __( 'Comprehensive information and details about %s store.', 'halacoupon' ), $store_name ),
            'wordCount' => $total_words,
            'timeRequired' => 'PT' . $reading_time . 'M',
            'inLanguage' => get_locale(),
            'isAccessibleForFree' => true,
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo( 'name' ),
                'url' => home_url()
            )
        ), JSON_UNESCAPED_UNICODE );
        $output .= '</script>';
    }

    $output .= '</' . esc_attr( $args['wrapper_element'] ) . '>';

    return $output;
}


/**
 * Render table of contents navigation for content blocks
 *
 * Creates a collapsible, accessible table of contents with proper SEO structure
 * and ARIA attributes for screen readers.
 *
 * @since 1.0.0
 * @param array $blocks Array of content blocks
 * @param array $args Rendering arguments
 * @return string Navigation HTML with table of contents structure
 */
function halacoupon_render_content_blocks_navigation( $blocks, $args ) {

    // Filter blocks to only include those with headings
    $nav_blocks = array_filter( $blocks, function( $block ) {
        return $block['has_heading'] && ! empty( $block['heading'] );
    });

    // Return empty if no navigable blocks
    if ( empty( $nav_blocks ) ) {
        return '';
    }

    // Generate unique IDs for accessibility
    $toc_id = 'store-toc-' . uniqid();
    $toggle_id = 'store-toc-toggle-' . uniqid();
    $list_id = 'store-toc-list-' . uniqid();

    // Count blocks for accessibility announcement
    $block_count = count( $nav_blocks );

    // Start building the table of contents
    $navigation = '<aside class="' . esc_attr( $args['navigation_class'] ) . ' store-toc-container" role="complementary" aria-labelledby="' . esc_attr( $toggle_id ) . '">';

    // Add structured data for SEO
    $navigation .= '<div itemscope itemtype="https://schema.org/SiteNavigationElement">';

    // Table of contents header with toggle button
    $navigation .= '<header class="store-toc-header">';
    $navigation .= '<button type="button" ';
    $navigation .= 'id="' . esc_attr( $toggle_id ) . '" ';
    $navigation .= 'class="store-toc-toggle" ';
    $navigation .= 'aria-expanded="false" ';
    $navigation .= 'aria-controls="' . esc_attr( $list_id ) . '" ';
    $navigation .= 'aria-describedby="' . esc_attr( $toc_id ) . '-desc">';

    // Toggle button content with icon
    $navigation .= '<span class="store-toc-icon" aria-hidden="true">';
    $navigation .= '<svg class="toc-icon-collapsed" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">';
    $navigation .= '<path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>';
    $navigation .= '</svg>';
    $navigation .= '<svg class="toc-icon-expanded" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">';
    $navigation .= '<path d="M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z"/>';
    $navigation .= '</svg>';
    $navigation .= '</span>';

    $navigation .= '<span class="store-toc-title">';
    $navigation .= esc_html__( 'Table of Contents', 'halacoupon' );
    $navigation .= '</span>';

    $navigation .= '<span class="store-toc-count" aria-label="' . esc_attr( sprintf( _n( '%d section', '%d sections', $block_count, 'halacoupon' ), $block_count ) ) . '">';
    $navigation .= '(' . esc_html( $block_count ) . ')';
    $navigation .= '</span>';

    $navigation .= '</button>';
    $navigation .= '</header>';

    // Hidden description for screen readers
    $navigation .= '<div id="' . esc_attr( $toc_id ) . '-desc" class="sr-only">';
    $navigation .= esc_html( sprintf( __( 'Navigate to different sections of the store information. Contains %d sections.', 'halacoupon' ), $block_count ) );
    $navigation .= '</div>';

    // Collapsible navigation list
    $navigation .= '<nav class="store-toc-nav" role="navigation" aria-label="' . esc_attr__( 'Store content sections', 'halacoupon' ) . '">';
    $navigation .= '<ol id="' . esc_attr( $list_id ) . '" class="store-content-nav-list" aria-hidden="true">';

    // Generate navigation items with URL-encoded links
    $section_number = 1;
    foreach ( $nav_blocks as $block ) {
        $navigation .= '<li class="store-content-nav-item" itemprop="name">';

        // Use URL-encoded slug for SEO-friendly URLs
        $link_url = ! empty( $block['seo_slug'] ) ? '#' . $block['seo_slug'] : '#' . $block['block_id'];

        // Add section number and data attributes
        $navigation .= '<a href="' . esc_attr( $link_url ) . '" ';
        $navigation .= 'class="store-content-nav-link" ';
        $navigation .= 'itemprop="url" ';
        $navigation .= 'data-target-id="' . esc_attr( $block['block_id'] ) . '" ';
        $navigation .= 'data-seo-slug="' . esc_attr( ! empty( $block['seo_slug'] ) ? $block['seo_slug'] : '' ) . '" ';
        $navigation .= 'aria-describedby="' . esc_attr( $block['block_id'] ) . '-desc">';

        $navigation .= '<span class="nav-section-number" aria-hidden="true">';
        $navigation .= esc_html( $section_number ) . '.';
        $navigation .= '</span>';

        $navigation .= '<span class="nav-section-title">';
        $navigation .= esc_html( $block['heading'] );
        $navigation .= '</span>';

        $navigation .= '</a>';

        // Description for screen readers
        $navigation .= '<span id="' . esc_attr( $block['block_id'] ) . '-desc" class="sr-only">';
        $navigation .= esc_html( sprintf( __( 'Section %d: %s', 'halacoupon' ), $section_number, $block['heading'] ) );
        $navigation .= '</span>';

        $navigation .= '</li>';
        $section_number++;
    }

    $navigation .= '</ol>';
    $navigation .= '</nav>';

    // Add skip link for keyboard users
    $navigation .= '<div class="store-toc-skip">';
    $navigation .= '<a href="#store-content-end" class="skip-link">';
    $navigation .= esc_html__( 'Skip table of contents', 'halacoupon' );
    $navigation .= '</a>';
    $navigation .= '</div>';

    $navigation .= '</div>'; // Close structured data
    $navigation .= '</aside>';

    return $navigation;
}


/**
 * Get store content blocks as array (utility function)
 *
 * @since 1.0.0
 * @param int|null $store_id Store ID (defaults to current store)
 * @param array $args Optional arguments
 * @return array Array of content blocks
 */
function halacoupon_get_store_content_blocks( $store_id = null, $args = array() ) {

    // Get store content
    if ( $store_id ) {
        $store = halacoupon_store( $store_id );
    } else {
        $store = halacoupon_store();
    }

    if ( ! $store || ! $store->is_store() ) {
        return array();
    }

    $content = $store->get_extra_info();

    return halacoupon_parse_store_content_blocks( $content, $args );
}


/**
 * Check if store has structured content blocks
 *
 * @since 1.0.0
 * @param int|null $store_id Store ID (defaults to current store)
 * @return bool True if store has H2-based content blocks
 */
function halacoupon_store_has_content_blocks( $store_id = null ) {

    $blocks = halacoupon_get_store_content_blocks( $store_id );

    // Check if we have multiple blocks or blocks with headings
    if ( count( $blocks ) > 1 ) {
        return true;
    }

    if ( count( $blocks ) === 1 && $blocks[0]['has_heading'] ) {
        return true;
    }

    return false;
}


/**
 * Get specific content block by heading or ID
 *
 * @since 1.0.0
 * @param string $identifier Block heading text or block ID
 * @param int|null $store_id Store ID (defaults to current store)
 * @return array|false Content block array or false if not found
 */
function halacoupon_get_store_content_block( $identifier, $store_id = null ) {

    $blocks = halacoupon_get_store_content_blocks( $store_id );

    foreach ( $blocks as $block ) {
        // Match by heading text
        if ( $block['heading'] === $identifier ) {
            return $block;
        }

        // Match by block ID
        if ( $block['block_id'] === $identifier ) {
            return $block;
        }

        // Match by sanitized heading (for URL-friendly matching)
        $sanitized_heading = sanitize_title( $block['heading'] );
        if ( $sanitized_heading === $identifier ) {
            return $block;
        }
    }

    return false;
}


/**
 * Setup store data
 *
 * @since 1.0.0
 *
 * @param null store
 * @param bool       $setup_data
 * @return null
 */
function halacoupon_setup_store( $store = null ) {
	if ( $store instanceof halacoupon_Store ) {
		return $store;
	}
	$_term = get_term( $store, 'coupon_store' );
	$GLOBALS['store'] = new halacoupon_Store( $_term );
}

/**
 * Alias of halacoupon_Store
 *
 * This function Must use after halacoupon_setup_coupon
 *
 * @since 1.0.0
 * @see halacoupon_Store
 * @see  halacoupon_setup_coupon()
 *
 * @param null $store
 * @return null|halacoupon_Store
 */
function halacoupon_store( $store = null ) {

	// check if store is set.
	if ( ! $store ) {
		$store = isset( $GLOBALS['store'] ) ? $GLOBALS['store'] : '';
	}

	if ( $store instanceof halacoupon_Store ) {
		return $store;
	}

	return new halacoupon_Store( $store );
}


/**
 * Add Edit Store CSS
 */
function halacoupon_store_admin_list_posts_css() {
	wp_enqueue_style( 'halacoupon-admin', get_template_directory_uri() . '/assets/css/admin.css' );
}
add_action( 'admin_head-edit-tags.php', 'halacoupon_store_admin_list_posts_css' );
add_action( 'admin_print_scripts-term.php', 'halacoupon_store_admin_list_posts_css' );
add_action('admin_enqueue_scripts', 'halacoupon_store_admin_list_posts_css');

/**
 * Return Search stores data
 *
 * @param array $args
 * @param array $data
 * @return array
 */
function halacoupon_get_stores_search( $args = array() ) {
	$stores = halacoupon_get_stores( $args );
	$r = array();
	foreach ( $stores as $k => $store ) {
		halacoupon_setup_store( $store );
		$n = halacoupon_store()->count;
		$r[] = array(
			'id'    => halacoupon_store()->term_id,
			'title' => halacoupon_store()->name,
			'url'   => halacoupon_store()->get_url(),
			'home'  => halacoupon_store()->get_home_url(),
			'image' => halacoupon_store()->get_thumbnail(),
			'description' => sprintf( _n( '%d Coupon', '%d Coupons', $n, 'halacoupon' ), $n ),
		);
	}
	return $r;
}



/**
 * Custom query in single taxonomy coupon_store
 *
 * @param $query
 */
function halacoupon_get_all_coupons( $query ) {

	if ( $query->is_tax( 'coupon_store' ) && $query->is_main_query() ) {
		$query->set( 'posts_per_page', 0 );
		$query->set( 'post_type', 'coupon' );
	}

}
// add_action( 'pre_get_posts', 'halacoupon_get_all_coupons' );
if ( is_admin() ) {
	require_once get_template_directory() . '/inc/core/term-editor.php';
}


/**
 * Auto store thumbnail if set
 *
 * Hook cmb2_save_{object_type}_fields
 */
function halacoupon_store_auto_thumbnail( $object_id, $cmb_id, $update = null, $cmb = null ) {
	if ( $cmb_id == '_ags_store_meta' ) {
		$data = wp_parse_args(
			$_POST,
			array(
				'_ags_auto_thumbnail' => '',
				'_ags_store_url'      => '',
				'_ags_store_image_id' => '',
			)
		);
		if ( $data['_ags_auto_thumbnail'] == 'on' && intval( $data['_ags_store_image_id'] ) <= 0 ) {
			$id = halacoupon_download_webshoot( $data['_ags_store_url'] );
			if ( $id ) {
				update_term_meta( $object_id, '_ags_store_image', wp_get_attachment_url( $id ) );
				update_term_meta( $object_id, '_ags_store_image_id', $id );
			}
		}
	}
}
add_action( 'cmb2_save_term_fields', 'halacoupon_store_auto_thumbnail', 60, 4 );


/**
 * Get coupons in store
 *
 * @param null   $store_id
 * @param int    $number
 * @param string $type empty string|active|unpopular|expires
 * @return array
 */
function halacoupon_get_store_coupons( $store_id = null, $number = 20, $paged = null, $type = '', $coupon_type = null ) {
	// get store id if in the loop and not set
	global $wp_query;

	if ( is_null( $coupon_type ) ) {
		$coupon_type = 'all';
		$available_coupon_type = halacoupon_get_coupon_types();
		$get_coupon_var = ( isset( $_GET['coupon_type'] ) ) ? sanitize_text_field( wp_unslash( $_GET['coupon_type'] ) ) : '';
		if ( isset( $get_coupon_var ) && array_key_exists( $get_coupon_var, $available_coupon_type ) ) {
			$coupon_type = $get_coupon_var;
		}
	}

	$args = array(
		'post_type'      => 'coupon',
		'post_status'    => 'publish',
		'posts_per_page' => $number,
		'paged' => $paged,
		'tax_query' => array(
			'relation' => 'AND',
			array(
				'taxonomy' => 'coupon_store',
				'field'    => 'term_id',
				'terms'    => array( $store_id ),
				'operator' => 'IN',
			),
		),
		'orderby'         => 'menu_order date',
		'order'           => 'asc',
	);

	$now = current_time( 'timestamp' );
	if ( 'active' == $type ) {

		$unpopular_coupons_percent = apply_filters( 'unpopular_coupons_percent', 20 );
		$args['meta_query'] = array(
			'relation' => 'AND',
			array(
				'relation' => 'AND',
				array(
					'key' => '_ags_percent_success',
					'value' => $unpopular_coupons_percent,
					'type' => 'NUMERIC',
					'compare' => '>=',
				),
				array(
					'key' => '_ags_percent_success',
					'value' => '',
					'compare' => '!=',
				),
			),
			array(
				'relation' => 'OR',
				array(
					'key' => '_ags_expires',
					'value' => $now,
					'type' => 'NUMERIC',
					'compare' => '>=',
				),
				array(
					'key' => '_ags_expires',
					'value' => '',
					'compare' => '=',
				),
			),
		);

	} elseif ( 'unpopular' == $type ) {

		$unpopular_coupons_percent = apply_filters( 'unpopular_coupons_percent', 20 );
		$args['meta_query'] = array(
			'relation' => 'AND',
			array(
				'relation' => 'AND',
				array(
					'key' => '_ags_percent_success',
					'value' => floatval( $unpopular_coupons_percent ),
					'type' => 'NUMERIC',
					'compare' => '<',
				),
				array(
					'key' => '_ags_percent_success',
					'value' => '',
					'compare' => '!=',
				),
			),
			array(
				'relation' => 'OR',
				array(
					'key' => '_ags_expires',
					'value' => $now,
					'type' => 'NUMERIC',
					'compare' => '>=',
				),
				array(
					'key' => '_ags_expires',
					'value' => '',
					'compare' => '=',
				),
			),
		);

		$args['meta_key'] = '_ags_percent_success';
		$args['orderby'] = 'meta_value';
		$args['meta_type'] = 'NUMERIC';
		$args['order']  = 'desc';

	} elseif ( 'expires' == $type ) {

		$args['meta_query'] = array(
			'relation' => 'AND',
			array(
				'key' => '_ags_expires',
				'compare' => 'EXISTS',
			),
			array(
				'key' => '_ags_expires',
				'type' => 'NUMERIC',
				'value' => 0,
				'compare' => '>',
			),
			array(
				'key' => '_ags_expires',
				'value' => $now,
				'type' => 'NUMERIC',
				'compare' => '<=',
			),
		);

		$args['meta_key'] = '_ags_expires';
		$args['orderby'] = 'meta_value';
		$args['meta_type'] = 'NUMERIC';
		$args['order']  = 'desc';
	}

	if ( 'all' != $coupon_type ) {
		$args['meta_query'][] = array(
			'key' => '_ags_coupon_type',
			'value' => $coupon_type,
			'compare' => '=',
		);
	}
	// st_debug( $args );
	$wp_query = new WP_Query( $args );
	// st_debug( $wp_query->request );
	return $wp_query->get_posts();
}

/**
 * Pre get post for store
 *
 * @since 1.2.6
 */
add_action( 'pre_get_posts', 'halacoupon_pre_get_store_posts', PHP_INT_MAX );
if ( ! function_exists( 'halacoupon_pre_get_store_posts' ) ) {
	function halacoupon_pre_get_store_posts( $query ) {
		if ( $query->is_tax( 'coupon_store' ) && $query->is_main_query() ) {

			$coupon_type = 'all';
			$available_coupon_type = halacoupon_get_coupon_types();
            $store_loop_order = 'desc';
			$get_coupon_var = ( isset( $_GET['coupon_type'] ) ) ? sanitize_text_field( wp_unslash( $_GET['coupon_type'] ) ) : '';
			$filtered_sortby = ( isset( $_GET['sort_by'] ) ) ? sanitize_text_field( wp_unslash( $_GET['sort_by'] ) ) : 'newest';
			$filtered_cat = ( isset( $_GET['coupon_cat'] ) ) ? sanitize_text_field( wp_unslash( $_GET['coupon_cat'] ) ) : '';
			$filtered_cat = array_filter( explode( ',', $filtered_cat ) );
			if ( isset( $get_coupon_var ) && array_key_exists( $get_coupon_var, $available_coupon_type ) ) {
				$coupon_type = $get_coupon_var;
			}
			$paged = ( get_query_var( 'paged' ) ) ? get_query_var( 'paged' ) : 1;
			$number_active =  10;
			$query->set( 'posts_per_page', $number_active );

			$query->set( 'paged', $paged );
			$now = current_time( 'timestamp' );
			$meta_query = array();
			if ( 'all' != $coupon_type ) {
				$meta_query = array(
					'relation' => 'AND',
					array(
						'key' => '_ags_coupon_type',
						'value' => $coupon_type,
						'compare' => '=',
					),
				);
			}

			if ( is_array( $filtered_cat ) && ! empty( $filtered_cat ) ) { // phpcs:ignore
				$query->set(
					'tax_query',
					array(
						'relation' => 'AND',
						array(
							'taxonomy' => 'coupon_category',
							'field'    => 'slug',
							'terms'    => $filtered_cat,
							'operator' => 'IN',
						),
					)
				);
			}
			switch ( $filtered_sortby ) {
                case 'newest':
                    $query->set( 'orderby', 'date' );
                    $query->set( 'order', 'desc' );
                    break;
				case 'popularity':
					$query->set( 'meta_key', '_ags_used' );
					$query->set( 'orderby', 'meta_value_num' );
					$query->set( 'order', 'desc' );
					$query->set( 'meta_type', 'NUMERIC' );
					break;
				case 'ending-soon':
					$query->set( 'meta_key', '_ags_expires' );
					$query->set( 'meta_value', $now );
					$query->set( 'meta_compare', '>=' );
					$query->set( 'meta_type', 'NUMERIC' );
					$query->set( 'orderby', 'meta_value_num' );
					$query->set( 'order', 'asc' );
					break;
				case 'expired':
					$meta_query[] = array(
						array(
							'key' => '_ags_expires',
							'compare' => 'EXISTS',
						),
						array(
							'key' => '_ags_expires',
							'type' => 'NUMERIC',
							'value' => 0,
							'compare' => '>',
						),
						array(
							'key' => '_ags_expires',
							'value' => $now,
							'type' => 'NUMERIC',
							'compare' => '<=',
						),
					);
					$query->set( 'meta_key', '_ags_expires' );
					$query->set( 'orderby', 'meta_value' );
					$query->set( 'meta_type', 'NUMERIC' );
					$query->set( 'order', 'desc' );
					break;
				default:
					$query->set( 'orderby', 'date' );
                    $query->set( 'order', $store_loop_order );
					break;
			}
			if ( ! empty( $meta_query ) ) {
				$query->set( 'meta_query', $meta_query );
			}
		}
		return $query;
	}
}

/**
 * Get url with page num
 *
 * @since 1.2.6
 * @return string
 */
function halacoupon_get_store_page_num( $pagenum, $base ) {
	global $wp_rewrite;
	$pagenum = (int) $pagenum;

	$request = remove_query_arg( 'paged', $base );
	$request = str_replace( $base, '', $request );

	$home_root = parse_url( home_url() );
	$home_root = ( isset( $home_root['path'] ) ) ? $home_root['path'] : '';
	$home_root = preg_quote( $home_root, '|' );

	$request = preg_replace( '|^' . $home_root . '|i', '', $request );
	$request = preg_replace( '|^/+|', '', $request );

	if ( ! $wp_rewrite->using_permalinks() ) {
		if ( $pagenum > 1 ) {
			$result = add_query_arg( 'paged', $pagenum, $base . $request );
		} else {
			$result = $base . $request;
		}
	} else {
		$base_url = explode( '?', $base );
		$base = $base_url[0];

		$qs_regex = '|\?.*?$|';
		preg_match( $qs_regex, $request, $qs_match );

		if ( ! empty( $qs_match[0] ) ) {
			$query_string = $qs_match[0];
			$request = preg_replace( $qs_regex, '', $request );
		} else {
			$query_string = '';
			if ( isset( $base_url[1] ) && '' != $base_url[1] ) {
				$query_string = '?' . $base_url[1];
			}
		}

		$request = preg_replace( "|$wp_rewrite->pagination_base/\d+/?$|", '', $request );
		$request = preg_replace( '|^' . preg_quote( $wp_rewrite->index, '|' ) . '|i', '', $request );
		$request = ltrim( $request, '/' );

		if ( $wp_rewrite->using_index_permalinks() && ( $pagenum > 1 || '' != $request ) ) {
			$base .= $wp_rewrite->index . '/';
		}

		if ( $pagenum > 1 ) {
			$request = ( ( ! empty( $request ) ) ? trailingslashit( $request ) : $request ) . user_trailingslashit( $wp_rewrite->pagination_base . '/' . $pagenum, 'paged' );
		}

		$result = $base . $request . $query_string;
	}

	$result = apply_filters( 'halacoupon_get_pagenum_link', $result );
	return esc_url_raw( $result );
}

/**
 * Get Coupon Categories From a store ID
 *
 * @since 1.2.6
 * @return array()
 */
function halacoupon_get_category_from_store( $store_id ) {
	global $wpdb;

	$table_term_relationships = $wpdb->prefix . 'term_relationships';
	$table_terms = $wpdb->prefix . 'terms';
	$table_posts = $wpdb->prefix . 'posts';
	$table_term_taxonomy = $wpdb->prefix . 'term_taxonomy';

	$result = $wpdb->get_results(
		$wpdb->prepare(
			"
				SELECT DISTINCT t.term_id, t.*, tt.*
				FROM {$table_terms} AS t 
				INNER JOIN {$table_term_taxonomy} AS tt ON t.term_id = tt.term_id
				INNER JOIN {$table_term_relationships} AS tr ON tr.term_taxonomy_id = tt.term_taxonomy_id
				WHERE tt.taxonomy IN ('coupon_category') AND tr.object_id IN (
					SELECT ID 
					FROM {$table_posts} p 
					WHERE p.post_status IN ('publish') AND ID IN ( 
						SELECT object_id 
						FROM {$table_term_relationships} tr2 
						WHERE tr2.term_taxonomy_id = %d )
				)
				ORDER BY t.name ASC
			",
			$store_id
		)
	);

	return $result;
}

/**
 * Get Coupon Categories From a store ID
 *
 * @since 1.2.6
 */
function halacoupon_store_cat_filter() {
	if ( is_tax( 'coupon_store' ) ) {
		$store_id = get_queried_object_id();
		$is_enable_filter = halacoupon_get_option( 'store_enable_sidebar_filter', true );
		$filter_title = halacoupon_get_option( 'store_sidebar_filter_title', 'Filter Store' );
		if ( $is_enable_filter ) {
			halacoupon_get_store_cat_filter_html( $store_id, $filter_title );
		}
	}
}
add_action( 'halacoupon_coupon_store_before_sidebar', 'halacoupon_store_cat_filter', 10 );

/**
 * Get Coupon Categories From a store ID
 *
 * @since 1.2.6
 */

function halacoupon_get_store_cat_filter_html( $store_id, $title = '' ) {
	$categories_of_store = halacoupon_get_category_from_store( $store_id );
	?>
	<aside class="widget widget_store_cat_filter">
		<h4 class="widget-title"><?php echo wp_kses_post( $title ); ?></h4> 
		<div class="shadow-box">
			<?php if ( is_array( $categories_of_store ) && ! empty( $categories_of_store ) ) { ?>
				<?php
					$filtered_sortby = ( isset( $_GET['sort_by'] ) ) ? sanitize_text_field( wp_unslash( $_GET['sort_by'] ) ) : 'default';
					$filtered_cat = ( isset( $_GET['coupon_cat'] ) ) ? sanitize_text_field( wp_unslash( $_GET['coupon_cat'] ) ) : '';
					$filtered_cat = explode( ',', $filtered_cat );
				?>
				<div class="store-cat-filter-wrapper store-cat-filter ui list">
					<h5><?php echo esc_html_e( 'Categories', 'halacoupon' ); ?></h5>
					<?php foreach ( $categories_of_store as $cat ) { ?>
						<div class="item">
							<label for="store-cat-filter-<?php echo esc_attr( $cat->term_id ); ?>">
								<input 
									id="store-cat-filter-<?php echo esc_attr( $cat->term_id ); ?>" 
									type="checkbox" 
									class="halacoupon-cat-filter-item store-filter-cat store-filter-cat-item" 
									name="halacoupon_cat_filter" value="<?php echo esc_attr( $cat->slug ); ?>" 
									<?php if ( is_array( $filtered_cat ) && in_array( $cat->slug, $filtered_cat ) ) {
										echo ' checked="checked" '; } ?> />
								<span class="cat-filter-name"><?php echo esc_html( $cat->name ); ?></span>
							</label>
						</div>
					<?php } ?>
				</div>
			<?php } ?>
			<?php
				$current_url = halacoupon_current_url();
				$filter_url = add_query_arg( array( 'coupon_type' => '' ), $current_url );
				$base_pagenum = get_pagenum_link( 1 );
				global $wp_query;
				$paged = ( get_query_var( 'paged' ) ) ? get_query_var( 'paged' ) : 1;
				$max_pages = $wp_query->max_num_pages;
			if ( $max_pages >= ( $paged + 1 ) ) {
				$next_page = ( $paged + 1 );
			} else {
				$next_page = $paged;
			}

			?>
			<input type="hidden" class="store_base_pagenum_url" name="store_base_pagenum_url" value="<?php echo get_pagenum_link( 1, false ); ?>"/>
			<input type="hidden" class="store_pagenum_url" name="store_pagenum_url" value="<?php echo get_pagenum_link( $paged ); ?>"/>
			<input type="hidden" class="store_next_pagenum_url" name="store_next_pagenum_url" value="<?php echo get_pagenum_link( $next_page ); ?>"/>

			<div class="store-filter-sortby-wrapper ui list">
				<h5><?php echo esc_html_e( 'Sort by', 'halacoupon' ); ?></h5>
                <div class="item">
                    <label for="store-sortby-default">
                        <input id="store-sortby-default" <?php checked( 'default', $filtered_sortby ); ?> type="radio" class="store-filter-sortby sortby-default store-filter-cat-item" name="store-filter-sortby" value="default" />
                        <span class="filter-sortby-name"><?php echo esc_html_e( 'Default', 'halacoupon' ); ?></span>
                    </label>
                </div>
				<div class="item">
					<label for="store-sortby-newest">
						<input id="store-sortby-newest" <?php checked( 'newest', $filtered_sortby ); ?> type="radio" class="store-filter-sortby sortby-newest store-filter-cat-item" name="store-filter-sortby" value="newest" />
						<span class="filter-sortby-name"><?php echo esc_html_e( 'Newest', 'halacoupon' ); ?></span>
					</label>
				</div>
				<div class="item">
					<label for="store-sortby-popularity">
						<input id="store-sortby-popularity" <?php checked( 'popularity', $filtered_sortby ); ?> type="radio" class="store-filter-sortby sortby-popularity store-filter-cat-item" name="store-filter-sortby" value="popularity" />
						<span class="filter-sortby-name"><?php echo esc_html_e( 'Popularity', 'halacoupon' ); ?></span>
					</label>
				</div>
				<div class="item">
					<label for="store-sortby-endingsoon">
						<input id="store-sortby-endingsoon" <?php checked( 'ending-soon', $filtered_sortby ); ?> type="radio" class="store-filter-sortby sortby-ending-soon store-filter-cat-item" name="store-filter-sortby" value="ending-soon" />
						<span class="filter-sortby-name"><?php echo esc_html_e( 'Ending Soon', 'halacoupon' ); ?></span>
					</label>
				</div>
				<div class="item">
					<label for="store-sortby-expired">
						<input id="store-sortby-expired" <?php checked( 'expired', $filtered_sortby ); ?> type="radio" class="store-filter-sortby sortby-expired store-filter-cat-item" name="store-filter-sortby" value="expired" />
						<span class="filter-sortby-name"><?php echo esc_html_e( 'Expired', 'halacoupon' ); ?></span>
					</label>
				</div>
			</div>
		</div>
	</aside>
	<?php
}


function halacoupon_get_filter_coupon_count() {
	$now = current_time( 'timestamp' );
	$store_id = get_queried_object_id();
	$query = array(
		'post_type' => 'coupon',
		'posts_per_page' => -1,
		'post_status' => 'publish',
		'tax_query' => array(
			'relation' => 'AND',
			array(
				'taxonomy' => 'coupon_store',
				'field'    => 'term_id',
				'terms'    => array( $store_id ),
				'operator' => 'IN',
			),
		),
	);
	$available_coupon_type = halacoupon_get_coupon_types();

	$filtered_sortby = ( isset( $_GET['sort_by'] ) ) ? sanitize_text_field( wp_unslash( $_GET['sort_by'] ) ) : '';
	$filtered_cat = ( isset( $_GET['coupon_cat'] ) ) ? sanitize_text_field( wp_unslash( $_GET['coupon_cat'] ) ) : '';
	$meta_query = $return = array();
	$filtered_cat = array_filter( explode( ',', $filtered_cat ) );

	if ( '' != $filtered_sortby || ! empty( $filtered_cat ) ) {
		if ( 'ending-soon' == $filtered_sortby ) {
			$query['meta_key'] = '_ags_expires';
			$query['meta_value'] = $now;
			$query['meta_compare'] = '>=';
			$query['meta_type'] = 'NUMERIC';
			$query['orderby'] = 'meta_value_num';
			$query['order'] = 'asc';
		} elseif ( 'expired' == $filtered_sortby ) {
			$meta_query = array(
				'relation' => 'AND',
				array(
					'key' => '_ags_expires',
					'compare' => 'EXISTS',
				),
				array(
					'key' => '_ags_expires',
					'type' => 'NUMERIC',
					'value' => 0,
					'compare' => '>',
				),
				array(
					'key' => '_ags_expires',
					'value' => $now,
					'type' => 'NUMERIC',
					'compare' => '<=',
				),
			);

			$query['meta_key'] = '_ags_expires';
			$query['orderby'] = 'meta_value';
			$query['meta_type'] = 'NUMERIC';
			$query['order']  = 'desc';
		}

		if ( is_array( $filtered_cat ) && ! empty( $filtered_cat ) ) { // phpcs:ignore
			$query['tax_query'][] = array(
				'taxonomy' => 'coupon_category',
				'field'    => 'slug',
				'terms'    => $filtered_cat,
				'operator' => 'IN',
			);
		}

		foreach ( $available_coupon_type as $k => $v ) {
			$meta_query['query_by_type'] = array(
				'relation' => 'AND',
				array(
					'key' => '_ags_coupon_type',
					'value' => $k,
					'compare' => '=',
				),
			);
			$query['meta_query'] = $meta_query;
			$count_type = new WP_Query( $query );
			$return[ $k ] = $count_type->found_posts;
			wp_reset_postdata();
		}
		$count_all = 0;
		if ( ! empty( $return ) ) {
			foreach ( $return as $value ) {
				$count_all += absint( $value );
			}
			$return['all'] = $count_all;
		}
	}
	return $return;
}


/**
 * Create SEO-optimized block ID with proper URL encoding like Google
 *
 * @since 1.0.0
 * @param string $heading_text The heading text
 * @param int $counter Block counter for fallback
 * @return array Array with safe_id for CSS and seo_slug for URLs
 */
function halacoupon_create_seo_block_id( $heading_text, $counter = 1 ) {

    // Remove HTML tags and normalize whitespace
    $clean_text = trim( preg_replace( '/\s+/', ' ', strip_tags( $heading_text ) ) );

    if ( empty( $clean_text ) ) {
        return array(
            'safe_id'   => 'section-' . $counter,
            'seo_slug'  => 'section-' . $counter,
            'encoded'   => false
        );
    }

    // Create URL-encoded slug (like Google does)
    $seo_slug = halacoupon_create_url_encoded_slug( $clean_text );

    // Create safe CSS ID (for JavaScript selectors)
    $safe_id = halacoupon_create_safe_css_id( $clean_text, $counter );

    return array(
        'safe_id'   => $safe_id,
        'seo_slug'  => $seo_slug,
        'encoded'   => true,
        'original'  => $clean_text
    );
}

/**
 * Create URL-encoded slug like Google does for Arabic and international content
 *
 * @since 1.0.0
 * @param string $text The text to convert to URL-encoded slug
 * @return string URL-encoded slug
 */
function halacoupon_create_url_encoded_slug( $text ) {

    // Normalize whitespace and trim
    $text = trim( preg_replace( '/\s+/', ' ', $text ) );

    // Replace spaces with hyphens
    $text = str_replace( ' ', '-', $text );

    // URL encode the text (like Google does)
    $encoded = rawurlencode( $text );

    // Clean up multiple hyphens
    $encoded = preg_replace( '/-+/', '-', $encoded );
    $encoded = trim( $encoded, '-' );

    return $encoded;
}

/**
 * Create safe CSS ID for JavaScript selectors (fallback for complex content)
 *
 * @since 1.0.0
 * @param string $text The text to convert
 * @param int $counter Counter for uniqueness
 * @return string Safe CSS ID
 */
function halacoupon_create_safe_css_id( $text, $counter = 1 ) {

    // For complex international content, use counter-based approach
    if ( preg_match( '/[\x{0600}-\x{06FF}\x{0750}-\x{077F}\x{08A0}-\x{08FF}\x{FB50}-\x{FDFF}\x{FE70}-\x{FEFF}]/u', $text ) ) {
        // Arabic content - use counter with hash
        $hash = substr( md5( $text ), 0, 6 );
        return 'section-' . $counter . '-' . $hash;
    }

    // For Latin content, create clean slug
    $slug = sanitize_title( $text );

    if ( empty( $slug ) || strlen( $slug ) < 3 ) {
        return 'section-' . $counter;
    }

    // Ensure valid CSS ID format
    $slug = substr( $slug, 0, 50 );
    if ( ! preg_match( '/^[a-zA-Z]/', $slug ) ) {
        $slug = 'section-' . $slug;
    }

    return $slug;
}




