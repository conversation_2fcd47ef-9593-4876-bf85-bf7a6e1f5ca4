<?php
/**
 * AJAX Handler for HalaCoupon Theme
 * 
 * Handles all AJAX requests for coupons, stores, search, and tracking functionality.
 * Includes caching, security validation, and performance optimizations.
 *
 * @package HalaCoupon
 * @version 2.0.0
 * <AUTHOR> G
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// ============================================================================
// AJAX ACTION HOOKS
// ============================================================================

add_action('wp_ajax_halacoupon_coupon_ajax', 'halacoupon_coupon_ajax');
add_action('wp_ajax_nopriv_halacoupon_coupon_ajax', 'halacoupon_coupon_ajax');

add_action('wp_ajax_halacoupon_coupon_ajax_search', 'halacoupon_coupon_ajax_search');
add_action('wp_ajax_nopriv_halacoupon_coupon_ajax_search', 'halacoupon_coupon_ajax_search');

// ============================================================================
// CACHE MANAGEMENT
// ============================================================================

/**
 * Clear AJAX search cache when content is updated
 */
function halacoupon_clear_ajax_search_cache() {
    global $wpdb;
    
    // Delete all search cache transients
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_ajax_search_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_ajax_search_%'");
    
    // Clear object cache if available
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }
}

// Hook cache clearing to content updates
add_action('save_post_coupon', 'halacoupon_clear_ajax_search_cache');
add_action('delete_post', function($post_id) {
    if (get_post_type($post_id) === 'coupon') {
        halacoupon_clear_ajax_search_cache();
    }
});
add_action('created_coupon_store', 'halacoupon_clear_ajax_search_cache');
add_action('edited_coupon_store', 'halacoupon_clear_ajax_search_cache');
add_action('delete_coupon_store', 'halacoupon_clear_ajax_search_cache');

// ============================================================================
// MAIN AJAX HANDLER
// ============================================================================

/**
 * Main AJAX handler for coupon-related requests
 */
function halacoupon_coupon_ajax() {
    // Security validation
    $nonce = isset($_REQUEST['_wpnonce']) ? $_REQUEST['_wpnonce'] : false;
    if (!wp_verify_nonce($nonce, 'halacoupon_ajax_nonce')) {
        wp_send_json_error(['msg' => __('Security check failed.', 'halacoupon')]);
        die();
    }

    // Get action type
    $doing = isset($_REQUEST['st_doing']) ? $_REQUEST['st_doing'] : 
             (isset($_GET['st_doing']) ? $_GET['st_doing'] : 
             (isset($_POST['st_doing']) ? $_POST['st_doing'] : false));

    if (!$doing) {
        wp_send_json_error(['msg' => __('No action specified.', 'halacoupon')]);
        die();
    }

    // Route to appropriate handler
    switch ($doing) {
        case 'ajax_search':
            halacoupon_handle_ajax_search();
            break;

        case 'get_coupon_modal':
            halacoupon_handle_coupon_modal();
            break;

        case 'tracking_coupon':
            halacoupon_handle_coupon_tracking();
            break;

        case 'vote_coupon':
            halacoupon_handle_coupon_voting();
            break;

        default:
            // Allow custom actions via hooks
            do_action('halacoupon_coupon_ajax', $doing);
            wp_send_json_error(['msg' => __('Unknown request.', 'halacoupon')]);
            break;
    }
}

// ============================================================================
// AJAX HANDLERS
// ============================================================================

/**
 * Handle AJAX search requests
 */
function halacoupon_handle_ajax_search() {
    $search_term = isset($_REQUEST['s']) ? sanitize_text_field($_REQUEST['s']) : '';
    $search_term = trim($search_term);

    if (empty($search_term)) {
        wp_send_json(['success' => true, 'results' => []]);
        die();
    }

    $num_results = apply_filters('ajax_coupon_search_num_posts', 8);
    
    $args = wp_parse_args([
        'name__like' => $search_term,
        'number' => $num_results
    ], []);

    $results = [
        'success' => true,
        'results' => halacoupon_get_stores_search($args),
    ];

    $results = apply_filters('halacoupon_ajax_search_result', $results);
    wp_send_json($results);
    die();
}



/**
 * Handle coupon modal requests
 */
function halacoupon_handle_coupon_modal() {
    $hash = isset($_REQUEST['hash']) ? sanitize_text_field($_REQUEST['hash']) : '';
    $hash = str_replace('#coupon-id-', '', $hash);

    if (!is_numeric($hash)) {
        wp_send_json_error(['msg' => __('Invalid coupon ID.', 'halacoupon')]);
        die();
    }

    ob_start();

    global $post;
    $post = get_post($hash);
    
    if (!$post || $post->post_type !== 'coupon') {
        wp_send_json_error(['msg' => __('Coupon not found.', 'halacoupon')]);
        die();
    }

    halacoupon_setup_coupon($post);
    get_template_part('loop/coupon-modal');

    $content = ob_get_clean();
    wp_send_json_success($content);
    die();
}

/**
 * Handle coupon tracking requests
 */
function halacoupon_handle_coupon_tracking() {
    $nonce = isset($_REQUEST['_coupon_nonce']) ? $_REQUEST['_coupon_nonce'] : '';
    if (!wp_verify_nonce($nonce, '_coupon_nonce')) {
        wp_send_json_error(['msg' => __('Security check failed.', 'halacoupon')]);
        die();
    }

    $coupon_id = isset($_REQUEST['coupon_id']) ? intval($_REQUEST['coupon_id']) : 0;
    if (!$coupon_id) {
        wp_send_json_error(['msg' => __('Invalid coupon ID.', 'halacoupon')]);
        die();
    }

    if (class_exists('halacoupon_Coupon_Tracking')) {
        halacoupon_Coupon_Tracking::update_used($coupon_id);
        wp_send_json_success(['msg' => __('Coupon usage tracked.', 'halacoupon')]);
    } else {
        wp_send_json_error(['msg' => __('Tracking system not available.', 'halacoupon')]);
    }
    die();
}

/**
 * Handle coupon voting requests
 */
function halacoupon_handle_coupon_voting() {
    $vote_type = isset($_REQUEST['vote_type']) ? $_REQUEST['vote_type'] : 'up';
    $coupon_id = isset($_REQUEST['coupon_id']) ? intval($_REQUEST['coupon_id']) : 0;

    if (!$coupon_id) {
        wp_send_json_error(['msg' => __('Invalid coupon ID.', 'halacoupon')]);
        die();
}

    // Check if coupon exists
    $coupon = get_post($coupon_id);
    if (!$coupon || $coupon->post_type !== 'coupon') {
        wp_send_json_error(['msg' => __('Coupon not found.', 'halacoupon')]);
        die();
    }

    // Check if user has already voted
    if (function_exists('halacoupon_user_has_voted') && halacoupon_user_has_voted($coupon_id)) {
        wp_send_json_error(['msg' => __('You have already voted for this coupon.', 'halacoupon')]);
        die();
    }

    if (!class_exists('halacoupon_Coupon_Tracking')) {
        wp_send_json_error(['msg' => __('Voting system not available.', 'halacoupon')]);
        die();
    }

    $vote_value = ($vote_type === 'down') ? -1 : 1;
    
    // Record the vote using our enhanced function
    if (function_exists('halacoupon_record_user_vote')) {
        $success = halacoupon_record_user_vote($coupon_id, $vote_value);
    } else {
        // Fallback to original method
        $success = halacoupon_Coupon_Tracking::vote($coupon_id, $vote_value);
    }
    
    if ($success) {
        // Get updated vote statistics directly from database
        $upvotes = intval(get_post_meta($coupon_id, '_ags_vote_up', true));
        $downvotes = intval(get_post_meta($coupon_id, '_ags_vote_down', true));
        $total_votes = $upvotes + $downvotes;
        $success_rate = $total_votes > 0 ? ($upvotes / $total_votes) * 100 : 100;
        
        $vote_stats = [
            'upvotes' => $upvotes,
            'downvotes' => $downvotes,
            'total_votes' => $total_votes,
            'success_rate' => round($success_rate)
        ];
        
        wp_send_json_success([
            'msg' => __('Vote recorded successfully.', 'halacoupon'),
            'vote_count' => $upvotes,
            'total_votes' => $total_votes,
            'success_rate' => round($success_rate),
            'has_voted' => true,
            'stats' => $vote_stats
        ]);
    } else {
        wp_send_json_error(['msg' => __('Failed to record vote. Please try again.', 'halacoupon')]);
    }
}

// ============================================================================
// ENHANCED SEARCH FUNCTION
// ============================================================================

/**
 * Enhanced AJAX search with caching and optimization
 */
function halacoupon_coupon_ajax_search() {
    $data = [];
    $search_query = isset($_REQUEST['ajax_sc']) ? sanitize_text_field($_REQUEST['ajax_sc']) : '';
    $search_query = trim($search_query);

    if (strlen($search_query) === 0) {
        wp_send_json(['success' => true, 'results' => []]);
        die();
    }

        // Check cache first
    $cache_key = 'ajax_search_' . md5($search_query);
    $cached_results = get_transient($cache_key);

    if (false !== $cached_results) {
        wp_send_json($cached_results);
        die();
    }

    $num_results = apply_filters('ajax_coupon_search_num_posts', 8);

    // Search stores with optimization
    $store_results = halacoupon_search_stores_optimized($search_query, $num_results);
    $data = array_merge($data, $store_results);

    // Search coupons with optimization
    $coupon_results = halacoupon_search_coupons_optimized($search_query, $num_results);
    $data = array_merge($data, $coupon_results);

    // Cache results for 5 minutes
    $results = [
        'success' => true,
        'results' => $data,
        'timestamp' => microtime(true),
        'query' => $search_query,
        'count' => count($data)
    ];

    set_transient($cache_key, $results, 5 * MINUTE_IN_SECONDS);

    wp_send_json($results);
            die();
        }

/**
 * Optimized store search
 */
function halacoupon_search_stores_optimized($search_query, $limit) {
    $results = [];

    try {
        $tax_query = new WP_Term_Query([
            'taxonomy' => 'coupon_store',
            'name__like' => $search_query,
            'number' => $limit,
            'orderby' => 'name',
            'order' => 'ASC',
            'hide_empty' => false
        ]);

        $terms = $tax_query->get_terms();

        if (!empty($terms)) {
        // Pre-fetch all store thumbnails to avoid N+1 queries
            $store_thumbnails = [];
            foreach ($terms as $term) {
                halacoupon_setup_store($term);
                $store_thumbnails[$term->term_id] = halacoupon_store()->get_thumbnail('halacoupon_small_thumb', true);
        }

            foreach ($terms as $term) {
                $results[] = [
                    'id' => $term->term_id,
                    'title' => esc_html($term->name),
                    'url' => esc_url(get_term_link($term)),
                    'image' => esc_url($store_thumbnails[$term->term_id]),
                    'type' => 'store',
                    'description' => esc_html($term->description)
                ];
            }
        }
    } catch (Exception $e) {
        error_log('HalaCoupon Store Search Error: ' . $e->getMessage());
    }

    return $results;
        }

/**
 * Optimized coupon search
 */
function halacoupon_search_coupons_optimized($search_query, $limit) {
    $results = [];

    try {
        // First get IDs to avoid heavy queries
        $coupon_query = new WP_Query([
            'post_type' => 'coupon',
            's' => $search_query,
            'posts_per_page' => $limit,
            'fields' => 'ids',
            'post_status' => 'publish',
            'no_found_rows' => true, // Performance optimization
            'update_post_meta_cache' => false,
            'update_post_term_cache' => false
        ]);

        if ($coupon_query->have_posts()) {
            // Get full post objects only for found posts
            $coupon_posts = get_posts([
                'post_type' => 'coupon',
                'include' => $coupon_query->posts,
                'orderby' => 'post__in',
                'post_status' => 'publish'
            ]);

            foreach ($coupon_posts as $coupon_post) {
                halacoupon_setup_coupon($coupon_post);
                
                $discount_text = get_post_meta($coupon_post->ID, '_ags_coupon_save', true);
                $discount_text_cleaned = wp_kses_post($discount_text);

                $results[] = [
                    'id' => $coupon_post->ID,
                    'title' => get_the_title($coupon_post),
                    'url' => get_permalink($coupon_post),
                    'image' => $discount_text_cleaned,
                    'type' => 'coupon',
                    'excerpt' => wp_trim_words(get_the_excerpt($coupon_post), 20, '...')
                ];
            }
        }
        wp_reset_postdata();
    } catch (Exception $e) {
        error_log('HalaCoupon Coupon Search Error: ' . $e->getMessage());
    }

    return $results;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Log AJAX errors for debugging
 */
function halacoupon_log_ajax_error($message, $context = []) {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('HalaCoupon AJAX Error: ' . $message . ' Context: ' . json_encode($context));
    }
}

/**
 * Validate and sanitize AJAX input
 */
function halacoupon_sanitize_ajax_input($input, $type = 'text') {
    switch ($type) {
        case 'int':
            return intval($input);
        case 'float':
            return floatval($input);
        case 'email':
            return sanitize_email($input);
        case 'url':
            return esc_url_raw($input);
        case 'textarea':
            return sanitize_textarea_field($input);
        case 'html':
            return wp_kses_post($input);
        default:
            return sanitize_text_field($input);
    }
    }

/**
 * Get AJAX response headers
 */
function halacoupon_set_ajax_headers() {
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
}