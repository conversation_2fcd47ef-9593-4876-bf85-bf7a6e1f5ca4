<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */




/**
 * Setup coupon data
 *
 * @since 1.0.0
 *
 * @param null $coupon
 * @return null
 */
function halacoupon_setup_coupon( $coupon = null, $current_link = null ) {
	if ( $coupon instanceof halacoupon_Coupon ) {
		return $coupon;
	}
	global $post;
	$post = get_post( $coupon );
	setup_postdata( $post );
	$GLOBALS['coupon'] = new halacoupon_Coupon( $post, $current_link );

}

/**
 * Alias of halacoupon_Coupon
 *
 * This function MUST use in the loop. Better if after function halacoupon_setup_coupon
 *
 * @since 1.0,0
 *
 * @see halacoupon_Coupon
 * @see halacoupon_setup_coupon()
 *
 * @param null $coupon
 * @return null|halacoupon_Coupon
 */
function halacoupon_coupon( $coupon = null, $current_link = null ) {
	// check if coupon is set
	if ( ! $coupon && isset( $GLOBALS['coupon'] ) ) {
		$coupon = $GLOBALS['coupon'];
	} else {
		halacoupon_setup_coupon( $coupon );
	}

	/*
	if ( ! $coupon ) {
		halacoupon_setup_coupon( );
		$coupon = $GLOBALS['coupon'];
	}
	*/

	if ( $coupon instanceof halacoupon_Coupon ) {
		return $coupon;
	}

	return new halacoupon_Coupon( $coupon, $current_link );
}




class halacoupon_Coupon {

	/**
	 * Post ID.
	 *
	 * @var int
	 */
	public $ID;

	/**
	 * ID of post author.
	 *
	 * A numeric string, for compatibility reasons.
	 *
	 * @var string
	 */
	public $post_author = 0;

	/**
	 * The post's local publication time.
	 *
	 * @var string
	 */
	public $post_date = '0000-00-00 00:00:00';

	/**
	 * The post's GMT publication time.
	 *
	 * @var string
	 */
	public $post_date_gmt = '0000-00-00 00:00:00';

	/**
	 * The post's content.
	 *
	 * @var string
	 */
	public $post_content = '';

	/**
	 * The post's title.
	 *
	 * @var string
	 */
	public $post_title = '';

	/**
	 * The post's excerpt.
	 *
	 * @var string
	 */
	public $post_excerpt = '';

	/**
	 * The post's status.
	 *
	 * @var string
	 */
	public $post_status = 'publish';

	/**
	 * Whether comments are allowed.
	 *
	 * @var string
	 */
	public $comment_status = 'open';

	/**
	 * Whether pings are allowed.
	 *
	 * @var string
	 */
	public $ping_status = 'open';

	/**
	 * The post's password in plain text.
	 *
	 * @var string
	 */
	public $post_password = '';

	/**
	 * The post's slug.
	 *
	 * @var string
	 */
	public $post_name = '';

	/**
	 * URLs queued to be pinged.
	 *
	 * @var string
	 */
	public $to_ping = '';

	/**
	 * URLs that have been pinged.
	 *
	 * @var string
	 */
	public $pinged = '';

	/**
	 * The post's local modified time.
	 *
	 * @var string
	 */
	public $post_modified = '0000-00-00 00:00:00';

	/**
	 * The post's GMT modified time.
	 *
	 * @var string
	 */
	public $post_modified_gmt = '0000-00-00 00:00:00';

	/**
	 * A utility DB field for post content.
	 *
	 * @var string
	 */
	public $post_content_filtered = '';

	/**
	 * ID of a post's parent post.
	 *
	 * @var int
	 */
	public $post_parent = 0;

	/**
	 * The unique identifier for a post, not necessarily a URL, used as the feed GUID.
	 *
	 * @var string
	 */
	public $guid = '';

	/**
	 * A field used for ordering posts.
	 *
	 * @var int
	 */
	public $menu_order = 0;

	/**
	 * The post's type, like post or page.
	 *
	 * @var string
	 */
	public $post_type = 'post';

	/**
	 * An attachment's mime type.
	 *
	 * @var string
	 */
	public $post_mime_type = '';

	/**
	 * Cached comment count.
	 *
	 * A numeric string, for compatibility reasons.
	 *
	 * @var string
	 */
	public $comment_count = 0;

	/**
	 * Stores the post object's sanitization level.
	 *
	 * Does not correspond to a DB field.
	 *
	 * @var string
	 */
	public $filter;

	/**
	 * Check coupon thumbnail
	 *
	 * @var string
	 */
	public $_thumb_id = 0;

	/**
	 * Thumbnail ID (alternative property name)
	 *
	 * @var int
	 */
	public $_thumbnail_id = 0;

	/**
	 * Current url
	 *
	 * The current URL of page that this coupon displaying, use for social share.
	 *
	 * @var string
	 */
	public $current_url = null;

	/**
	 * Percent success of user voted
	 *
	 * @var int
	 */
	public $percent_success = 100;


 	/**
     * Whether the coupon offers free shipping.
     *
     * @var bool
     */


	/**
	* The expiration date of the coupon.
	*
	* @var string|null
	*/
	public $_ags_expires = null; // Initialize the property here

	/**
	* The code or identifier for the type of coupon.
	*
	* @var string|null
	*/
	public $_ags_coupon_type_code = null; // Initialize the property here

	/**
	* The ID or reference to a printable version of the coupon.
	*
	* @var int|null
	*/
	public $_ags_coupon_type_printable_id = null; // Initialize the property here

	/**
	* The type or category of the coupon (e.g., discount, free shipping).
	*
	* @var string|null
	*/
	public $_ags_coupon_type = null; // Initialize the property here

	/**
	* Information about whether the coupon has a printable version available.
	*
	* @var bool|null
	*/
	public $_ags_coupon_type_printable = null; // Initialize the property here

	/**
	* The store associated with the coupon.
	*
	* @var string|null
	*/
	public $_ags_store = null; // Initialize the property here

	/**
	* The destination URL where the user will be redirected upon clicking the coupon.
	*
	* @var string|null
	*/
	public $_ags_destination_url = null; // Initialize the property here

	/**
	* Indicates whether the coupon has been used or redeemed.
	*
	* @var bool|null
	*/
	public $_ags_used = null; // Initialize the property here

	public $_ags_coupon_terms = null; // Initialize the property here

	/**
	* The success rate or percentage associated with the coupon.
	*
	* @var float|null
	*/
	public $_ags_percent_success; // Initialize the property here

	/**
	* The number of views or impressions the coupon has received.
	*
	* @var int|null
	*/
	public $_ags_views; // Initialize the property here

	/**
	* Information related to the current day or date.
	*
	* @var string|null
	*/
	public $_ags_today; // Initialize the property here

	/**
	* The number of upvotes or positive votes received for the coupon.
	*
	* @var int|null
	*/
	public $_ags_vote_up; // Initialize the property here

	/**
	* The number of downvotes or negative votes received for the coupon.
	*
	* @var int|null
	*/
	public $_ags_vote_down; // Initialize the property here

	/**
	* The amount saved or discount value associated with the coupon.
	*
	* @var float|null
	*/
	public $_ags_coupon_save; // Initialize the property here

	/**
	 * Home offer slider background
	 *
	 * @var string|null
	 */
	public $_ags_home_offer_slider_bg = null;

	/**
	 * Edit last timestamp
	 *
	 * @var string|null
	 */
	public $_edit_last = null;

	/**
	 * Background color
	 *
	 * @var string|null
	 */
	public $_ags_background_color = null;

	/**
	 * excludive data
	 *
	 * @var bool
	 */
	public $_ags_exclusive = null;

	/**
	 * WordPress old slug for post revisions
	 *
	 * @var string|null
	 */
	public $_wp_old_slug = null;



	/**
	 * Coupon WP post
	 *
	 * @var array|mixed|null|WP_Post
	 */
	public $post;

	/**
	 * @var halacoupon_Store
	 */
	public $store;
	private $_edit_lock;

	/**
	 * Check if still has more content after get_excerpt call
	 *
	 * @var $has_more_content
	 */
	public $has_more_content = false;

	/**
	 *  Construct function
	 *
	 * @param mixed  $p
	 * @param string $current_url
	 */
	function __construct( $p = null, $current_url = null ) {

		if ( ! is_object( $p ) ) {
			$p = get_post( $p );
		}

		$meta = array();

		if ( $p && $p->post_type == 'coupon' ) {
			foreach ( $p as $k => $v ) {
				$this->$k = $v;
			}

			$this->post = $p;

			if ( $p ) {
				$meta = get_post_custom( $p->ID );
			}
		}

		// default meta keys
		$default_meta = array(
			'_ags_store'                     => '',
			'_ags_coupon_type'               => '',
			'_ags_expires'                   => '',
			'_ags_home_offer_slider_bg'      => '',
			'_ags_exclusive'                 => '',
			'_ags_coupon_type_code'          => '',
			'_ags_coupon_save'               => '',
			'_ags_coupon_type_printable_id'  => '',
			'_ags_coupon_type_printable'     => '',
			'_ags_destination_url'           => '',
			'_ags_coupon_terms'              => '',
			'_edit_last'                     => '',
			'_ags_background_color'          => '',
			'_thumbnail_id'                  => 0,
			// meta keys for tracking
			'_ags_used'                      => 0,
			'_ags_percent_success'           => 100,
			'_ags_views'                     => 0,
			'_ags_today'                     => '',
			'_ags_vote_up'                   => 0,
			'_ags_vote_down'                 => 0,
		);

		$meta = wp_parse_args( $meta, apply_filters( 'st_default_coupon_metas', $default_meta ) );

		// Setup meta key as property - ONLY for legitimate meta keys to prevent deprecated warnings
		foreach ( $meta as $key => $values ) {
			// Only create properties for legitimate meta keys (those in default_meta)
			if ( array_key_exists( $key, $default_meta ) ) {
				if ( ! is_array( $values ) ) {
					$this->$key = $values;
				} else {
					$this->$key = maybe_unserialize( $values[0] );
				}
			}
		}

		if ( ! isset( $this->_ags_coupon_type ) ) {
			$this->_ags_coupon_type = 'code';
		}

		// Set first Store
		if ( is_array( $this->_ags_store ) ) {
			$this->_ags_store = intval( current( $this->_ags_store ) );
		} else {
			$this->_ags_store = intval( $this->_ags_store );
		}

		// Support Yoat SEO
		if ( defined( 'WPSEO_FILE' ) ) {
			$primary_store = get_post_meta( $this->ID, '_yoast_wpseo_primary_coupon_store', true );
			if ( $primary_store ) {
				$this->_ags_store = $primary_store;
			}
		}

		if ( ! $this->_ags_store ) {
			$term_list = wp_get_post_terms( $this->ID, 'coupon_store', array( 'fields' => 'ids' ) );
			if ( $term_list && ! is_wp_error( $term_list ) ) {
				$this->_ags_store = current( $term_list );
			}
		}

		/**
		 * Hook to change property
		 *
		 * @since 1.2.3
		 */
		do_action_ref_array( 'halacoupon_after_setup_coupon', array( &$this ) );

		$this->store = new halacoupon_Store( $this->_ags_store );
		$this->current_url = ( $current_url ) ? $current_url : $this->store->get_url();

	}

	/**
	 * Check if coupon exists
	 *
	 * @since 1.0.0
	 * @return bool
	 */
	public function is_coupon() {
		return ( isset( $this->ID ) );
	}



	/**
	 * Get excerpt of coupon
	 *
	 * @param int $num_words
	 * @return string
	 */
	public function get_excerpt( $num_words = null, $more = 'null', $has_thumb = true ) {

		if ( ! $has_thumb ) {
			if ( isset( $GLOBALS['coupon_num_words_no_thumb'] ) && intval( $GLOBALS['coupon_num_words_no_thumb'] ) > 0 ) {
				$num_words = $GLOBALS['coupon_num_words_no_thumb'];
			} else {
				if ( $num_words <= 0 ) {
					$num_words = 16;
				}
			}
		} else {
			if ( isset( $GLOBALS['coupon_num_words'] ) && intval( $GLOBALS['coupon_num_words'] ) > 0 ) {
				$num_words = $GLOBALS['coupon_num_words'];
			} else {
				if ( $num_words <= 0 ) {
					$num_words = 10;
				}
			}
		}

		$num_words = apply_filters( 'coupon_num_words_excerpt', '10', $has_thumb );

		if ( $num_words <= 0 ) {
			$num_words = 10;
		}

		if ( $this->post_excerpt != '' ) {
			$text = wp_trim_words( $this->post_excerpt, $num_words, '' );
		} else {
			$text = wp_trim_words( $this->post_content, $num_words, '' );
		}

		if ( trim( $text ) != trim( $this->post_content ) ) {
			$this->has_more_content = true;
			$text .= $more;
		} else {
			$this->has_more_content = false;
		}
		return $text;
	}

	/**
	 * Check coupon has expired
	 *
	 * @return bool
	 */
	function has_expired() {
		if ( ! $this->_ags_expires || $this->_ags_expires == '' ) {
			return false;
		}

		$time = time();

		return $time > $this->_ags_expires ? true : false;
	}

	/**
	 * If is exclusive coupon
	 *
	 * @return bool
	 */
	public function is_exclusive() {
		return strtolower( $this->_ags_exclusive ) == 'on' ? true : false;
	}



	/**
	 * If is verified coupon
	 *
	 * @return bool
	 */


	/**
	 * Get expires
	 * @return bool|string
	 */
	public function get_expires() {
		if ($this->_ags_expires) {
			if (!$this->has_expired()) {
				return sprintf( __( 'Expires %s', 'halacoupon' ), date_i18n(get_option('date_format'), $this->_ags_expires));
			} else {
				return esc_html__( 'Expired', 'halacoupon' );
			}
		}
		return esc_html__( 'No Expires', 'halacoupon' );
	}




	/**
	 * Get coupon type
	 *
	 * @since 1.0.0
	 * @return mixed
	 */
	public function get_type() {
		return $this->_ags_coupon_type;
	}

	/**
	 * Get coupon code
	 *
	 * @since 1.0.0
	 * @return mixed
	 */
	public function get_code( $number = 'full', $no_code = false ) {
		$code = false;
		if ( is_numeric( $number ) ) {
			$number = absint( $number );
			$code = substr( $this->_ags_coupon_type_code, - $number );
		} else {
			$code = $this->_ags_coupon_type_code;
		}

		if ( $no_code && ! $code ) {
			$code = apply_filters( 'halacoupon_no_code', '&nbsp;' );
		}

		return $code;
	}



	/**
	 * Get discount value (amount saved) for this coupon
	 *
	 * @return float|null
	 */
	public function get_discount_value() {
		return $this->_ags_coupon_save;
	}

	/**
	 * Get Print coupon image
	 *
	 * @since 1.0.0
	 * @param string $size
	 * @return mixed
	 */
	function get_print_image( $size = 'full' ) {
		if ( intval( $this->_ags_coupon_type_printable_id ) > 0 ) {
			$img = wp_get_attachment_image_src( $this->_ags_coupon_type_printable_id, $size );
			if ( $img ) {
				return $img[0];
			} else {
				return $this->_ags_coupon_type_printable;
			}
		} else {
			return $this->_ags_coupon_type_printable;
		}
	}

	/** coupon terms wysing */
	public function get_coupon_terms() {
		return $this->_ags_coupon_terms;
	}

	/**
	 * @return false|string
	 * @since 1.0.7
	 */
	function get_print_image_file() {
		if ( intval( $this->_ags_coupon_type_printable_id ) > 0 ) {
			return get_attached_file( $this->_ags_coupon_type_printable_id, $size );
		} else {
			return $this->_ags_coupon_type_printable;
		}
	}

	/**
	 * Get store id
	 *
	 * @since 1.0.0
	 * @return mixed
	 */
	public function get_store_id() {
		return $this->_ags_store;
	}

	/**
	 * Get store url
	 *
	 * @since 1.0.0
	 * @return bool|string
	 */
	public function get_store_url() {
		if ( $this->store->is_store() ) {
			return $this->store->get_url();
		}
		return '';
	}

	/**
	 *
	 * @return null|string
	 */
	function get_share_url() {
		return $this->get_href();
	}

	public function get_href() {
		global $wp_rewrite;
		$enable_single = false; 
		$coupon_id = $this->ID;
		$link = '';
	
		if (!$enable_single) {
			if (!$wp_rewrite->using_permalinks()) {
				// Non-permalink structure
				$link = add_query_arg(array('coupon_id' => $coupon_id), $this->get_store_url());
			} else {
				// Permalink structure without the coupon ID in the middle
				$link = trailingslashit($this->get_store_url());
			}
	
			// Add # before coupon ID for frontend
			$link .= '#' .  $coupon_id;
	
		} else {
			if ($wp_rewrite->using_permalinks()) {
				$link = trailingslashit(trailingslashit($this->get_store_url()) . $this->post->post_name);
			} else {
				$link = get_permalink($this->post);
			}
		}
	
		return $link;

	}

	/**
	 * Get store website url
	 *
	 * @since 1.0.0
	 * @return string
	 */
	public function get_store_site_url() {
		return (string) $this->store->get_website_url();
	}

	/**
	 * Get coupon type add display text
	 *
	 * @since 1.0.0
	 * @return string|void
	 */
	public function get_coupon_type_text() {
		$type = $this->get_type();
		$supported_types = halacoupon_get_coupon_types();

		if ( isset( $supported_types[ $type ] ) ) {
			return $supported_types[ $type ];
		} else {
			return esc_html__( 'Code', 'halacoupon' );
		}
	}

	/**
	 * Get destination url
	 *
	 * @since 1.0.0
	 * @param bool $store_url_if_empty
	 * @return bool|string
	 */
	public function get_destination_url( $store_url_if_empty = true ) {

		$url = '';
		if ( ! $this->_ags_destination_url && $store_url_if_empty ) {
			$url = $this->get_store_site_url();
		} else {
			$url = $this->_ags_destination_url;
		}

		if ( ! $url ) {
			$url = $this->store->get_url();
		}

		return $url;
	}

	/**
	 * Display out url
	 *
	 * example: http://site.com/out/{coupon_id}
	 *
	 * @return string
	 */
	public function get_go_out_url() {
		$home = trailingslashit( home_url() );
		// If permalink enable
		if ( get_option( 'permalink_structure' ) != '' ) {
			$out_slug = halacoupon_get_option( 'go_out_slug', 'out' );
			return $home . $out_slug . '/' . $this->ID;
		} else {
			return $home . '?out=' . $this->ID;
		}
	}


	/**
	 * Get Go Out store url
	 *
	 * @example: http://site.com/go-store/{coupon_id}
	 *
	 * @see: Class halacoupon_Store
	 *
	 * @return string
	 */
	function get_go_store_url() {

		$home = trailingslashit( home_url() );
		// If permalink enable
		if ( get_option( 'permalink_structure' ) != '' ) {
			$go_store_slug = halacoupon_get_option( 'go_out_slug', 'go-store' );
			return $home . $go_store_slug . '/' . $this->get_store_id();
		} else {
			return $home . '?go_store_id=' . $this->get_store_id();
		}
	}

	/**
	 * check if coupon has thumbnail
	 *
	 * @since 1.0.0
	 * @return bool
	 */
	public function has_thumb() {
		if ( $this->_thumb_id > 0 ) {
			return true;
		}
		if ( has_post_thumbnail( $this->ID ) ) {
			$this->_thumb_id = get_post_thumbnail_id( $this->ID );
		} elseif ( has_post_thumbnail( $this->get_store_id() ) ) {
			$this->_thumb_id = get_post_thumbnail_id( $this->get_store_id() );
		} else {
			$this->_thumb_id = $this->store->has_thumbnail();
		}

		return $this->_thumb_id ? true : false;
	}

	/**
	 * Get Thumbnail HTML
	 *
	 * @since 1.0.0
	 * @param string $size
	 * @param bool   $placeholder
	 * @return bool|string
	 */
	public function get_thumb( $size = 'halacoupon_medium-thumb', $placeholder = false, $url_only = false ) {
		$image_alt = get_post_meta( $this->_thumb_id, '_wp_attachment_image_alt', true );
		// check if have thumbnail
		if ( $this->has_thumb( $size ) && is_numeric( $this->_thumb_id ) ) {
			if ( $url_only ) {
				$image = wp_get_attachment_image_src( $this->_thumb_id, $size );

				if ( $image ) {
					return $image[0];
				} else {
					return false;
				}
			} else {
				$image_title = get_the_title( $this->_thumb_id );
				return wp_get_attachment_image(
					$this->_thumb_id,
					$size,
					false,
					array(
						'alt' => $image_alt,
						'title' => $image_title,
						'class' => 'ui Tiny circular image',
						'loading' => 'lazy',
						'width' => '80px',
						'height' => '80px',
					)
				);
			}
		} elseif ( $this->has_thumb() && $this->_thumb_id != '' ) {
			if ( $url_only ) {
				return $this->_thumb_id;
			} else {
				return '<img src="' . esc_attr( $this->_thumb_id ) . '" alt="' . $image_alt . '">';
			}
		} else {
			return $this->store->get_thumbnail( $size, $url_only );
		}
	}

	/**
	 * Get store Thumbnail
	 *
	 * @param string $size
	 * @return bool|mixed|void
	 */
	public function get_store_thumb( $size = 'full' ) {
		return $this->store->get_thumbnail( $size );
	}	


	/**
	 *  Get number used of coupon
	 *
	 * @since 1.0.0
	 * @changed 1.1.3
	 * @return int
	 */
	public function get_total_used() {
		return apply_filters( 'halacoupon_coupon_total_used', intval( $this->_ags_used ), $this->ID, $this );
	}

	/**
	 * Get used today
	 *
	 * @return int
	 * @changed 1.1.3
	 */
	public function get_used_today() {
		$data = wp_parse_args(
			$this->_ags_today,
			array(
				'date' => current_time( 'Y-m-d' ),
				'used' => 0,
			)
		);

		if ( $data['date'] == current_time( 'Y-m-d' ) ) {
			$n = $data['used'];
		} else {
			$n = 0;
		}

		return apply_filters( 'halacoupon_coupon_used_today', $n, $this->ID, $this );
	}

	/**
	 * Get percent success
	 *
	 * @since 1.0.0
	 * @return float|int
	 */
	public function percent_success() {
		$vote_up = intval( $this->_ags_vote_up );
		$vote_down = intval( $this->_ags_vote_down );

		$total = $vote_up + $vote_down;
		// Request minimum 3 vote to calc
		if ( $total <= apply_filters( 'st_minimum_votes', 3 ) ) {
			return 100;
		}
		$this->percent_success = 0;
		if ( $total <= 0 ) {
			$this->percent_success = 100;
		} else {
			$this->percent_success = ( $vote_up / $total ) * 100;
		}

		return $this->percent_success;
	}

	/**
	 *
	 * @param string $type  ids|array  ids return ids only, array: return array key is term_id  value is cat nams
	 * @return array
	 */
	function get_categories( $type = 'array' ) {
		if ( ! $this->is_coupon() ) {
			return false;
		}
		$terms = get_the_terms( $this->ID, 'coupon_category' );
		$cats = false;
		if ( $terms && ! is_wp_error( $terms ) ) {
			$cats = array();
			foreach ( $terms as $term ) {
				if ( $type == 'ids' ) {
					$cats[ $term->term_id ] = $term->term_id;
				} else {
					$cats[ $term->term_id ] = $term->name;
				}
			}
		}
		return $cats;
	}



}


/**
 * Short coupon by property
 *
 * @since 1.0.0
 *
 * @param $coupons array of object coupon
 * @param string                         $orderby property of halacoupon_Coupon
 * @param string                         $sort
 * @return mixed
 */
function halacoupon_sort_coupons( $coupons, $orderby = '_ags_expires', $sort = 'desc' ) {
	$sortArray = array();
	foreach ( $coupons as $coupon ) {
		if ( ! isset( $sortArray[ $orderby ] ) ) {
			$sortArray[ $orderby ] = array();
		}
		if ( isset( $coupon->$orderby ) ) {
			$sortArray[ $orderby ][] = $coupon->$orderby;
		} else {
			$sortArray[ $orderby ][] = 0;
		}
	}
	if ( strtolower( $sort ) == 'desc' ) {
		array_multisort( $sortArray[ $orderby ], SORT_DESC, $coupons );
	} else {
		array_multisort( $sortArray[ $orderby ], SORT_ASC, $coupons );
	}
	return $coupons;
}




/**
 * Get coupon type
 *
 * @param null $coupon_id
 * @return mixed
 */
function halacoupon_get_coupon_type( $coupon_id = null ) {
	if ( ! $coupon_id ) {
		global  $post;
		$coupon_id = $post->ID;
	}

	$type = get_post_meta( $coupon_id, '_ags_coupon_type', true );
	return $type;
}

/**
 * Get coupon type as text
 *
 * @param $coupon_id
 * @return string|void
 */
function halacoupon_get_coupon_type_text( $coupon_id = null ) {
	$type = halacoupon_get_coupon_type( $coupon_id );
	$supported_types = halacoupon_get_coupon_types();
	if ( ! isset( $supported_types[ $type ] ) ) {
		return $supported_types[ $type ];
	} else {
		return esc_html__( 'Code', 'halacoupon' );
	}
}

/**
 * Get coupon code
 *
 * @param null $coupon_id
 * @return string
 */
function halacoupon_get_coupon_code( $coupon_id = null ) {
	if ( ! $coupon_id ) {
		global  $post;
		$coupon_id = $post->ID;
	}

	return (string) get_post_meta( $coupon_id, '_ags_coupon_type_code', true );
}

/**
 * Get coupon store ID
 *
 * @param null $coupon_id
 * @return int
 */
function halacoupon_get_coupon_store_id( $coupon_id = null ) {
	if ( ! $coupon_id ) {
		global  $post;
		$coupon_id = $post->ID;
	}
	return (int) get_post_meta( $coupon_id, '_ags_store', true );
}

/**
 * Get store website url
 *
 * @param null $coupon_id
 * @return mixed
 */
function halacoupon_get_coupon_store_site_url( $coupon_id = null ) {
	if ( ! $coupon_id ) {
		global  $post;
		$coupon_id = $post->ID;
	}

	return get_post_meta( $coupon_id, '_ags_store_url', true );
}


/**
 * A call back listing comments
 *
 * @see wp_list_comments()
 *
 * @param $comment
 * @param $args
 * @param $depth
 */
function halacoupon_coupon_comment( $comment, $args, $depth ) {
	$GLOBALS['comment'] = $comment;
	global $post;
	switch ( $comment->comment_type ) :
		case 'pingback':
		case 'trackback':
			// Display trackbacks differently than normal comments.
			?>
			<div <?php comment_class(); ?> id="comment-<?php comment_ID(); ?>">
			<p><?php esc_html_e( 'Pingback:', 'halacoupon' ); ?> <?php comment_author_link(); ?> <?php edit_comment_link( esc_html__( '(Edit)', 'halacoupon' ), '<span class="edit-link">', '</span>' ); ?></p>
			<?php
			break;
		default:
			// Proceed with normal comments.
			?>
		<div <?php comment_class(); ?> data-c-id="<?php comment_ID(); ?>">

			<a class="avatar">
				<?php echo get_avatar( $comment, 60 ); ?>
			</a>

			<div class="content">
				<a class="author" href="<?php echo get_comment_author_url(); ?>"><?php echo get_comment_author(); ?></a>
				<div class="metadata">
					<span class="date"><?php printf( esc_html__( ' %s ago', 'halacoupon' ), human_time_diff( get_comment_time( 'U' ), current_time( 'timestamp' ) ) ); ?></span>
				</div>
				<div class="text">
					<?php if ( '0' == $comment->comment_approved ) : ?>
						<p class="comment-awaiting-moderation"><?php esc_html_e( 'Your comment is awaiting moderation.', 'halacoupon' ); ?></p>
					<?php endif; ?>
					<?php comment_text(); ?>
				</div>
			</div>

			<?php
			break;
	endswitch; // end comment_type check
}

/**
 * A call  en back listing comments
 *
 * @see wp_list_comments()
 *
 * @param $comment
 * @param $args
 * @param $depth
 */
function halacoupon_coupon_comment_end() {
	echo '</div>';
}

/**
 * Custom walker coupon class
 *
 * Class halacoupon_Walker_Coupon_Comment
 */
class halacoupon_Walker_Coupon_Comment extends Walker_Comment {

	/**
	 * Start the list before the elements are added.
	 *
	 * @see Walker::start_lvl()
	 *
	 * @since 2.7.0
	 *
	 * @param string $output Passed by reference. Used to append additional content.
	 * @param int    $depth Depth of comment.
	 * @param array  $args Uses 'style' argument for type of HTML list.
	 */
	public function start_lvl( &$output, $depth = 0, $args = array() ) {
		$GLOBALS['comment_depth'] = $depth + 1;

		switch ( $args['style'] ) {
			case 'div':
				$output .= '<div class="children comments">' . "\n";
				break;
			case 'ol':
				$output .= '<ol class="children">' . "\n";
				break;
			case 'ul':
			default:
				$output .= '<ul class="children">' . "\n";
				break;
		}
	}

	/**
	 * End the list of items after the elements are added.
	 *
	 * @see Walker::end_lvl()
	 *
	 * @since 2.7.0
	 *
	 * @param string $output Passed by reference. Used to append additional content.
	 * @param int    $depth  Depth of comment.
	 * @param array  $args   Will only append content if style argument value is 'ol' or 'ul'.
	 */
	public function end_lvl( &$output, $depth = 0, $args = array() ) {
		$GLOBALS['comment_depth'] = $depth + 1;

		switch ( $args['style'] ) {
			case 'div':
				$output .= "</div><!-- .children -->\n";
				break;
			case 'ol':
				$output .= "</ol><!-- .children -->\n";
				break;
			case 'ul':
			default:
				$output .= "</ul><!-- .children -->\n";
				break;
		}
	}

}


/**
 * New comment for coupon
 *
 * Handle submit comment form ajax
 *
 * @since 1.0.0
 *
 * @return bool|int
 */
function halacoupon_new_coupon_comments() {
	if ( ! isset( $_POST['c_comment'] ) ) {
		return 0;
	}
	$data = $_POST['c_comment'];
	// if is user logged in then use that user data
	if ( is_user_logged_in() ) {
		$data['user_id'] = get_current_user_id();
		$user = wp_get_current_user();
		$data['comment_author'] = $user->display_name;
		$data['comment_author_email'] = $user->user_email;
	}
	$data['comment_author_url'] = '';
	return wp_new_comment( $data );

}

/**
 * Class halacoupon_Coupon_Tracking
 *
 * /*
 * Coupon meta keys for tracking:
 * '_ags_used'                      => 0,
 * '_ags_views'                     => 0,
 * '_ags_today'                     => '' ,
 * '_ags_vote_up'                   => 0,
 * '_ags_vote_down'                 => 0,
 */
class halacoupon_Coupon_Tracking {

	/**
	 * Tracking coupon used
	 *
	 * @since 1.0.0
	 * @param $coupon_id ID of coupon
	 * @return bool
	 */
	public static function update_used( $coupon_id ) {

		$coupon = new halacoupon_Coupon( $coupon_id );
		if ( ! $coupon->is_coupon() ) {
			return false;
		}

		/**
		 * Update total used
		 */
		$used = $coupon->get_total_used() + 1;
		update_post_meta( $coupon->ID, '_ags_used', $used );

		/**
		 * Update used to day
		 */
		$to_day = current_time( 'Y-m-d' );
		$used_today = $coupon->get_used_today();
		update_post_meta(
			$coupon->ID,
			'_ags_today',
			array(
				'date' => $to_day,
				'used' => ( $used_today + 1 ),
			)
		);

	}

	/**
	 * Vote coupon
	 *
	 * @since 1.0.0
	 * @param $coupon_id ID of coupon
	 * @param int                    $vote -1 for vote down else for vote up
	 * @return bool
	 */
	public static function vote( $coupon_id, $vote = 1 ) {

		$coupon = new halacoupon_Coupon( $coupon_id );
		if ( ! $coupon->is_coupon() ) {
			return false;
		}

		if ( $vote != -1 ) {
			$vote = 1;
		}

		// Get current vote counts
		$current_vote_up = intval( get_post_meta( $coupon->ID, '_ags_vote_up', true ) );
		$current_vote_down = intval( get_post_meta( $coupon->ID, '_ags_vote_down', true ) );

		if ( $vote == 1 ) {
			$new_vote_up = $current_vote_up + 1;
			update_post_meta( $coupon->ID, '_ags_vote_up', $new_vote_up );
		} else {
			$new_vote_down = $current_vote_down + 1;
			update_post_meta( $coupon->ID, '_ags_vote_down', $new_vote_down );
		}

		// Calculate new totals
		$vote_up = $vote == 1 ? $new_vote_up : $current_vote_up;
		$vote_down = $vote == -1 ? $new_vote_down : $current_vote_down;
		$total = $vote_up + $vote_down;

		// Calculate success percent
		if ( $total <= apply_filters( 'st_minimum_votes', 3 ) ) {
			$percent_success = 100;
		} else {
			if ( $total <= 0 ) {
				$percent_success = 100;
			} else {
				$percent_success = ( $vote_up / $total ) * 100;
			}
		}

		update_post_meta( $coupon->ID, '_ags_percent_success', $percent_success );

		return true;
	}


}




function halacoupon_ajax_store_coupons() {
	$instance = isset( $_REQUEST['args'] ) ? $_REQUEST['args'] : false;
	$default_base_pagenum_url = trailingslashit( get_bloginfo( 'url' ) );
	$instance = wp_parse_args(
		$instance,
		array(
			'store_id' => '',
			'type' => '',
			'number' => 15,
			'coupon_type' => 'all',
			'base_pagenum_url' => $default_base_pagenum_url,
		)
	);

	$paged = isset( $_REQUEST['next_page'] ) ? intval( $_REQUEST['next_page'] ) : 1;
	global $post, $wp_query;

	$content = '';
	$next_page = 0;
	$max_pages = 0;

	$store = get_term( $instance['store_id'], 'coupon_store' );
	if ( $store && ! is_wp_error( $store ) ) {
		ob_start();
		$coupons = halacoupon_get_store_coupons( $store->term_id, absint( $instance['number'] ), $paged, $instance['type'], $instance['coupon_type'] );
		$max_pages = $wp_query->max_num_pages;
		if ( $max_pages >= ( $paged + 1 ) ) {
			$next_page = ( $paged + 1 );
		} else {
			$next_page = 0;
		}

		$current_link = get_term_link( $store, 'coupon_store' );
		foreach ( $coupons as $post ) {
			halacoupon_setup_coupon( $post, $current_link );
			get_template_part( 'loop/loop-coupon' );
		}
		$content = ob_get_clean();
	}

	$link = get_term_link( $store );

	return array(
		'content'   => $content,
		'next_page' => $next_page,
		'pagenum_url' => halacoupon_get_store_page_num( $paged, $instance['base_pagenum_url'] ),
		'next_pagenum_url' => halacoupon_get_store_page_num( $next_page, $instance['base_pagenum_url'] ),
		'max_pages' => $max_pages,
	);
}


/**
 * Get popular coupons
 *
 * @param int $posts_per_page
 * @param int $paged
 * @return array
 */
function halacoupon_get_popular_coupons( $posts_per_page = 10, $paged = 1 ) {
	// _ags_percent_success
	// _ags_expires
	// _ags_vote_up
	// _ags_used
	global $wp_query;
	$current = current_time( 'timestamp' );
	$args = array(
		'post_type'      => 'coupon',
		'posts_per_page' => $posts_per_page,
		'meta_key'       => '_ags_used',
		'paged'          => $paged,
		'meta_query'     => array(
			'relation' => 'AND',
			array(
				'relation' => 'OR',
				array(
					'key'     => '_ags_expires',
					'value'   => '',
					'compare' => '=',
				),
				array(
					'key'     => '_ags_expires',
					'value'   => $current,
					'compare' => '>=',
				),
			),

		),
		'orderby'         => 'menu_order meta_value_num',
		'order'           => 'desc',
	);

	$wp_query = new WP_Query( $args );
	return $wp_query->get_posts();

}

/**
 * Search SQL filter for matching against post title only.
 *
 * @link    http://wordpress.stackexchange.com/a/11826/1685
 *
 * @param   string   $search
 * @param   WP_Query $wp_query
 */
function halacoupon_coupon_search_by_title( $search, $wp_query ) {
	if ( is_admin() ) {
		return $search;
	}
	if ( ! empty( $search ) && ! empty( $wp_query->query_vars['search_terms'] ) ) {
		global $wpdb;

		$q = $wp_query->query_vars;
		$n = ! empty( $q['exact'] ) ? '' : '%';

		$search = array();

		foreach ( (array) $q['search_terms'] as $term ) {
			$search[] = $wpdb->prepare( "$wpdb->posts.post_title LIKE %s", $n . $wpdb->esc_like( $term ) . $n );
		}

		if ( ! is_user_logged_in() ) {
			$search[] = "$wpdb->posts.post_password = ''";
		}

		$search = ' AND ' . implode( ' AND ', $search );
	}
	return $search;
}

/**
 * Change page title when single coupon not enabled.
 *
 * @param $title_array
 * @return mixed
 */
function halacoupon_sing_coupon_modal_title_parts( $title_array ) {
	if ( ! halacoupon_is_single_enable() ) {
		if ( is_tax( 'coupon_store' ) ) {
			$coupon_id = get_query_var( 'coupon_id' );
			if ( $coupon_id ) {
				$post = get_post( $coupon_id );
				if ( $post ) {
					$title_array['title'] = $post->post_title;
				}
			}
		}
	}
	return $title_array;
}

add_filter( 'document_title_parts', 'halacoupon_sing_coupon_modal_title_parts' );

