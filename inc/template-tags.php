<?php

/**
 * Calculate reading time for posts
 *
 * @param int $post_id Post ID (optional)
 * @return int Reading time in minutes
 */
if (!function_exists('halacoupon_get_reading_time')) {
    function halacoupon_get_reading_time($post_id = null) {
        if (!$post_id) {
            $post_id = get_the_ID();
        }

        $content = get_post_field('post_content', $post_id);
        $word_count = str_word_count(strip_tags($content));
        $reading_time = ceil($word_count / 200); // Average reading speed: 200 words per minute

        return max(1, $reading_time); // Minimum 1 minute
    }
}

/**
 * Enhanced wp_link_pages function with modern styling
 *
 * @param array $args Arguments for wp_link_pages
 * @return void
 */
if (!function_exists('halacoupon_wp_link_pages')) {
    function halacoupon_wp_link_pages($args = array()) {
        $defaults = array(
            'before'           => '<nav class="post-page-links mt-8 mb-6" aria-label="' . esc_attr__('Post pages', 'halacoupon') . '"><div class="flex flex-wrap items-center gap-2 justify-center"><span class="text-sm font-medium text-text/70 mr-4">' . esc_html__('Pages:', 'halacoupon') . '</span>',
            'after'            => '</div></nav>',
            'link_before'      => '<span class="inline-flex items-center justify-center w-10 h-10 bg-white border border-gray-200 rounded-lg text-sm font-medium text-text hover:bg-primary hover:text-white hover:border-primary transition-all duration-200">',
            'link_after'       => '</span>',
            'next_or_number'   => 'number',
            'separator'        => '',
            'pagelink'         => '%',
            'echo'             => 1
        );

        $parsed_args = wp_parse_args($args, $defaults);

        // Get the content
        $content = get_the_content();

        // Check if content has page breaks
        if (strpos($content, '<!--nextpage-->') !== false) {
            wp_link_pages($parsed_args);
        }
    }
}

/**
 * Modern comment callback function
 * Enhanced with glass morphism design and modern styling
 *
 * @param object $comment Comment object
 * @param array $args Comment arguments
 * @param int $depth Comment depth
 */
if (!function_exists('halacoupon_comment')) {
    function halacoupon_comment($comment, $args, $depth) {
        $GLOBALS['comment'] = $comment;

        switch ($comment->comment_type) :
            case 'pingback':
            case 'trackback':
                // Display trackbacks differently than normal comments
                ?>
                <div <?php comment_class('glass-card rounded-xl border border-white/30 p-4 mb-4'); ?> id="comment-<?php comment_ID(); ?>">
                    <p class="text-sm text-text/70">
                        <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5z" clip-rule="evenodd"></path>
                            <path fill-rule="evenodd" d="M7.414 15.414a2 2 0 01-2.828-2.828l3-3a2 2 0 012.828 0 1 1 0 001.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 00-1.414-1.414l-1.5 1.5z" clip-rule="evenodd"></path>
                        </svg>
                        <?php esc_html_e('Pingback:', 'halacoupon'); ?>
                        <?php comment_author_link(); ?>
                        <?php edit_comment_link(esc_html__('(Edit)', 'halacoupon'), '<span class="edit-link ml-2 text-primary hover:text-primary-600">', '</span>'); ?>
                    </p>
                </div>
                <?php
                break;

            default:
                // Proceed with normal comments
                ?>
                <div <?php comment_class('glass-card rounded-xl border border-white/30 p-6 mb-6 hover:border-primary/40 transition-all duration-300'); ?> id="comment-<?php comment_ID(); ?>" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">

                    <div class="flex gap-4">
                        <!-- Avatar -->
                        <div class="flex-shrink-0">
                            <?php echo get_avatar($comment, 48, '', '', [
                                'class' => 'w-12 h-12 rounded-full border-2 border-white shadow-sm'
                            ]); ?>
                        </div>

                        <!-- Comment Content -->
                        <div class="flex-1 min-w-0">

                            <!-- Comment Header -->
                            <div class="flex items-center gap-3 mb-3">
                                <h4 class="font-semibold text-text">
                                    <?php
                                    $author_url = get_comment_author_url();
                                    if ($author_url) {
                                        echo '<a href="' . esc_url($author_url) . '" class="hover:text-primary transition-colors duration-200">' . get_comment_author() . '</a>';
                                    } else {
                                        echo get_comment_author();
                                    }
                                    ?>
                                </h4>

                                <span class="text-text/50">•</span>

                                <time class="text-sm text-text/70" datetime="<?php echo esc_attr(get_comment_date('c')); ?>">
                                    <?php printf(esc_html__('%s ago', 'halacoupon'), human_time_diff(get_comment_time('U'), current_time('timestamp'))); ?>
                                </time>

                                <?php edit_comment_link(esc_html__('Edit', 'halacoupon'), '<span class="text-text/50">•</span> <span class="text-sm text-primary hover:text-primary-600 transition-colors duration-200">', '</span>'); ?>
                            </div>

                            <!-- Comment Text -->
                            <div class="comment-content prose prose-sm max-w-none text-text/80">
                                <?php if ('0' == $comment->comment_approved) : ?>
                                    <p class="bg-yellow-50 border border-yellow-200 text-yellow-800 px-3 py-2 rounded-lg text-sm mb-3">
                                        <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?php esc_html_e('Your comment is awaiting moderation.', 'halacoupon'); ?>
                                    </p>
                                <?php endif; ?>

                                <?php comment_text(); ?>
                            </div>

                            <!-- Comment Actions -->
                            <div class="flex items-center gap-4 mt-4 pt-3 border-t border-gray-100">
                                <?php
                                comment_reply_link(array_merge($args, array(
                                    'add_below' => 'comment',
                                    'depth'     => $depth,
                                    'max_depth' => $args['max_depth'],
                                    'before'    => '<span class="inline-flex items-center gap-1 text-sm text-primary hover:text-primary-600 transition-colors duration-200 cursor-pointer">',
                                    'after'     => '</span>'
                                )));
                                ?>

                                <!-- Like/Helpful button (if needed) -->
                                <button class="inline-flex items-center gap-1 text-sm text-text/60 hover:text-primary transition-colors duration-200">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                    </svg>
                                    <?php esc_html_e('Helpful', 'halacoupon'); ?>
                                </button>
                            </div>

                        </div>
                    </div>

                </div>
                <?php
                break;
        endswitch; // end comment_type check
    }
}

/**
 * Comment end callback function
 *
 * @param object $comment Comment object
 * @param array $args Comment arguments
 * @param int $depth Comment depth
 */
if (!function_exists('halacoupon_comment_end')) {
    function halacoupon_comment_end($comment, $args, $depth) {
        echo '</div><!-- .comment-children -->';
    }
}
/**
 * Template Tags - Legacy Loader
 *
 * This file now loads the modular app system instead of containing all functions.
 * All template tag functions have been organized into the inc/app/ directory.
 *
 * @package HalaCoupon
 * @version 2.0.0 - Modular System
 * <AUTHOR> G
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load the new modular app system
require_once get_template_directory() . '/inc/app/loader.php';

/**
 * Legacy compatibility notice
 *
 * All functions have been moved to the modular app system in inc/app/
 * This ensures better organization, maintainability, and performance.
 *
 * Module Structure:
 * - inc/app/pagination/ - Pagination functions
 * - inc/app/sliders/ - Slider and carousel functions
 * - inc/app/navigation/ - Breadcrumbs and navigation
 * - inc/app/stores/ - Store-related functions
 * - inc/app/coupons/ - Coupon-related functions
 * - inc/app/ajax/ - AJAX handlers
 * - inc/app/social/ - Social profiles
 * - inc/app/localization/ - Country/language functions
 * - inc/app/forms/ - Contact forms and FAQs
 * - inc/app/utilities/ - Helper functions
 */

/**
 * End of modular template tags system
 *
 * All functions are now loaded from inc/app/ modules
 * This provides better organization and maintainability
 */