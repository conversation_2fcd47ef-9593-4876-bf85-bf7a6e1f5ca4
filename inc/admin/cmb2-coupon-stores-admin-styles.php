<?php
/**
 * CMB2 Coupon Stores Admin Styles Enqueue
 * Enhanced styling for coupon stores taxonomy admin interface
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue CMB2 coupon stores admin styles
 * Only loads on coupon_store taxonomy pages
 */
add_action('admin_enqueue_scripts', 'halacoupon_enqueue_cmb2_coupon_stores_admin_styles');
function halacoupon_enqueue_cmb2_coupon_stores_admin_styles($hook_suffix) {
    // Get current screen
    $screen = get_current_screen();
    
    // Only load on coupon_store taxonomy pages
    if (!$screen || $screen->taxonomy !== 'coupon_store') {
        return;
    }
    
    // Enqueue main admin styles
    wp_enqueue_style(
        'halacoupon-cmb2-coupon-stores-admin',
        get_template_directory_uri() . '/assets/css/admin/cmb2-coupon-stores-admin.css',
        array(),
        filemtime(get_template_directory() . '/assets/css/admin/cmb2-coupon-stores-admin.css')
    );
    
    // Enqueue RTL styles if needed
    if (is_rtl()) {
        wp_enqueue_style(
            'halacoupon-cmb2-coupon-stores-admin-rtl',
            get_template_directory_uri() . '/assets/css/admin/cmb2-coupon-stores-admin-rtl.css',
            array('halacoupon-cmb2-coupon-stores-admin'),
            filemtime(get_template_directory() . '/assets/css/admin/cmb2-coupon-stores-admin-rtl.css')
        );
    }
    
    // Add custom CSS variables for dynamic theming
    $custom_css = "
        :root {
            --halacoupon-primary: #667eea;
            --halacoupon-secondary: #764ba2;
            --halacoupon-accent-primary: #EBBF43;
            --halacoupon-accent-secondary: #FFBC02;
            --halacoupon-success: #10b981;
            --halacoupon-error: #ef4444;
            --halacoupon-warning: #f59e0b;
            --halacoupon-info: #3b82f6;
        }
    ";
    wp_add_inline_style('halacoupon-cmb2-coupon-stores-admin', $custom_css);
}

/**
 * Add admin body classes for better targeting
 */
add_filter('admin_body_class', 'halacoupon_add_cmb2_admin_body_classes');
function halacoupon_add_cmb2_admin_body_classes($classes) {
    $screen = get_current_screen();
    
    // Add class for coupon_store taxonomy pages
    if ($screen && $screen->taxonomy === 'coupon_store') {
        $classes .= ' halacoupon-cmb2-enhanced ';
        
        // Add RTL class if needed
        if (is_rtl()) {
            $classes .= ' halacoupon-rtl ';
        }
        
        // Add specific page classes
        if ($screen->base === 'edit-tags') {
            $classes .= ' halacoupon-store-list ';
        } elseif ($screen->base === 'term') {
            $classes .= ' halacoupon-store-edit ';
        }
    }
    
    return $classes;
}


/**
 * Enhance CMB2 field output with additional classes
 */
add_filter('cmb2_render_class_name', 'halacoupon_cmb2_add_enhanced_classes', 10, 2);
function halacoupon_cmb2_add_enhanced_classes($class_name, $field) {
    // Only apply to coupon_store taxonomy
    if (!is_admin() || !function_exists('get_current_screen')) {
        return $class_name;
    }
    
    $screen = get_current_screen();
    if (!$screen || $screen->taxonomy !== 'coupon_store') {
        return $class_name;
    }
    
    // Add enhanced classes based on field type
    $field_type = $field->type();
    $enhanced_classes = array(
        'text' => 'halacoupon-enhanced-text',
        'text_medium' => 'halacoupon-enhanced-text-medium',
        'text_url' => 'halacoupon-enhanced-url',
        'textarea' => 'halacoupon-enhanced-textarea',
        'wysiwyg' => 'halacoupon-enhanced-wysiwyg',
        'select' => 'halacoupon-enhanced-select',
        'checkbox' => 'halacoupon-enhanced-checkbox',
        'file' => 'halacoupon-enhanced-file',
        'group' => 'halacoupon-enhanced-group'
    );
    
    if (isset($enhanced_classes[$field_type])) {
        $class_name .= ' ' . $enhanced_classes[$field_type];
    }
    
    return $class_name;
}

/**
 * Add JavaScript enhancements for CMB2 fields
 */
add_action('admin_footer', 'halacoupon_cmb2_admin_javascript');
function halacoupon_cmb2_admin_javascript() {
    $screen = get_current_screen();
    
    // Only load on coupon_store taxonomy pages
    if (!$screen || $screen->taxonomy !== 'coupon_store') {
        return;
    }
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        // Enhanced form validation
        $('.taxonomy-coupon_store .cmb2-text-input, .taxonomy-coupon_store .cmb2-text-medium, .taxonomy-coupon_store .cmb2-text-url').on('blur', function() {
            var $field = $(this);
            var value = $field.val().trim();
            
            // Remove previous validation classes
            $field.removeClass('cmb2-field-error cmb2-field-success');
            
            // Basic validation
            if ($field.hasClass('cmb2-text-url') && value) {
                var urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
                if (urlPattern.test(value)) {
                    $field.addClass('cmb2-field-success');
                } else {
                    $field.addClass('cmb2-field-error');
                }
            } else if (value && value.length > 2) {
                $field.addClass('cmb2-field-success');
            }
        });
        
        // Enhanced group field animations
        $('.taxonomy-coupon_store .cmb-group-title').on('click', function() {
            var $group = $(this).closest('.cmb-group');
            var $content = $group.find('.cmb-row');
            
            $content.slideToggle(300);
            $(this).toggleClass('collapsed');
        });
        
        // Auto-save indication
        var saveTimeout;
        $('.taxonomy-coupon_store .cmb2-text-input, .taxonomy-coupon_store .cmb2-textarea').on('input', function() {
            clearTimeout(saveTimeout);
            var $field = $(this);
            
            saveTimeout = setTimeout(function() {
                $field.addClass('auto-saved');
                setTimeout(function() {
                    $field.removeClass('auto-saved');
                }, 2000);
            }, 1000);
        });
        
        // Accessibility enhancements
        $('.taxonomy-coupon_store .cmb2-upload-button').attr('aria-label', '<?php echo esc_js(__('Upload file', 'halacoupon')); ?>');
        $('.taxonomy-coupon_store .cmb-remove-group-row').attr('aria-label', '<?php echo esc_js(__('Remove this item', 'halacoupon')); ?>');
        $('.taxonomy-coupon_store .cmb-add-group-row').attr('aria-label', '<?php echo esc_js(__('Add new item', 'halacoupon')); ?>');
        
        // RTL-specific enhancements
        <?php if (is_rtl()): ?>
        $('body').addClass('halacoupon-rtl-enhanced');
        
        // Adjust Select2 for RTL
        if ($.fn.select2) {
            $('.taxonomy-coupon_store .select2').select2({
                dir: 'rtl',
                language: {
                    noResults: function() {
                        return '<?php echo esc_js(__('No results found', 'halacoupon')); ?>';
                    },
                    searching: function() {
                        return '<?php echo esc_js(__('Searching...', 'halacoupon')); ?>';
                    }
                }
            });
        }
        <?php endif; ?>
    });
    </script>
    
    <style>
    /* Dynamic CSS for enhanced states */
    .taxonomy-coupon_store .auto-saved {
        border-color: #10b981 !important;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
    }
    
    .taxonomy-coupon_store .cmb-group-title.collapsed + .cmb-row {
        display: none;
    }
    
    /* Loading states */
    .taxonomy-coupon_store .cmb2-upload-file.uploading {
        opacity: 0.7;
        pointer-events: none;
    }
    
    .taxonomy-coupon_store .cmb2-upload-file.uploading::after {
        content: '<?php echo esc_js(__('Uploading...', 'halacoupon')); ?>';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 255, 255, 0.9);
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
    }
    </style>
    <?php
}

/**
 * Add help tabs for CMB2 coupon stores interface
 */
add_action('current_screen', 'halacoupon_add_cmb2_help_tabs');
function halacoupon_add_cmb2_help_tabs() {
    $screen = get_current_screen();
    
    // Only add help tabs on coupon_store taxonomy pages
    if (!$screen || $screen->taxonomy !== 'coupon_store') {
        return;
    }
    
    $screen->add_help_tab(array(
        'id' => 'halacoupon-cmb2-overview',
        'title' => __('Enhanced Interface', 'halacoupon'),
        'content' => '<p>' . __('This enhanced interface provides a modern, user-friendly experience for managing coupon stores. All fields support both Arabic and English content with proper RTL/LTR handling.', 'halacoupon') . '</p>'
    ));
    
    $screen->add_help_tab(array(
        'id' => 'halacoupon-cmb2-features',
        'title' => __('Features', 'halacoupon'),
        'content' => '<ul>' .
            '<li>' . __('Enhanced form validation with visual feedback', 'halacoupon') . '</li>' .
            '<li>' . __('Improved file upload interface', 'halacoupon') . '</li>' .
            '<li>' . __('Collapsible FAQ sections with drag-and-drop sorting', 'halacoupon') . '</li>' .
            '<li>' . __('Full RTL/LTR language support', 'halacoupon') . '</li>' .
            '<li>' . __('Responsive design for all screen sizes', 'halacoupon') . '</li>' .
        '</ul>'
    ));
    
    $screen->set_help_sidebar(
        '<p><strong>' . __('For more information:', 'halacoupon') . '</strong></p>' .
        '<p><a href="#" target="_blank">' . __('Documentation', 'halacoupon') . '</a></p>' .
        '<p><a href="#" target="_blank">' . __('Support', 'halacoupon') . '</a></p>'
    );
}
