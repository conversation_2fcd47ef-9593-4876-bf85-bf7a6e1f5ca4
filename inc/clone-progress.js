// Simple JS for polling clone progress and updating the progress bar
(function($){
  function pollProgress() {
    $.get(ajaxurl, { action: 'get_clone_progress' }, function(data) {
      if (data && data.percent !== undefined) {
        $('#clone-progress-bar').css('width', data.percent + '%');
        $('#clone-progress-label').text(data.message);
        if (data.done) {
          $('#clone-progress-label').text('Clone complete!');
          clearInterval(window.cloneProgressInterval);
        }
      }
    });
  }
  $(function(){
    window.cloneProgressInterval = setInterval(pollProgress, 2000);
    pollProgress();
  });
})(jQuery);
