<?php
/**
 * Enhanced Search Modal Component
 * 
 * @package HalaCoupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<?php
/**
 * Clear search modal cache when coupons or stores are updated
 */
function halacoupon_clear_search_modal_cache() {
	delete_transient('search_modal_stores_mobile');
	delete_transient('search_modal_stores_desktop');
}

// Hook into post save/update/delete for coupons
add_action('save_post_coupon', 'halacoupon_clear_search_modal_cache');
add_action('delete_post', function($post_id) {
	if (get_post_type($post_id) === 'coupon') {
		halacoupon_clear_search_modal_cache();
	}
});

// Hook into term save/update/delete for stores
add_action('created_coupon_store', 'halacoupon_clear_search_modal_cache');
add_action('edited_coupon_store', 'halacoupon_clear_search_modal_cache');
add_action('delete_coupon_store', 'halacoupon_clear_search_modal_cache');
?>

<div id="search_modal" class="fixed inset-0 bg-black/80 backdrop-blur-lg z-[9999] hidden items-center justify-center p-4 animate-fade-in" role="dialog" aria-modal="true" aria-labelledby="search-modal-title" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
	<div class="relative bg-white/95 backdrop-blur-xl rounded-3xl shadow-float-lg border border-gray-200 w-[85vw] h-[85vh] overflow-hidden modal-content" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">

		<div class="relative bg-black/95 backdrop-blur-xl p-4 lg:p-6 border-b border-gray-200">
			<div class="relative flex items-center justify-between <?php echo is_rtl() ? 'flex-row' : ''; ?>" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
				<div class="flex items-center gap-3">
					<div class="w-8 h-8 lg:w-10 lg:h-10 bg-primary/20 backdrop-blur-sm rounded-2xl flex items-center justify-center border border-primary/30 shadow-glow">
						<svg class="w-4 h-4 lg:w-5 lg:h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</div>
					<div class="<?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
						<h3 id="search-modal-title" class="text-base lg:text-lg font-bold text-white">
							<?php esc_html_e( 'Discover Amazing Deals', 'halacoupon' ); ?>
						</h3>
						<p class="text-white/80 text-xs mt-0.5">
							<?php esc_html_e( 'Search through thousands of coupons and stores', 'halacoupon' ); ?>
						</p>
					</div>
				</div>
				<button
					class="close_search_modal group flex items-center justify-center w-8 h-8 lg:w-10 lg:h-10 rounded-2xl bg-white/20 backdrop-blur-sm border border-white/30 hover:bg-white/30 hover:border-white/50 transition-all duration-300 hover:scale-105"
					aria-label="<?php esc_attr_e('Close search modal', 'halacoupon'); ?>"
				>
					<svg class="w-4 h-4 lg:w-5 lg:h-5 text-white group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>
		</div>

		<div class="p-4 lg:p-6 h-full flex flex-col bg-white/90 backdrop-blur-sm" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
			<form action="<?php echo esc_url( home_url( '/' ) ); ?>" method="get" id="header-search" class="mb-4 lg:mb-6" aria-label="<?php esc_attr_e( 'Search for Stores and Coupons', 'halacoupon' ); ?>" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
				<div class="relative group">
					<div class="absolute inset-y-0 <?php echo is_rtl() ? 'right-0 pr-3 lg:pr-4' : 'left-0 pl-3 lg:pl-4'; ?> flex items-center pointer-events-none">
						<svg class="w-4 h-4 lg:w-5 lg:h-5 text-gray-400 group-focus-within:text-primary transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</div>
					<div class="absolute inset-y-0 <?php echo is_rtl() ? 'left-0 pl-2 lg:pl-3' : 'right-0 pr-2 lg:pr-3'; ?> flex items-center">
						<!-- Enhanced Loading Animation -->
						<div id="search_loading" class="hidden">
							<div class="w-4 h-4 lg:w-5 lg:h-5 relative">
								<div class="absolute inset-0 border-2 border-primary/20 rounded-full"></div>
								<div class="absolute inset-0 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
							</div>
						</div>
					</div>
					<input
						autocomplete="off"
						class="w-full pi-4 ps-10 pe-14 py-3 text-sm lg:text-base bg-white border-2 border-gray-300 rounded-xl lg:rounded-2xl focus:outline-none focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 placeholder-gray-500 hover:border-gray-400 text-gray-900"
						id="search_input"
						name="s"
						placeholder="<?php esc_attr_e( 'Search for amazing deals, stores, and exclusive coupons...', 'halacoupon' ); ?>"
						type="search"
						aria-label="<?php esc_attr_e( 'Search input', 'halacoupon' ); ?>"
						dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>"
					>
					<button
						type="submit"
						class="absolute inset-y-0 <?php echo is_rtl() ? 'left-0 pl-2 lg:pl-3' : 'right-0 pr-2 lg:pr-3'; ?> flex items-center group"
						aria-label="<?php esc_attr_e( 'Submit search', 'halacoupon' ); ?>"
					>
						<div class="w-8 h-8 lg:w-10 lg:h-10 bg-gradient-to-r from-primary to-secondary rounded-xl lg:rounded-2xl flex items-center justify-center hover:from-secondary hover:to-primary transition-all duration-300 hover:scale-105 shadow-glow hover:shadow-glow-secondary">
							<svg class="w-3 h-3 lg:w-4 lg:h-4 text-black group-hover:<?php echo is_rtl() ? '-translate-x-0.5' : 'translate-x-0.5'; ?> transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo is_rtl() ? 'M11 17l-5-5m0 0l5-5m-5 5h12' : 'M13 7l5 5m0 0l-5 5m5-5H6'; ?>"></path>
							</svg>
						</div>
					</button>
				</div>
			</form>

			<div id="search_results" class="hidden space-y-3 flex-1 overflow-y-auto scrollbar-thin mb-4 lg:mb-6" aria-live="polite" aria-label="<?php esc_attr_e( 'Search results', 'halacoupon' ); ?>" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>"></div>

			<div id="default_content" class="flex-1 overflow-y-auto" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
				<div class="mb-4 lg:mb-6" id="search_tag_cloud">
					<div class="flex items-center gap-2 mb-3 lg:mb-4 ">
						<div class="w-5 h-5 lg:w-6 lg:h-6 bg-gradient-to-r from-secondary to-primary rounded-lg lg:rounded-xl flex items-center justify-center shadow-glow">
							<svg class="w-2.5 h-2.5 lg:w-3 lg:h-3 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
							</svg>
						</div>
						<h4 class="text-xs lg:text-sm font-bold text-gray-900 <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
							<?php esc_html_e('Quick Categories', 'halacoupon'); ?>
						</h4>
					</div>

					<div class="mb-4 lg:mb-6">
						<div class="flex flex-wrap gap-1.5 lg:gap-2 justify-start" id="category_tags" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
							<?php
							// Get popular categories for tag cloud
							$popular_categories = get_terms(array(
								'taxonomy' => 'coupon_category',
								'orderby' => 'count',
								'order' => 'DESC',
								'number' => wp_is_mobile() ? 8 : 15,
								'hide_empty' => true,
							));

							if (!empty($popular_categories) && !is_wp_error($popular_categories)) {
								foreach ($popular_categories as $category) {
									$category_link = get_term_link($category, 'coupon_category');
									if (!is_wp_error($category_link)) {
										echo '<a href="' . esc_url($category_link) . '" class="search-tag category-tag px-2.5 lg:px-4 py-1.5 lg:py-2 text-xs lg:text-sm font-bold bg-secondary/10 hover:bg-secondary/20 border border-secondary/30 hover:border-secondary/50 rounded-lg lg:rounded-xl transition-all duration-300 hover:scale-105 text-gray-800 hover:text-gray-900 shadow-soft hover:shadow-glow-secondary inline-block ' . (is_rtl() ? 'text-right' : 'text-left') . '" data-search="' . esc_attr($category->name) . '" data-type="category" dir="' . (is_rtl() ? 'rtl' : 'ltr') . '">';
										echo esc_html($category->name);
										echo '</a>';
									}
								}
							}
							?>
						</div>
					</div>
				</div>

				<div class="flex-1">
					<div class="flex items-center gap-2 mb-3 lg:mb-4">
						<div class="w-5 h-5 lg:w-6 lg:h-6 bg-gradient-to-r from-primary to-secondary rounded-lg lg:rounded-xl flex items-center justify-center shadow-glow">
							<svg class="w-2.5 h-2.5 lg:w-3 lg:h-3 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
							</svg>
						</div>
						<h4 class="text-xs lg:text-sm font-bold text-gray-900 <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
							<?php esc_html_e('Popular Stores', 'halacoupon'); ?>
						</h4>
					</div>

					<div class="grid grid-cols-2 gap-2 lg:gap-3 <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>" id="store_grid" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
						<?php
						// Check for cached store data first
						$cache_key = 'search_modal_stores_' . (wp_is_mobile() ? 'mobile' : 'desktop');
						$cached_stores_data = get_transient($cache_key);
						$is_fresh_data = false;

						if (false === $cached_stores_data) {
							// Get popular stores with latest coupons
							$popular_stores = get_terms(array(
								'taxonomy' => 'coupon_store',
								'orderby' => 'count',
								'order' => 'DESC',
								'number' => wp_is_mobile() ? 12 : 20,
								'hide_empty' => true,
							));
							$is_fresh_data = true;
						} else {
							// Use cached data
							$popular_stores = $cached_stores_data['stores'] ?? array();
						}

						if (!empty($popular_stores) && !is_wp_error($popular_stores)) {
							// Pre-fetch all latest coupons in a single optimized query to avoid N+1 problem
							$store_ids = wp_list_pluck($popular_stores, 'term_id');
							$latest_coupons_cache = array();

							if (!empty($store_ids)) {
								// Get more coupons to ensure we have at least one per store
								$all_latest_coupons = get_posts(array(
									'post_type' => 'coupon',
									'posts_per_page' => count($store_ids) * 3, // Get 3x more to ensure coverage
									'post_status' => 'publish',
									'orderby' => 'date',
									'order' => 'DESC',
									'tax_query' => array(
										array(
											'taxonomy' => 'coupon_store',
											'field' => 'term_id',
											'terms' => $store_ids,
										)
									),
									'suppress_filters' => false
									// Removed meta_query to include all coupon types
								));

								// Group coupons by store for quick lookup - prioritize latest per store
								foreach ($all_latest_coupons as $coupon) {
									$coupon_stores = wp_get_post_terms($coupon->ID, 'coupon_store', array('fields' => 'ids'));
									foreach ($coupon_stores as $store_id) {
										if (in_array($store_id, $store_ids)) {
											// Only set if we don't have a coupon for this store yet (ensures latest)
											if (!isset($latest_coupons_cache[$store_id])) {
												$latest_coupons_cache[$store_id] = $coupon;
											}
										}
									}
								}

								// Fallback: For stores without coupons from the batch query, try individual queries
								foreach ($store_ids as $store_id) {
									if (!isset($latest_coupons_cache[$store_id])) {
										$individual_coupon = get_posts(array(
											'post_type' => 'coupon',
											'posts_per_page' => 1,
											'post_status' => 'publish',
											'orderby' => 'date',
											'order' => 'DESC',
											'tax_query' => array(
												array(
													'taxonomy' => 'coupon_store',
													'field' => 'term_id',
													'terms' => $store_id,
												)
											),
											'suppress_filters' => false
										));

										if (!empty($individual_coupon)) {
											$latest_coupons_cache[$store_id] = $individual_coupon[0];
										}
									}
								}
							}

							foreach ($popular_stores as $store) {
								// Setup store object to use proper methods
								halacoupon_setup_store($store);
								$store_obj = halacoupon_store();

								// Get coupon title from cache or fallback
								if (isset($latest_coupons_cache[$store->term_id])) {
									$cached_coupon = $latest_coupons_cache[$store->term_id];
									$coupon_title = get_the_title($cached_coupon->ID);

									// Enhanced title validation
									if (empty($coupon_title) ||
										$coupon_title === 'Auto Draft' ||
										$coupon_title === '(no title)' ||
										trim($coupon_title) === '' ||
										strlen(trim($coupon_title)) < 3) {

										// Try to get coupon code or discount as fallback
										$coupon_code = get_post_meta($cached_coupon->ID, '_ags_coupon_code', true);
										$coupon_discount = get_post_meta($cached_coupon->ID, '_ags_coupon_save', true);

										if (!empty($coupon_code) && strlen(trim($coupon_code)) > 2) {
											$coupon_title = sprintf(__('Code: %s', 'halacoupon'), $coupon_code);
										} elseif (!empty($coupon_discount) && strlen(trim($coupon_discount)) > 2) {
											$coupon_title = sprintf(__('Save %s', 'halacoupon'), $coupon_discount);
										} else {
											$coupon_title = __('Latest Deals Available', 'halacoupon');
										}
									}
								} else {
									$coupon_title = __('Latest Deals Available', 'halacoupon');
									// Debug: Log stores without coupons (only in development)
									if (defined('WP_DEBUG') && WP_DEBUG) {
										error_log("Search Modal: Store '{$store->name}' (ID: {$store->term_id}) has no coupons found");
									}
								}
								$store_link = get_term_link($store, 'coupon_store');

								if (!is_wp_error($store_link)) {
									echo '<a href="' . esc_url($store_link) . '" class="store-card block bg-gray-50/90 backdrop-blur-sm border border-gray-200 rounded-lg lg:rounded-xl p-3 lg:p-4 hover:bg-white hover:border-gray-300 hover:shadow-soft transition-all duration-300 hover:scale-101 ' . (is_rtl() ? 'text-right' : 'text-left') . '" data-search="' . esc_attr($store->name) . '" data-type="store" dir="' . (is_rtl() ? 'rtl' : 'ltr') . '">';
								} else {
									echo '<div class="store-card bg-gray-50/90 backdrop-blur-sm border border-gray-200 rounded-lg lg:rounded-xl p-3 lg:p-4 cursor-pointer hover:bg-white hover:border-gray-300 hover:shadow-soft transition-all duration-300 hover:scale-101 ' . (is_rtl() ? 'text-right' : 'text-left') . '" data-search="' . esc_attr($store->name) . '" data-type="store" dir="' . (is_rtl() ? 'rtl' : 'ltr') . '">';
								}

								// Store Image and Name with RTL/LTR Support
								echo '<div class="flex items-start gap-2 lg:gap-3 mb-2">';

								// Use proper store thumbnail method with full RTL/LTR coverage
								if ($store_obj->has_thumbnail()) {
									echo '<div class="w-10 h-10 lg:w-12 lg:h-12 bg-white rounded-lg overflow-hidden border border-gray-200 flex-shrink-0">';
									echo '<div class="w-full h-full">';
									echo str_replace('class="', 'class="w-full h-full object-cover ', $store_obj->get_thumbnail('thumbnail'));
									echo '</div>';
									echo '</div>';
								} else {
									echo '<div class="w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center flex-shrink-0">';
									echo '<span class="text-black font-bold text-sm lg:text-lg">' . esc_html(substr($store->name, 0, 1)) . '</span>';
									echo '</div>';
								}

								echo '<div class="flex-1 min-w-0 ' . (is_rtl() ? 'text-right' : 'text-left') . '" dir="' . (is_rtl() ? 'rtl' : 'ltr') . '">';
								echo '<h5 class="font-bold text-gray-900 text-xs lg:text-sm truncate ' . (is_rtl() ? 'text-right' : 'text-left') . '">' . esc_html($store->name) . '</h5>';
								echo '<p class="text-gray-600 text-xs mt-1 line-clamp-2 ' . (is_rtl() ? 'text-right' : 'text-left') . '">' . esc_html($coupon_title) . '</p>';
								echo '</div>';
								echo '</div>';

								// Store Stats with RTL/LTR Support
								echo '<div class="flex items-center ' . (is_rtl() ? 'justify-end flex-row-reverse' : 'justify-start') . ' text-xs gap-1" dir="' . (is_rtl() ? 'rtl' : 'ltr') . '">';
								echo '<svg class="w-3 h-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
								echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>';
								echo '</svg>';
								echo '<span class="text-gray-500 font-medium">' . sprintf(_n('%d coupon', '%d coupons', $store->count, 'halacoupon'), $store->count) . '</span>';
								echo '</div>';

								if (!is_wp_error($store_link)) {
									echo '</a>';
								} else {
									echo '</div>';
								}
							}
						}
						?>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
