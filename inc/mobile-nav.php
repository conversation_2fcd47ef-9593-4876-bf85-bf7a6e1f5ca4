<!-- Enhanced Brand Mobile Menu Modal -->
<div id="mobile_menu_modal" class="modal-overlay hidden fixed inset-0 bg-black/80 backdrop-blur-lg z-[9999] lg:hidden animate-fade-in" role="dialog" aria-modal="true" aria-labelledby="mobile-menu-title">
	<div class="flex items-start <?php echo is_rtl() ? 'justify-start' : 'justify-end'; ?> min-h-screen">
		<div class="modal-content w-full max-w-sm h-full bg-black/95 backdrop-blur-xl shadow-float-lg <?php echo is_rtl() ? 'border-r border-primary/30 animate-slide-in-left' : 'border-l border-primary/30 animate-slide-in-right'; ?> overflow-y-auto">

			<!-- Enhanced Brand Header with Logo -->
			<div class="relative bg-gradient-to-br from-primary via-secondary to-accent p-6 overflow-hidden">

				<!-- Creative Background Elements -->
				<div class="absolute inset-0 bg-gradient-to-br from-black/10 to-transparent"></div>
				<div class="absolute top-0 right-0 w-24 h-24 bg-white/5 rounded-full transform translate-x-12 -translate-y-12"></div>
				<div class="absolute bottom-0 left-0 w-16 h-16 bg-white/5 rounded-full transform -translate-x-8 translate-y-8"></div>

				<div class="relative flex items-center justify-between" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
					<!-- Logo Section -->
					<div class="flex items-center gap-3">
						<?php if ( halacoupon_get_option('site_logo', false, 'url') != '' ) : ?>
							<a
								href="<?php echo esc_url( home_url( '/' ) ); ?>"
								title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>"
								rel="home"
								class="relative block group"
								aria-label="<?php echo esc_attr( sprintf( __('Go to %s homepage', 'halacoupon'), get_bloginfo( 'name' ) ) ); ?>"
							>
								<div class="relative p-3 bg-white/20 backdrop-blur-sm rounded-2xl border border-white/30 group-hover:bg-white/30 group-hover:border-white/50 transition-all duration-300 group-hover:scale-105 shadow-glow">
									<img
										src="<?php echo esc_url( halacoupon_get_option('site_logo', false, 'url') ); ?>"
										alt="<?php echo esc_attr( get_bloginfo( 'name' ) ); ?>"
										class="h-8 w-auto transition-all duration-300 group-hover:scale-105"
										loading="eager"
										decoding="async"
									/>
								</div>
							</a>
						<?php else : ?>
							<div class="text-center">
								<?php if ( is_home() || is_front_page() ) : ?>
									<h1 class="text-xl font-bold">
										<a
											href="<?php echo esc_url( home_url( '/' ) ); ?>"
											class="group relative inline-block px-4 py-2 logo-gradient-text text-white"
											title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>"
											rel="home"
										>
											<?php bloginfo( 'name' ); ?>
											<span class="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></span>
										</a>
									</h1>
								<?php else : ?>
									<h2 class="text-xl font-bold">
										<a
											href="<?php echo esc_url( home_url( '/' ) ); ?>"
											class="group relative inline-block px-4 py-2 logo-gradient-text text-white"
											title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>"
											rel="home"
										>
											<?php bloginfo( 'name' ); ?>
											<span class="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></span>
										</a>
									</h2>
								<?php endif; ?>
							</div>
						<?php endif; ?>

						<!-- Screen Reader Only Menu Title -->
						<div class="sr-only">
							<h3 id="mobile-menu-title">
								<?php esc_html_e('Menu', 'halacoupon'); ?>
							</h3>
							<p>
								<?php esc_html_e('Navigate our site', 'halacoupon'); ?>
							</p>
						</div>
					</div>

					<!-- Close Button -->
					<button
						data-modal-close="mobile_menu_modal"
						class="group flex items-center justify-center w-10 h-10 rounded-2xl bg-white/20 backdrop-blur-sm border border-white/30 hover:bg-white/30 hover:border-white/50 transition-all duration-300 hover:scale-105 shadow-soft"
						aria-label="<?php esc_attr_e('Close mobile menu', 'halacoupon'); ?>"
					>
						<svg class="w-5 h-5 text-white group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M6 18L18 6M6 6l12 12"></path>
						</svg>
					</button>
				</div>
			</div>

			<!-- Enhanced Mobile Navigation Menu -->
			<div class="p-6 bg-black/90 backdrop-blur-sm border-b border-primary/20">
				<nav class="space-y-3" role="navigation" aria-label="<?php esc_attr_e('Mobile Navigation', 'halacoupon'); ?>" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
					<?php
					wp_nav_menu( array(
						'theme_location' => 'primary',
						'container' => false,
						'menu_class' => 'space-y-3',
						'fallback_cb' => false,
						'walker' => new class extends Walker_Nav_Menu {
							function start_lvl( &$output, $depth = 0, $args = null ) {
								$margin_class = is_rtl() ? 'mr-6' : 'ml-6';
								$output .= '<ul class="' . $margin_class . ' mt-2 space-y-2">';
							}
							function end_lvl( &$output, $depth = 0, $args = null ) {
								$output .= '</ul>';
							}
							function start_el( &$output, $item, $depth = 0, $args = null, $id = 0 ) {
								$output .= '<li>';
								if ( $depth === 0 ) {
									$output .= '<a href="' . esc_url( $item->url ) . '" class="group flex items-center px-5 py-4 text-white hover:text-black bg-primary/20 hover:bg-gradient-to-r hover:from-primary hover:to-secondary rounded-2xl border border-primary/30 hover:border-primary/50 transition-all duration-300 hover:scale-105 hover:shadow-glow backdrop-blur-sm">';
									$dot_margin = is_rtl() ? 'ml-3' : 'mr-3';
									$output .= '<div class="w-2 h-2 bg-primary rounded-full ' . $dot_margin . ' group-hover:bg-black group-hover:scale-125 transition-all duration-300"></div>';
								} else {
									$translate_class = is_rtl() ? 'hover:-translate-x-1' : 'hover:translate-x-1';
									$output .= '<a href="' . esc_url( $item->url ) . '" class="block px-5 py-3 text-white/80 hover:text-primary bg-black/50 hover:bg-primary/10 rounded-xl transition-all duration-300 ' . $translate_class . ' border border-primary/20 hover:border-primary/40">';
								}
								$output .= '<span class="font-semibold">' . esc_html( $item->title ) . '</span>';
								$output .= '</a>';
							}
							function end_el( &$output, $item, $depth = 0, $args = null ) {
								$output .= '</li>';
							}
						}
					) );
					?>
				</nav>
			</div>

			<!-- Enhanced Contact/Social Section -->
			<div class="p-6 bg-black/90 backdrop-blur-sm border-b border-primary/20">
				<div class="text-center mb-5">
					<div class="flex items-center justify-center gap-3 mb-3">
						<div class="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-2xl flex items-center justify-center shadow-glow">
							<svg class="w-4 h-4 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
							</svg>
						</div>
						<h3 class="text-lg font-bold text-white">
							<?php esc_html_e( 'Connect With Us', 'halacoupon' ); ?>
						</h3>
					</div>
					<p class="text-sm text-white/70 leading-relaxed px-4">
						<?php esc_html_e( 'Stay connected for the latest deals, exclusive offers, and coupon updates', 'halacoupon' ); ?>
					</p>
				</div>
				<div class="flex justify-center space-x-4">
					<?php echo halacoupon_display_social_profiles(); ?>
				</div>
			</div>

			<!-- Enhanced Mobile Menu Footer -->
			<div class="p-6 bg-black/95 backdrop-blur-sm">
				<div class="text-center space-y-3">
					<div class="flex items-center justify-center gap-2">
						<div class="w-6 h-6 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center shadow-glow">
							<svg class="w-3 h-3 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
							</svg>
						</div>
						<p class="text-sm font-medium text-white">
							<?php esc_html_e( 'Made with love for savings', 'halacoupon' ); ?>
						</p>
					</div>
					<p class="text-xs text-white/60">
						<?php printf(
							esc_html__( '© %s %s - All rights reserved', 'halacoupon' ),
							date('Y'),
							get_bloginfo('name')
						); ?>
					</p>
				</div>
			</div>
		</div>
	</div>
</div>