<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */


if ( file_exists(  get_template_directory() . '/inc/metabox/init.php' ) ) {
	require_once  get_template_directory() . '/inc/metabox/init.php';
    require_once  get_template_directory() . '/inc/metabox-addons/icon/icon.php';
}

// Load enhanced CMB2 admin styles for coupon stores
if ( file_exists( get_template_directory() . '/inc/admin/cmb2-coupon-stores-admin-styles.php' ) ) {
    require_once get_template_directory() . '/inc/admin/cmb2-coupon-stores-admin-styles.php';
}


function cmb2_change_minutes_step( $l10n ){
    $l10n['defaults']['time_picker']['stepMinute'] = 1;
    return $l10n;
}

add_filter( 'cmb2_localized_data', 'cmb2_change_minutes_step' );


/**
 * Sanitizes WYSIWYG fields like WordPress does for post_content fields.
 */
function cmb2_html_content_sanitize( $content ) {
    return apply_filters( 'content_save_pre', $content );
}


/**
 * Metabox for Show on page IDs callback
 * <AUTHOR> Morton
 * @link https://github.com/WebDevStudios/CMB2/wiki/Adding-your-own-show_on-filters
 *
 * @param bool $display
 * @param array $meta_box
 * @return bool display metabox
 */
function halacoupon_metabox_show_on_cb( $field ) {
    global $post;

    $meta_box = $field->args;
    if ( ! isset( $meta_box['show_on_page'] ) ) {
        return true ;
    }

    $post_id = $post->ID;

    if ( ! $post_id ) {
        return false;
    }

    // See if there's a match
    return in_array( $post_id, (array) $meta_box['show_on_page'] );
}




/**
 * Add metaboxes for coupons, organized into different sections.
 */
/**
 * Add metaboxes for page templates
 */
add_action( 'cmb2_admin_init', 'halacoupon_register_page_template_metaboxes' );
function halacoupon_register_page_template_metaboxes() {
    $prefix = '_halacoupon_';

    // Stores List Template Metabox
    $stores_list_meta = new_cmb2_box( array(
        'id'            => 'stores_list_template_settings',
        'title'         => esc_html__( 'Stores List Template Settings', 'halacoupon' ),
        'object_types'  => array( 'page' ),
        'context'       => 'normal',
        'priority'      => 'high',
        'show_names'    => true,
        'show_on_cb'    => 'halacoupon_show_on_stores_list_template',
    ));

    $stores_list_meta->add_field( array(
        'name'    => esc_html__( 'Custom Content After Stores Grid', 'halacoupon' ),
        'desc'    => esc_html__( 'Add custom content that will appear after the stores grid section', 'halacoupon' ),
        'id'      => $prefix . 'stores_list_custom_content',
        'type'    => 'wysiwyg',
        'options' => array(
            'textarea_rows' => 8,
            'media_buttons' => true,
        ),
    ));

    // All Coupons Template Metabox
    $all_coupons_meta = new_cmb2_box( array(
        'id'            => 'all_coupons_template_settings',
        'title'         => esc_html__( 'All Coupons Template Settings', 'halacoupon' ),
        'object_types'  => array( 'page' ),
        'context'       => 'normal',
        'priority'      => 'high',
        'show_names'    => true,
        'show_on_cb'    => 'halacoupon_show_on_all_coupons_template',
    ));

    $all_coupons_meta->add_field( array(
        'name'    => esc_html__( 'Custom Content After Coupons Grid', 'halacoupon' ),
        'desc'    => esc_html__( 'Add custom content that will appear after the coupons grid section', 'halacoupon' ),
        'id'      => $prefix . 'all_coupons_custom_content',
        'type'    => 'wysiwyg',
        'options' => array(
            'textarea_rows' => 8,
            'media_buttons' => true,
        ),
    ));
}

/**
 * Callback to show metabox only on stores list template
 */
function halacoupon_show_on_stores_list_template( $cmb ) {
    $post_id = $cmb->object_id();
    $template = get_page_template_slug( $post_id );
    return 'templates/stores-list.php' === $template;
}

/**
 * Callback to show metabox only on all coupons template
 */
function halacoupon_show_on_all_coupons_template( $cmb ) {
    $post_id = $cmb->object_id();
    $template = get_page_template_slug( $post_id );
    return 'templates/all-coupons.php' === $template;
}

add_action( 'cmb2_admin_init', 'halacoupon_register_coupon_metaboxes' );
function halacoupon_register_coupon_metaboxes() {
    $prefix = '_ags_';

    // General Settings Metabox
    $general_meta = new_cmb2_box( array(
        'id'            => 'coupon_general_settings',
        'title'         => esc_html__( 'إعدادات الكوبون العامة', 'halacoupon' ),
        'object_types'  => array( 'coupon' ),
        'context'       => 'normal',
        'priority'      => 'high',
        'show_names'    => true,
    ));

    // Store selection dropdown field
    $general_meta->add_field( array(
        'name'    => esc_html__( 'أختر المتجر', 'halacoupon' ),
        'desc'    => esc_html__( 'اختر أو أضف متجر جديد', 'halacoupon' ),
        'id'      => $prefix . 'coupon_store',
        'type'    => 'taxonomy_select',
        'taxonomy' => 'coupon_store',
        'remove_default' => 'true',
        'attributes' => array(
            'data-placeholder' => esc_html__( 'Search or Add a Store...', 'halacoupon' ),
        ),
    ));

    // Coupon Type
    $general_meta->add_field( array(
        'name'    => esc_html__( 'نوع الكوبون', 'halacoupon' ),
        'id'      => $prefix . 'coupon_type',
        'type'    => 'select',
        'show_option_none' => false,
        'options' => halacoupon_get_coupon_types(),
    ));

    // Coupon Code (conditionally shown)
    $general_meta->add_field( array(
        'name'       => esc_html__( 'رمز الكود', 'halacoupon' ),
        'id'         => $prefix . 'coupon_type_code',
        'type'       => 'text_medium',
        'attributes' => array(
            'placeholder' => esc_html__( 'AGSSSSSSSS', 'halacoupon' ),
        ),
        'before_row' => '<div class="cmb-row" data-show-when="code" data-show-on="' . $prefix . 'coupon_type' . '">',
        'after_row'  => '</div>',
    ));

    // Affiliate Link
    $general_meta->add_field( array(
        'name' => esc_html__( 'لينك الأفلييت', 'halacoupon' ),
        'id'   => $prefix . 'destination_url',
        'type' => 'text_url',
        'attributes' => array(
            'placeholder' => esc_html__( 'http://...', 'halacoupon' ),
        ),
    ));

    // Discount Percentage
    $general_meta->add_field( array(
        'name'       => esc_html__( 'نسبة الخصم', 'halacoupon' ),
        'id'         => $prefix . 'coupon_save',
        'type'       => 'text_medium',
        'attributes' => array(
            'placeholder' => esc_html__( '30%', 'halacoupon' ),
        ),
        'default'    => '30%',
    ));

    // Exclusive Coupon
    $general_meta->add_field( array(
        'name' => esc_html__( 'كوبون مميز ؟', 'halacoupon' ),
        'desc' => esc_html__( 'اجعل الكوبون يظهر في صفحه المتجر في الأعلي دائما ؟', 'halacoupon' ),
        'id'   => $prefix . 'exclusive',
        'type' => 'checkbox',
    ));


	$general_meta->add_field( array(
        'name' => esc_html__( 'Terms & Conditions', 'halacoupon' ),
        'id'   => $prefix . 'coupon_terms',
        'type' => 'wysiwyg',
		'options' => array(
            'wpautop' => true, // use wpautop?
            'media_buttons' => true, // show insert/upload button(s)
            'textarea_rows' => 10, // rows="..."
            'teeny' => false, // output the minimal editor config used in Press This
            'dfw' => false, // replace the default fullscreen with DFW (needs specific css)
            'tinymce' => true, // load TinyMCE, can be used to pass settings directly to TinyMCE using an array()
            'quicktags' => true, // load Quicktags, can be used to pass settings directly to Quicktags using an array()
        ),
    ));


   
    // Statistics Metabox
    $stats_meta = new_cmb2_box( array(
        'id'            => 'coupon_stats',
        'title'         => esc_html__( 'إحصائيات الكوبون', 'halacoupon' ),
        'object_types'  => array( 'coupon' ),
        'context'       => 'side',
        'priority'      => 'low',
        'show_names'    => true,
    ));

    // Hidden Stats Fields
    $stats_meta->add_field( array(
        'name'     => esc_html__( 'Number Coupon Used', 'halacoupon' ),
        'id'       => $prefix . 'used',
        'type'     => 'text',
        'show_on_cb' => 'halacoupon_hide_field_callback',
    ));

    $stats_meta->add_field( array(
        'name'     => esc_html__( 'Number Views', 'halacoupon' ),
        'id'       => $prefix . 'views',
        'type'     => 'text',
        'show_on_cb' => 'halacoupon_hide_field_callback',
    ));

    $stats_meta->add_field( array(
        'name'     => esc_html__( 'Vote Up', 'halacoupon' ),
        'id'       => $prefix . 'vote_up',
        'type'     => 'text',
        'show_on_cb' => 'halacoupon_hide_field_callback',
    ));

    $stats_meta->add_field( array(
        'name'     => esc_html__( 'Vote Down', 'halacoupon' ),
        'id'       => $prefix . 'vote_down',
        'type'     => 'text',
        'show_on_cb' => 'halacoupon_hide_field_callback',
    ));
}

// Hide fields callback function
function halacoupon_hide_field_callback() {
    return false; // Always hide stats fields in the dashboard
}

/**
 * Hook in and add a metabox to add fields to taxonomy terms
 */
add_action( 'cmb2_admin_init', 'halacoupon_register_coupon_store_taxonomy_metabox' );
function halacoupon_register_coupon_store_taxonomy_metabox() {
    $prefix = '_ags_';

    /**
     * Metabox to add fields to coupon store
     */
    $store_meta = new_cmb2_box( array(
        'id'               => $prefix . 'store_meta',
        'title'            => esc_html__( 'Store Descriptions', 'halacoupon' ),
        'object_types'     => array( 'term' ),
        'taxonomies'       => array( 'coupon_store' ), 
        // 'new_term_section' => true, 
    ) );


	$store_meta->add_field( array(
        'name'          => esc_html__( 'عنوان  المتجر للسيو H1', 'halacoupon' ),
        'id'            => $prefix . 'store_heading',
        'type'          => 'text_medium',
        'sanitization_cb'    => 'cmb2_html_content_sanitize'
    ) );


	$store_meta->add_field( array(
        'name'    => esc_html__( 'صوره المتجر ', 'halacoupon' ),
        'id'      => $prefix . 'store_image',
        'type'    => 'file',
        // Optional:
        'options' => array(
            'url' => false,
            'add_upload_file_text' => 'Upload Image' 
        ),
    ) );

	$store_meta->add_field( array(
        'name'          => esc_html__( 'لينك المتجر', 'halacoupon' ),
        'id'            => $prefix . 'store_url',
        'type'          => 'text_url',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'https://abdullah-g.com', 'halacoupon' ),
        ),
    ) );

    $store_meta->add_field( array(
        'name'          => esc_html__( 'لينك الافلييت الخاص بك', 'halacoupon' ),
        'id'            => $prefix . 'store_aff_url',
        'desc'          => esc_html__( 'اكتب رابط الافليت الخاص بك لهذا المتجر إن وجد !', 'halacoupon' ),
        'type'          => 'text_url',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'https://abdullah-g.com', 'halacoupon' ),
        ),
    ) );



    $store_meta->add_field( array(
        'name'          => esc_html__( 'Is Store Exclusive?', 'halacoupon' ),
        'desc'          => esc_html__( 'فعل هذا اذا كنت تريد المتجر يظهر في الصفحة الرئيسية والمتاجر الحصرية', 'halacoupon' ),
        'id'            => $prefix . 'is_featured',
        'type'          => 'checkbox'
    ) );

	$store_meta->add_field( array(
        'name'          => esc_html__( 'Pin Store beside home slider?', 'halacoupon' ),
		'desc'          => esc_html__( 'If you want to pin this store beside the home slider, check this box.', 'halacoupon' ),
        'id'            => $prefix . 'pinned_store',
        'type'          => 'checkbox'
    ) );


	$store_meta->add_field( array(
        'name'     => esc_html__( 'Full Content', 'halacoupon' ),
        'id'       => $prefix . 'extra_info',
        'type'     => 'wysiwyg',
        'options' => array(
            'wpautop' => true, // use wpautop?
            'media_buttons' => true, // show insert/upload button(s)
            ///'textarea_name' => $editor_id, // set the textarea name to something different, square brackets [] can be used here
            'textarea_rows' => get_option('default_post_edit_rows', 12), // rows="..."
            'tabindex' => '',
            'editor_css' => '', // intended for extra styles for both visual and HTML editors buttons, needs to include the `<style>` tags, can use "scoped".
            'editor_class' => '', // add extra class(es) to the editor textarea
            'teeny' => false, // output the minimal editor config used in Press This
            'dfw' => false, // replace the default fullscreen with DFW (needs specific css)
            'tinymce' => true, // load TinyMCE, can be used to pass settings directly to TinyMCE using an array()
            'quicktags' => true // load Quicktags, can be used to pass settings directly to Quicktags using an array()
        ),
        'on_front' => true,
    ) );



	// Define a CMB2 box for the 'coupon_store' taxonomy
    $cmb = new_cmb2_box(array(
        'id'            => $prefix . 'coupon_store_faq',
        'title'         => esc_html__('الأسئلة الشائعة', 'coupon_store'),
        'object_types'  => array('term'), // It's a term-based field
        'taxonomies'    => array('coupon_store'), // Specify the taxonomy
        'show_names'    => true,
    ));

    // Add a repeater group for FAQs
    $faq_group_field_id = $cmb->add_field(array(
        'id'          => $prefix . 'faq_group',
        'type'        => 'group',
		'description' => esc_html__('Frequently Asked Questions', 'halacoupon'),
        'options'     => array(
            'group_title'   => esc_html__('Question {#}', 'halacoupon'), // Group title format
            'add_button'    => esc_html__('Add New Question', 'halacoupon'),
            'remove_button' => esc_html__('Remove Question', 'halacoupon'),
            'sortable'      => true, // Allow sorting of FAQs
        ),
    ));

    // Add 'question' field inside the FAQ repeater
    $cmb->add_group_field($faq_group_field_id, array(
        'name'        => esc_html__('Question', 'halacoupon'),
        'id'          => 'question',
        'type'        => 'text',
    ));

    // Add 'answer' field inside the FAQ repeater
    $cmb->add_group_field($faq_group_field_id, array(
        'name'        => esc_html__('الجواب', 'coupon_store'),
        'id'          => 'answer',
        'type'        => 'wysiwyg',
        'options'     => array(
            'media_buttons' => false, // Hide media button from editor
        ),
    ));




    /**
     * Metabox to add social Profiles to coupon store
     */
    $store_social_profile = new_cmb2_box( array(
        'id'               => $prefix . 'store_social_profile',
        'title'            => esc_html__( 'Store Social Profiles', 'halacoupon' ),
        'object_types'     => array( 'term' ),
        'taxonomies'       => array( 'coupon_store' ), 
    ) );

	$store_social_profile->add_field( array(
        'name'          => esc_html__( 'Facebook', 'halacoupon' ),
        'id'            => $prefix . 'store_fb',
        'type'          => 'text_url',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'https://abdullah-g.com', 'halacoupon' ),
        ),
    ) );

		$store_social_profile->add_field( array(
        'name'          => esc_html__( 'X', 'halacoupon' ),
        'id'            => $prefix . 'store_x',
        'type'          => 'text_url',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'https://abdullah-g.com', 'halacoupon' ),
        ),
    ) );


		$store_social_profile->add_field( array(
        'name'          => esc_html__( 'instagram', 'halacoupon' ),
        'id'            => $prefix . 'store_insta',
        'type'          => 'text_url',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'https://abdullah-g.com', 'halacoupon' ),
        ),
    ) );


		$store_social_profile->add_field( array(
        'name'          => esc_html__( 'Pintrest', 'halacoupon' ),
        'id'            => $prefix . 'store_pin',
        'type'          => 'text_url',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'https://abdullah-g.com', 'halacoupon' ),
        ),
    ) );

		$store_social_profile->add_field( array(
        'name'          => esc_html__( 'Contact link', 'halacoupon' ),
        'id'            => $prefix . 'store_contact_url',
        'type'          => 'text_url',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'https://abdullah-g.com', 'halacoupon' ),
        ),
    ) );

		$store_social_profile->add_field( array(
        'name'          => esc_html__( 'Delivery link', 'halacoupon' ),
        'id'            => $prefix . 'store_delivery_url',
        'type'          => 'text_url',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'https://abdullah-g.com', 'halacoupon' ),
        ),
    ) );

		$store_social_profile->add_field( array(
        'name'          => esc_html__( 'Returns Policy link', 'halacoupon' ),
        'id'            => $prefix . 'store_returns_policy_url',
        'type'          => 'text_url',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'https://abdullah-g.com', 'halacoupon' ),
        ),
    ) );

		$store_social_profile->add_field( array(
        'name'          => esc_html__( 'FAQs link', 'halacoupon' ),
        'id'            => $prefix . 'store_faq_url',
        'type'          => 'text_url',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'https://abdullah-g.com', 'halacoupon' ),
        ),
    ) );



    /**
     * Metabox to add fields to Coupon categories
     */
    $cat_meta = new_cmb2_box( array(
        'id'               => $prefix . 'coupon_category_meta',
        'title'            => esc_html__( 'Category info', 'halacoupon' ),
        'object_types'     => array( 'term' ), // Tells CMB2 to use term_meta vs post_meta
        'taxonomies'       => array( 'coupon_category' ), // Tells CMB2 which taxonomies should have these fields
        'new_term_section' => true, // Will display in the "Add New Category" section
    ) );


	$cat_meta->add_field( array(
        'name'          => esc_html__( 'Icon', 'halacoupon' ),
        'id'            => $prefix . 'icon',
        'type'          => 'icon',
        'desc'          => 'Category icon',
    ) );


	$slides = new_cmb2_box(array(
        'id'            => $prefix . 'metabox',
        'title'         => __('بيانات السلايدر ', 'halacoupon'),
        'object_types'  => array('slides'),
    ));

	    $slides->add_field(array(
        'name' => __('Headline Text', 'halacoupon'),
        'id'   => $prefix . 'slide_headline_text',
        'type' => 'text',
    ));

	
    $slides->add_field(array(
        'name' => __('Button Text', 'halacoupon'),
        'id'   => $prefix . 'slide_button_text',
        'type' => 'text',
    ));



    $slides->add_field(array(
        'name' => __('Button_link', 'halacoupon'),
        'id'   => $prefix . 'slide_button_url',
        'type' => 'text_url',
    ));
}






add_action( 'admin_enqueue_scripts', 'halacoupon_enqueue_select2_for_taxonomy' );
function halacoupon_enqueue_select2_for_taxonomy( $hook_suffix ) {
    // Only load on the coupon post type page
    if ( 'post.php' != $hook_suffix && 'post-new.php' != $hook_suffix ) {
        return;
    }

    wp_enqueue_style( 'select2-css', get_template_directory_uri() . '/inc/metabox-addons/select2lib/select2.min.css', array(), '4.0.13' );
    wp_enqueue_script( 'select2-js', get_template_directory_uri() . '/inc/metabox-addons/select2lib/select2.min.js', array( 'jquery' ), '4.0.13', true );

    wp_add_inline_script( 'select2-js', '
        jQuery(document).ready(function($) {
            $("#_ags_coupon_store").select2({
                width: "100%",
                placeholder: "' . esc_html__( 'Select or Add a Store', 'halacoupon' ) . '",
                tags: true, // Enable tagging to allow new term creation
                createTag: function (params) {
                    var term = $.trim(params.term);
                    if (term === "") {
                        return null;
                    }
                    return {
                        id: term, // Temporary ID (using the name itself as the ID)
                        text: term, // Display the term name
                        newTag: true // Mark it as a new tag
                    };
                },
                insertTag: function (data, tag) {
                    data.push(tag); // Add the new tag to the list
                },
                dir: "rtl" // Enable RTL for the dropdown
            }).on("select2:select", function(e) {
                var data = e.params.data;
                if (data.newTag) {
                    // Send AJAX request to create a new term
                    $.ajax({
                        type: "POST",
                        url: ajaxurl,
                        data: {
                            action: "add_coupon_store_term",
                            new_term: data.text, // Send the new term name to server
                            taxonomy: "coupon_store",
                            _ajax_nonce: "' . wp_create_nonce( 'add_coupon_store_term' ) . '"
                        },
                        success: function(response) {
                            if (response.success) {
                                var newOption = new Option(response.data.term_name, response.data.term_name, false, true); // Using term_name as both value and text
                                $("#_ags_coupon_store").find("option[value=\'" + data.id + "\']").remove(); // Remove temporary option
                                $("#_ags_coupon_store").append(newOption).trigger("change"); // Append with correct term name
                            } else {
                                alert(response.data.message); // Display error message if any
                            }
                        }
                    });
                }
            });
        });
    ' );
}




add_action( 'wp_ajax_add_coupon_store_term', 'halacoupon_add_coupon_store_term' );
function halacoupon_add_coupon_store_term() {
    // Check nonce for security
    check_ajax_referer( 'add_coupon_store_term', '_ajax_nonce' );

    // Get the new term from the AJAX request
    $new_term = isset( $_POST['new_term'] ) ? sanitize_text_field( $_POST['new_term'] ) : '';
    $taxonomy = isset( $_POST['taxonomy'] ) ? sanitize_text_field( $_POST['taxonomy'] ) : 'coupon_store';

    // Validate the new term
    if ( empty( $new_term ) ) {
        wp_send_json_error( array( 'message' => esc_html__( 'The term cannot be empty.', 'halacoupon' ) ) );
        return;
    }

    // Insert the new term into the taxonomy
    $term = wp_insert_term( $new_term, $taxonomy );

    // Handle any errors when inserting the term
    if ( is_wp_error( $term ) ) {
        wp_send_json_error( array( 'message' => $term->get_error_message() ) );
    } else {
        // On success, return only the term name
        wp_send_json_success( array(
            'term_name' => $new_term, // Return the term name (the value the user input)
        ) );
    }
}




add_action( 'add_meta_boxes', 'halacoupon_hide_stats_metabox', 100 );
function halacoupon_hide_stats_metabox() {
    // Check if we're in the admin dashboard
    if ( is_admin() ) {
        // Remove the metabox from display in the admin dashboard
        remove_meta_box( 'coupon_stats', 'coupon', 'side' );
    }
}


