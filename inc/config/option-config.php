<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */



if ( ! class_exists( 'halacoupon_Theme_Options_Config' ) ) {

    class halacoupon_Theme_Options_Config {

        public $args = array();
        public $sections = array();
        public $theme;
        public $ReduxFramework;

        public function __construct() {

            if ( ! class_exists( 'ReduxFramework' ) ) {
                return;
            }
            $this->initSettings();
        }


        public function initSettings() {

            // Set the default arguments
            $this->setArguments();

            // Set a few help tabs so you can see how it's done
            // $this->setHelpTabs();

            // Create the sections and fields
            $this->setSections();

            if ( ! isset( $this->args['opt_name'] ) ) { // No errors please
                return;
            }

            $this->args = apply_filters( 'st_redux_theme_options_args', $this->args );

            $this->ReduxFramework = new ReduxFramework( $this->sections, $this->args );
        }


        /**
         * All the possible arguments for Redux.
         * For full documentation on arguments, please refer to: https://github.com/ReduxFramework/ReduxFramework/wiki/Arguments
         * */
        public function setArguments() {

            $theme = wp_get_theme(); // For use with some settings. Not necessary.

            $this->args = array(
                // TYPICAL -> Change these values as you need/desire
                'opt_name'           => 'ag_halacoupon',
                // This is where your data is stored in the database and also becomes your global variable name.
                'display_name'       => $theme->get( 'Name' ).' Options',
                // Name that appears at the top of your panel
                'display_version'    => false,
                // Version that appears at the top of your panel
                'menu_type'          => 'menu', // submenu , menu
                // Specify if the admin menu should appear or not. Options: menu or submenu (Under appearance only)
                'allow_sub_menu'     => false,
                // Show the sections below the admin menu item or not
                'menu_title'         => esc_html__( 'إعدادات القالب', 'halacoupon' ),
                'page_title'         => esc_html__( 'إعدادات القالب', 'halacoupon' ),
                // You will need to generate a Google API key to use this feature.
                // Please visit: https://developers.google.com/fonts/docs/developer_api#Auth
                'google_api_key'     => '',
                // Must be defined to add google fonts to the typography module
                'async_typography'   => false,
                // Use a asynchronous font on the front end or font string
                'admin_bar'          => true,
                // Show the panel pages on the admin bar
                'global_variable'    => 'ag_option',
                // Set a different name for your global variable other than the opt_name
                'dev_mode'           => false,
                // Show the time the page took to load, etc
                'customizer'         => false,
                // Enable basic customizer support
                // OPTIONAL -> Give you extra features
                'page_priority'      => 26,
                // Order where the menu appears in the admin area. If there is any conflict, something will not show. Warning.
                'page_parent'        => 'themes.php', // themes.php
                // For a full list of options, visit: http://codex.wordpress.org/Function_Reference/add_submenu_page#Parameters
                'page_permissions'   => 'manage_options',
                // Permissions needed to access the options panel.
                'menu_icon'          => '',
                // Specify a custom URL to an icon
                'last_tab'           => '',
                // Force your panel to always open to a specific tab (by id)
                'page_icon'          => 'icon-themes',
                // Icon displayed in the admin panel next to your menu_title
                'page_slug'          => 'halacoupon_options',
                // Page slug used to denote the panel
                'save_defaults'      => true,
                // On load save the defaults to DB before user clicks save or not
                'default_show'       => false,
                // If true, shows the default value next to each field that is not the default value.
                'default_mark'       => '',
                // What to print by the field's title if the value shown is default. Suggested: *
                'show_import_export' => true,
                // Shows the Import/Export panel when not used as a field.
                // CAREFUL -> These options are for advanced use only
                'transient_time'     => 60 * MINUTE_IN_SECONDS,

                'output'             => true,
                // Global shut-off for dynamic CSS output by the framework. Will also disable google fonts output
                'output_tag'         => true,
                // Allows dynamic CSS to be generated for customizer and google fonts, but stops the dynamic CSS from going to the head
                'footer_credit'     => 'Develop by <a href="https://abdullah-g.com/">Abdullah Gamal</a>',
                // Disable the footer credit of Redux. Please leave if you can help it.
                // FUTURE -> Not in use yet, but reserved or partially implemented. Use at your own risk.
                'database'           => '',
                // possible: options, theme_mods, theme_mods_expanded, transient. Not fully functional, warning!
                'system_info'        => false,
                // REMOVE
                // HINTS
                'hints'              => array(
                    'icon'          => 'icon-question-sign',
                    'icon_position' => 'right',
                    'icon_color'    => 'lightgray',
                    'icon_size'     => 'normal',
                    'tip_style'     => array(
                        'color'   => 'light',
                        'shadow'  => true,
                        'rounded' => false,
                        'style'   => '',
                    ),
                    'tip_position'  => array(
                        'my' => 'top left',
                        'at' => 'bottom right',
                    ),
                    'tip_effect'    => array(
                        'show' => array(
                            'effect'   => 'slide',
                            'duration' => '500',
                            'event'    => 'mouseover',
                        ),
                        'hide' => array(
                            'effect'   => 'slide',
                            'duration' => '500',
                            'event'    => 'click mouseleave',
                        ),
                    ),
                ),
            );



        }

        public function setSections() {


            /*
            --------------------------------------------------------*/
            /*
             GENERAL SETTINGS
            /*--------------------------------------------------------*/
            $this->sections[] = array(
                'title'  => esc_html__( 'إعدادات عامة', 'halacoupon' ),
                'desc'   => '',
                'icon'   => 'el-icon-cog el-icon-large',
                'submenu' => true,
                'fields' => array(
                    array(
                       'id' => 'section-start',
                       'type' => 'section',
                       'title' => esc_html__('أعدادات الهوية', 'halacoupon'),
                       'indent' => true 
                    ),
                    array(
                        'id'       => 'site_logo',
                        'url'      => false,
                        'type'     => 'media',
                        'title'    => esc_html__( 'Site logo', 'halacoupon' ),
                        'default'  => array( 'url' => get_template_directory_uri() . '/assets/images/logo.png' ),
                    ),
                    array(
                        'id'       => 'site_logo',
                        'url'      => false,
                        'type'     => 'media',
                        'title'    => esc_html__( 'لوجو الموقع', 'halacoupon' ),
                        'required' => array(
                            array('id'=>'site_logo', 'required' => true)
                        ),
                        'compiler' => 'true',
                        'options'  => array(
                            'url'      => false
                        ),
                        'settings' => array(
                            'upload'    => true,
                            'size'      => 'thumbnail', 
                            'library-type' => 'image',
                        ),
                        'hint'      => array(
                            'content' => esc_html__('Please upload your site logo here.', 'halacoupon'),
                        ),
                    ),
                    array(
                        'id'       => 'logo_width',
                        'type'     => 'slider',
                        'title'    => esc_html__( 'حجم اللوجو PX', 'halacoupon' ),
                        'default'  => '160',
                        'validate' => 'numeric',
                        'min'      => '80', 
                        'max'      => '300', 
                        'step'     => '1',   
                    ),
                    array(
                        'id'       => 'site_logo_retina',
                        'url'      => false,
                        'type'     => 'media',
                        'title'    => esc_html__( 'لوجو اجهزه الريتينا 	X2', 'halacoupon' ),
                        'default'  => '',
                        'subtitle' => esc_html__( '<EMAIL>', 'halacoupon' ),

                    ),

					array(
                        'id'   => 'divider_er',
                        'type' => 'section',
						'title'    => esc_html__( 'اعدادات الصفحات', 'halacoupon' ),
						'indent' => true 

                    ),



					array(
						'id'       => 'all_stores_page',
						'type'     => 'select',
						'data'     => 'pages',
						'title'    => esc_html__('صفحه المتاجر', 'halacoupon'),
						'default'  => '',
					),
					array(
						'id'       => 'all_categories_page',
						'type'     => 'select',
						'data'     => 'pages',
						'title'    => esc_html__('صفحه تصنيفات الكوبونات', 'halacoupon'),
						'default'  => '',
					),

					array(
                        'id'   => 'divider_er',
                        'type' => 'section',
						'title'    => esc_html__( 'اعدادات الروابط', 'halacoupon' ),
						'required' => array('some_non_existent_field', 'equals', 'non_existent_value'),
						'indent' => true 

                    ),


                    array(
                        'id'      => 'layout',
                        'title'   => esc_html__( 'Site Layout', 'halacoupon' ),
                        'desc'    => esc_html__( 'Default site layout', 'halacoupon' ),
                        'type'    => 'button_set',
                        'default' => 'right-sidebar',
						'required' => array('some_non_existent_field', 'equals', 'non_existent_value'),                        
						'options' => array(
                            'left-sidebar'   => esc_html__( 'Left sidebar', 'halacoupon' ),
                            'no-sidebar'     => esc_html__( 'No sidebar', 'halacoupon' ),
                            'right-sidebar'  => esc_html__( 'Right sidebar', 'halacoupon' ),
                        ),
                    ),
                    array(
                        'id'       => 'search_only_coupons',
                        'type'     => 'checkbox',
                        'title'    => esc_html__( 'Show only coupons on search results', 'halacoupon' ),
                        'default'  => 1,
						'required' => array('some_non_existent_field', 'equals', 'non_existent_value'),

                    ),

					array(
                        'id'       => 'rewrite_store_slug',
                        'url'      => false,
                        'type'     => 'text',
                        'title'    => esc_html__( 'Custom Store rewrite slug', 'halacoupon' ),
                        'subtitle'    => esc_html__( 'Default: store', 'halacoupon' ),
                        'default'  => 'discount-codes',
						'required' => array('some_non_existent_field', 'equals', 'non_existent_value'),

                    ),

                    array(
                        'id'       => 'rewrite_category_slug',
                        'url'      => false,
                        'type'     => 'text',
                        'title'    => esc_html__( 'Custom coupon category rewrite slug', 'halacoupon' ),
                        'subtitle'    => esc_html__( 'Default: coupon-category', 'halacoupon' ),
                        'default'  => 'coupon-category',
						'required' => array('some_non_existent_field', 'equals', 'non_existent_value'),


                    ),

                    array(
                        'id'       => 'rewrite_country_slug',
                        'url'      => false,
                        'type'     => 'text',
                        'title'    => esc_html__( 'Custom coupon country rewrite slug', 'halacoupon' ),
                        'subtitle'    => esc_html__( 'Default: coupon-country', 'halacoupon' ),
                        'default'  => 'coupon-country',
						'required' => array('some_non_existent_field', 'equals', 'non_existent_value'),


                    ),


					array(
                        'id'       => 'go_store_slug',
                        'type'     => 'text',
                        'default' => 'go-store',
                        'title'    => esc_html__( 'Custom goto store slug', 'halacoupon' ),
						'required' => array('some_non_existent_field', 'equals', 'non_existent_value'),

                    ),


					array(
                        'id'       => 'go_out_slug',
                        'type'     => 'text',
                        'default' => 'out',
                        'title'    => esc_html__( 'Custom coupon go out slug', 'halacoupon' ),
						'required' => array('some_non_existent_field', 'equals', 'non_existent_value'),

                    ),


					array(
                        'id'   => 'divider_nr',
                        'type' => 'divide',
                    ),




                ),
				
            );







        
            /*
            --------------------------------------------------------*/
            /*
             Contact fields */
            /*--------------------------------------------------------*/
            $this->sections[] = array(
				'title'  => esc_html__( 'بيانات التواصل', 'halacoupon' ), // 'Contact Information'
				'desc'   => '',
				'icon'   => 'el-icon-tags',
				'submenu' => true,
				'fields' => array(
			
					array(
						'id'       => 'contact_form_email',
						'type'     => 'text',
						'default'  => '<EMAIL>',
						'validate' => 'email',  
						'title'    => esc_html__( 'Form Receive Email', 'halacoupon' ),
						'subtitle' => esc_html__( 'Enter the email that will receive contact form submissions.', 'halacoupon' ),
					),
			
					array(
						'id'       => 'contact_phone_number',
						'type'     => 'text',
						'title'    => esc_html__( 'Contact Phone Number', 'halacoupon' ),
						'subtitle' => esc_html__( 'Enter the phone number for contact purposes.', 'halacoupon' ),
						'default'  => '**************',  
					),

					array(
						'id'   => 'divider_nr',
						'type' => 'divide',
					),
	
					array(
						'id'       => 'newsletter_api_key',
						'type'     => 'text',
						'title'    => esc_html__('Mailchimp API Key', 'halacoupon'),
						'subtitle' => esc_html__('Enter your Mailchimp API key', 'halacoupon'),
					),
					array(
						'id'       => 'newsletter_list_id',
						'type'     => 'text',
						'title'    => esc_html__('Mailchimp List ID', 'halacoupon'),
						'subtitle' => esc_html__('Enter your Mailchimp List ID', 'halacoupon'),
					),

					
			
			
				),

				
			);

            /*
            --------------------------------------------------------*/






            /*
             COUPON ITEM
            /*--------------------------------------------------------*/
            $this->sections[] = array(
                'title'  => esc_html__( 'إعدادات الفوتر', 'halacoupon' ),
                'desc'   => '',
                'icon'   => 'el-icon-tag',
                'submenu' => true,
                'fields' => array(

					array(
                        'id'   => 'divider_rrtr',
						'title'    => esc_html__( 'نصوص الفوتر أسفل الموقع', 'halacoupon' ),
                        'type' => 'section',
						'indent' => true 

                    ),


					array(
                        'id'       => 'footer_text_identity',
                        'type'     => 'textarea',
                        'title'    => esc_html__( 'نص بعد الوجو', 'halacoupon' ),
                        'default'  => 'كوبونكو هو وجهتك الأولى للتوفير! حيث نقدم لك كوبونات وأكواد خصم حصرية وفعالة للعديد من المتاجر الإلكترونية. استمتع بتجربة تسوق موفرة وذكية ومميزة معنا.',
                    ),


					
					array(
                        'id'       => 'footer_text_help',
                        'type'     => 'textarea',
                        'title'    => esc_html__( 'نص التنبية', 'halacoupon' ),
                        'default'  => 'AGS || +201001248698',
                    ),




					array(
						'id'       => 'social_profiles_icons',
						'type'     => 'repeater',
						'title'    => esc_html__('صفحات التواصل الأجتماعي', 'halacoupon'),
						'fields'   => array(
							array(
								'id'       => 'profile_social_icon',
								'type'     => 'select',
								'title'    => esc_html__('Social Media Icon', 'halacoupon'),
								'options'  => array(
									'facebook'   => esc_html__('Facebook', 'halacoupon'),
									'twitter'    => esc_html__('Twitter', 'halacoupon'),
									'instagram'  => esc_html__('Instagram', 'halacoupon'),
									'linkedin'   => esc_html__('LinkedIn', 'halacoupon'),
									'youtube'    => esc_html__('YouTube', 'halacoupon'),
									'pinterest'  => esc_html__('Pinterest', 'halacoupon'),
									'snapchat'   => esc_html__('Snapchat', 'halacoupon'),
									'tiktok'     => esc_html__('TikTok', 'halacoupon'),
									'whatsapp'   => esc_html__('WhatsApp', 'halacoupon'),
									'telegram'   => esc_html__('Telegram', 'halacoupon'),
								),
								'default'  => 'facebook',
							),
							array(
								'id'       => 'profile_social_url',
								'type'     => 'text',
								'title'    => esc_html__('Social Media URL', 'halacoupon'),
								'validate' => 'url',
								'default'  => '#',
							),
							
						),
					),


                    array(
                        'id'       => 'sale_prev_tab',
                        'type'     => 'checkbox',
                        'default' => true,
                        'title'    => esc_html__( 'Open store website in new tab when click on "Get Deal" button.', 'halacoupon' ),
						'required' => array('some_non_existent_field', 'equals', 'non_existent_value'),


                    ),

                    array(
                        'id'       => 'code_prev_tab',
                        'type'     => 'checkbox',
                        'default' => true,
                        'title'    => esc_html__( 'Open store website in new tab when click on "Get Code" button.', 'halacoupon' ),
						'required' => array('some_non_existent_field', 'equals', 'non_existent_value'),


                    ),

                    array(
                        'id'       => 'coupon_click_action',
                        'type'     => 'button_set',
                        'default' => 'prev',
                        'options' => array(
                            'prev' => __( 'Previous Tab', 'halacoupon' ),
                            'next' => __( 'Next Tab', 'halacoupon' ),
                        ),
                        'title'    => esc_html__( 'Action when open store website.', 'halacoupon' ),
						'required' => array('some_non_existent_field', 'equals', 'non_existent_value'),


                    ),
                ),
            );
           


           

            $this->sections = apply_filters( 'halacoupon_more_options_settings', $this->sections );

        }

    }

    global $reduxConfig;
    function halacoupon_options_init() {
        global $reduxConfig;
        // force remove sample redux demo option
        delete_option( 'ReduxFrameworkPlugin' );
        $reduxConfig = new halacoupon_Theme_Options_Config();
    }
    add_action( 'init', 'halacoupon_options_init' );

}


/**
 * Removes the demo link and the notice of integrated demo from the redux-framework plugin
 */
if ( ! function_exists( 'halacoupon_remove_demo' ) ) {
    function halacoupon_remove_demo() {
        // Used to hide the demo mode link from the plugin page. Only used when Redux is a plugin.
        if ( class_exists( 'ReduxFrameworkPlugin' ) ) {
            remove_filter(
                'plugin_row_meta',
                array(
                    ReduxFrameworkPlugin::instance(),
                    'plugin_metalinks',
                ),
                null,
                2
            );

            // Used to hide the activation notice informing users of the demo panel. Only used when Redux is a plugin.
            remove_action( 'admin_notices', array( ReduxFrameworkPlugin::instance(), 'admin_notices' ) );
        }
    }
}
halacoupon_remove_demo();


