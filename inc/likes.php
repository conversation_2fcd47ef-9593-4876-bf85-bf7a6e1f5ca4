<?php
/**
 * Likes functionality for HalaCoupon Theme
 *
 * This file contains the like button functionality including:
 * - Like button display with user tracking
 * - IP-based vote tracking to prevent duplicates
 * - AJAX integration for voting
 * - Visual feedback for liked/unliked states
 *
 * @package HalaCoupon
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display like button for a coupon
 * 
 * @param int $coupon_id The coupon ID
 * @param array $args Additional arguments
 * @return string HTML output
 */
function halacoupon_display_like_button($coupon_id = null, $args = []) {
    if (!$coupon_id) {
        $coupon_id = get_the_ID();
    }
    
    if (!$coupon_id || get_post_type($coupon_id) !== 'coupon') {
        return '';
    }
    
    $defaults = [
        'show_label' => true,
        'show_count' => true,
        'button_class' => '',
        'icon_class' => 'w-4 h-4',
        'label_text' => __('Helpful?', 'halacoupon'),
        'aria_label' => __('Vote if this coupon worked for you', 'halacoupon')
    ];
    
    $args = wp_parse_args($args, $defaults);
    
    // Check if user has already voted
    $user_has_voted = halacoupon_user_has_voted($coupon_id);
    $vote_count = halacoupon_get_coupon_vote_count($coupon_id);
    $total_votes = halacoupon_get_coupon_total_votes($coupon_id);
    
    // Determine button state
    $is_liked = $user_has_voted;
    $button_classes = 'like-btn flex items-center gap-1 px-4 py-2 rounded-full text-sm font-bold shadow-soft transition-all duration-300 transform hover:scale-105 border';
    $icon_classes = $args['icon_class'];
    
    if ($is_liked) {
        // User has already voted - show liked state
        $button_classes .= ' bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border-green-200 cursor-not-allowed opacity-75';
        $icon_classes .= ' text-green-600';
        $aria_label = __('You have already voted for this coupon', 'halacoupon');
    } else {
        // User hasn't voted - show interactive state
        $button_classes .= ' bg-gradient-to-r from-primary-100 to-accent-100 hover:bg-gradient-primary text-primary-700 hover:text-white hover:shadow-medium border-primary-200';
        $aria_label = $args['aria_label'];
    }
    
    if ($args['button_class']) {
        $button_classes .= ' ' . $args['button_class'];
    }
    
    ob_start();
    ?>
    
    <div class="like-button-container">
        <?php if ($args['show_label']): ?>
            <span class="text-sm text-gray-600 font-medium"><?php echo esc_html($args['label_text']); ?></span>
        <?php endif; ?>
        
        <button 
            class="<?php echo esc_attr($button_classes); ?>"
            data-coupon-id="<?php echo esc_attr($coupon_id); ?>"
            data-vote-count="<?php echo esc_attr($vote_count); ?>"
            data-total-votes="<?php echo esc_attr($total_votes); ?>"
            data-has-voted="<?php echo $is_liked ? '1' : '0'; ?>"
            type="button"
            <?php if ($is_liked): ?>
                disabled
            <?php endif; ?>
            aria-label="<?php echo esc_attr($aria_label); ?>">
            
            <svg class="<?php echo esc_attr($icon_classes); ?>" fill="<?php echo $is_liked ? 'currentColor' : 'none'; ?>" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"/>
            </svg>
            
            <?php if ($args['show_count']): ?>
                <span class="font-bold vote-count"><?php echo esc_html($vote_count); ?></span>
                <?php if ($total_votes > $vote_count): ?>
                    <span class="text-xs opacity-75 total-votes">/<?php echo esc_html($total_votes); ?></span>
                <?php endif; ?>
            <?php endif; ?>
        </button>
    </div>
    
    <?php
    return ob_get_clean();
}

/**
 * Check if current user has voted for a coupon
 * 
 * @param int $coupon_id The coupon ID
 * @return bool True if user has voted
 */
function halacoupon_user_has_voted($coupon_id) {
    $user_ip = halacoupon_get_user_ip();
    $vote_key = 'coupon_vote_' . $coupon_id . '_' . md5($user_ip);
    
    // Check if we have a record of this vote
    $voted_ips = get_post_meta($coupon_id, '_ags_voted_ips', true);
    if (!is_array($voted_ips)) {
        $voted_ips = [];
    }
    
    return in_array($user_ip, $voted_ips);
}

/**
 * Get user IP address
 * 
 * @return string User IP address
 */
function halacoupon_get_user_ip() {
    $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * Get coupon vote count
 * 
 * @param int $coupon_id The coupon ID
 * @return int Vote count
 */
function halacoupon_get_coupon_vote_count($coupon_id) {
    $vote_up = intval(get_post_meta($coupon_id, '_ags_vote_up', true));
    return max(0, $vote_up);
}

/**
 * Get total votes for a coupon (upvotes + downvotes)
 * 
 * @param int $coupon_id The coupon ID
 * @return int Total vote count
 */
function halacoupon_get_coupon_total_votes($coupon_id) {
    $vote_up = intval(get_post_meta($coupon_id, '_ags_vote_up', true));
    $vote_down = intval(get_post_meta($coupon_id, '_ags_vote_down', true));
    return max(0, $vote_up + $vote_down);
}

/**
 * Get coupon vote statistics
 * 
 * @param int $coupon_id The coupon ID
 * @return array Vote statistics
 */
function halacoupon_get_coupon_vote_stats($coupon_id) {
    $vote_up = intval(get_post_meta($coupon_id, '_ags_vote_up', true));
    $vote_down = intval(get_post_meta($coupon_id, '_ags_vote_down', true));
    $total_votes = $vote_up + $vote_down;
    
    return [
        'upvotes' => $vote_up,
        'downvotes' => $vote_down,
        'total_votes' => $total_votes,
        'success_rate' => $total_votes > 0 ? round(($vote_up / $total_votes) * 100) : 0
    ];
}

/**
 * Record user vote for a coupon
 * 
 * @param int $coupon_id The coupon ID
 * @param int $vote_value Vote value (1 for up, -1 for down)
 * @return bool Success status
 */
function halacoupon_record_user_vote($coupon_id, $vote_value = 1) {
    if (!class_exists('halacoupon_Coupon_Tracking')) {
        return false;
    }
    
    $user_ip = halacoupon_get_user_ip();
    
    // Check if user already voted
    if (halacoupon_user_has_voted($coupon_id)) {
        return false;
    }
    
    // Record the vote
    halacoupon_Coupon_Tracking::vote($coupon_id, $vote_value);
    
    // Store user IP to prevent duplicate votes
    $voted_ips = get_post_meta($coupon_id, '_ags_voted_ips', true);
    if (!is_array($voted_ips)) {
        $voted_ips = [];
    }
    
    $voted_ips[] = $user_ip;
    update_post_meta($coupon_id, '_ags_voted_ips', $voted_ips);
    
    return true;
}

/**
 * Get like button with echo (for template usage)
 * 
 * @param int $coupon_id The coupon ID
 * @param array $args Additional arguments
 */
function halacoupon_like_button($coupon_id = null, $args = []) {
    echo halacoupon_display_like_button($coupon_id, $args);
}

/**
 * Display detailed vote statistics for a coupon
 * 
 * @param int $coupon_id The coupon ID
 * @param array $args Additional arguments
 * @return string HTML output
 */
function halacoupon_display_vote_stats($coupon_id = null, $args = []) {
    if (!$coupon_id) {
        $coupon_id = get_the_ID();
    }
    
    if (!$coupon_id || get_post_type($coupon_id) !== 'coupon') {
        return '';
    }
    
    $defaults = [
        'show_success_rate' => true,
        'show_total_votes' => true,
        'container_class' => 'vote-stats-container',
        'stat_class' => 'vote-stat-item'
    ];
    
    $args = wp_parse_args($args, $defaults);
    
    $stats = halacoupon_get_coupon_vote_stats($coupon_id);
    
    if ($stats['total_votes'] === 0) {
        return '';
    }
    
    ob_start();
    ?>
    
    <div class="<?php echo esc_attr($args['container_class']); ?> flex items-center gap-4 text-sm text-gray-600">
        <?php if ($args['show_total_votes']): ?>
        <div class="<?php echo esc_attr($args['stat_class']); ?> flex items-center gap-1">
            <svg class="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="font-medium"><?php echo esc_html($stats['total_votes']); ?> <?php esc_html_e('total votes', 'halacoupon'); ?></span>
        </div>
        <?php endif; ?>
        
        <?php if ($args['show_success_rate']): ?>
        <div class="<?php echo esc_attr($args['stat_class']); ?> flex items-center gap-1">
            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            <span class="font-medium"><?php echo esc_html($stats['success_rate']); ?>% <?php esc_html_e('success rate', 'halacoupon'); ?></span>
        </div>
        <?php endif; ?>
    </div>
    
    <?php
    return ob_get_clean();
}

/**
 * Display vote statistics with echo (for template usage)
 * 
 * @param int $coupon_id The coupon ID
 * @param array $args Additional arguments
 */
function halacoupon_vote_stats($coupon_id = null, $args = []) {
    echo halacoupon_display_vote_stats($coupon_id, $args);
}