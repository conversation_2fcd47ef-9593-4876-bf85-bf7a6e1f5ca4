<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */






add_action( 'init', 'halacoupon_theme_post_types_init' );
function halacoupon_theme_post_types_init() {
    // Coupon post type
    $labels = array(
        'name'               => esc_html_x( 'Coupons', 'post type general name', 'halacoupon' ),
        'singular_name'      => esc_html_x( 'Coupon', 'post type singular name', 'halacoupon' ),
        'menu_name'          => esc_html_x( 'الكوبونات والعروض', 'admin menu', 'halacoupon' ),
        'name_admin_bar'     => esc_html_x( 'Coupon', 'add new on admin bar', 'halacoupon' ),
        'add_new'            => esc_html_x( 'اضف كوبون', 'coupon', 'halacoupon' ),
        'add_new_item'       => esc_html__( 'Add New Coupon', 'halacoupon' ),
        'new_item'           => esc_html__( 'New Coupon', 'halacoupon' ),
        'edit_item'          => esc_html__( 'Edit Coupon', 'halacoupon' ),
        'view_item'          => esc_html__( 'View Coupon', 'halacoupon' ),
        'all_items'          => esc_html__( 'كل الكوبونات', 'halacoupon' ),
        'search_items'       => esc_html__( 'إبحث عن كوبون', 'halacoupon' ),
        'parent_item_colon'  => esc_html__( 'Parent Coupons:', 'halacoupon' ),
        'not_found'          => esc_html__( 'No coupons found.', 'halacoupon' ),
        'not_found_in_trash' => esc_html__( 'No coupons found in Trash.', 'halacoupon' ),
        'attributes'        => esc_html__( 'Coupon attributes', 'halacoupon' )
    );

    $enable_single = false;
    if ( $enable_single ) {
        $enable_single = true;
    } else {
        $enable_single = false;
    }

    $args = array(
        'labels'             => $labels,
        'public'             => $enable_single,
        'publicly_queryable' => $enable_single,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array( 'slug' => 'coupon' ),
        'capability_type'    => 'post',
        'has_archive'        => false,
        'hierarchical'       => false,
        'exclude_from_search' => false,
        'feeds'              => true,
        'menu_position'      => null,
        'menu_icon'          => 'dashicons-tickets-alt',
        'supports'           => array( 'title', 'editor', 'thumbnail', 'author', 'excerpt', 'comments', 'page-attributes' )
    );

    register_post_type( 'coupon', $args );


    /**
     * Store category
     * Add new taxonomy, make it hierarchical (like categories)
     */

	 $cat_slug = trim( halacoupon_get_option( 'rewrite_category_slug', '' ) );
    if ( ! $cat_slug ) {
        $cat_slug = 'coupon-category';
    }

    $labels = array(
        'name'              => esc_html_x( 'Coupon Categories', 'taxonomy general name', 'halacoupon' ),
        'singular_name'     => esc_html_x( 'Coupon Category', 'taxonomy singular name', 'halacoupon' ),
        'search_items'      => esc_html__( 'Search Coupon Categories', 'halacoupon' ),
        'all_items'         => esc_html__( 'All Coupon Categories', 'halacoupon' ),
        'parent_item'       => esc_html__( 'Parent Coupon Category', 'halacoupon' ),
        'parent_item_colon' => esc_html__( 'Parent Coupon Category:', 'halacoupon' ),
        'edit_item'         => esc_html__( 'Edit Coupon Category', 'halacoupon' ),
        'update_item'       => esc_html__( 'Update Category', 'halacoupon' ),
        'add_new_item'      => esc_html__( 'Add New Coupon Category', 'halacoupon' ),
        'new_item_name'     => esc_html__( 'New Coupon Category Name', 'halacoupon' ),
        'menu_name'         => esc_html__( 'أقسام الكوبونات ', 'halacoupon' ),
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => false,
        'query_var'         => false,
        'show_in_menu'      => true,
        'show_in_nav_menus' => true,
        'show_in_quick_edit' => true,
        'rewrite'           => array( 'slug' => $cat_slug ),
    );

    register_taxonomy( 'coupon_category', array( 'coupon' ), $args );

    /**
     * Coupon Store
     *
     * Add new taxonomy, make it hierarchical (like categories)
     */

	 $store_slug = trim( halacoupon_get_option( 'rewrite_store_slug', '' ) );
    if ( ! $store_slug ) {
        $store_slug = 'discount-codes';
    }

    $labels = array(
        'name'              => esc_html_x( 'Coupon Stores', 'taxonomy general name', 'halacoupon' ),
        'singular_name'     => esc_html_x( 'Coupon Store', 'taxonomy singular name', 'halacoupon' ),
        'search_items'      => esc_html__( 'Search Stores', 'halacoupon' ),
        'all_items'         => esc_html__( 'All Stores', 'halacoupon' ),
        'parent_item'       => esc_html__( 'Parent Store', 'halacoupon' ),
        'parent_item_colon' => esc_html__( 'Parent Store:', 'halacoupon' ),
        'update_item'       => esc_html__( 'Update Store', 'halacoupon' ),
        'add_new_item'      => esc_html__( 'Add New Store', 'halacoupon' ),
        'new_item_name'     => esc_html__( 'New Store', 'halacoupon' ),
        'menu_name'         => esc_html__( 'المتاجر', 'halacoupon' ),
        'view_item'         => esc_html__( 'View Store', 'halacoupon' ),
        'edit_item'         => esc_html__( 'Edit Store', 'halacoupon' ),
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'show_in_menu'      => true,
        'show_in_nav_menus' => true,
        'show_in_quick_edit'=> true,
        'rewrite'           => array( 'slug' => $store_slug ),
    );

    register_taxonomy( 'coupon_store', array( 'coupon' ), $args );

//     /**
//      * Coupon Country
//      *
//      * Add new taxonomy, make it hierarchical (like categories)
//      */

// 	 $country_slug = trim( halacoupon_get_option( 'rewrite_country_slug', '' ) );
// 	 if ( ! $country_slug ) {
// 		 $country_slug = 'coupon-country';
// 	 }
//     $labels = array(
//         'name'              => esc_html_x( 'Coupon Countries', 'taxonomy general name', 'halacoupon' ),
//         'singular_name'     => esc_html_x( 'Coupon Country', 'taxonomy singular name', 'halacoupon' ),
//         'search_items'      => esc_html__( 'Search Countries', 'halacoupon' ),
//         'all_items'         => esc_html__( 'جميع الدول', 'halacoupon' ),
//         'parent_item'       => esc_html__( 'Parent Country', 'halacoupon' ),
//         'parent_item_colon' => esc_html__( 'Parent Country:', 'halacoupon' ),
//         'update_item'       => esc_html__( 'Update Country', 'halacoupon' ),
//         'add_new_item'      => esc_html__( 'Add New Country', 'halacoupon' ),
//         'new_item_name'     => esc_html__( 'New Country', 'halacoupon' ),
//         'menu_name'         => esc_html__( 'الدول', 'halacoupon' ),
//         'view_item'         => esc_html__( 'View Country', 'halacoupon' ),
//         'edit_item'         => esc_html__( 'Edit Country', 'halacoupon' ),
//     );

//     $args = array(
//         'hierarchical'      => false,
//         'labels'            => $labels,
//         'show_ui'           => true,
//         'show_admin_column' => false,
//         'query_var'         => true,
//         'show_in_menu'      => true,
//         'show_in_nav_menus' => false,
//         'show_in_quick_edit'=> true,
//         'rewrite'           => array( 'slug' => $country_slug ),
//     );

//     register_taxonomy( 'coupon_country', array( 'coupon' ), $args );


}



function create_slides_cpt() {
    $labels = array(
        'name' => _x('Slides', 'Post Type General Name', 'halacoupon'),
        'singular_name' => _x('Slide', 'Post Type Singular Name', 'halacoupon'),
        'menu_name' => __('السلايدر', 'halacoupon'),
        'all_items' => __('All Slides', 'halacoupon'),
        'add_new_item' => __('Add New Slide', 'halacoupon'),
        'add_new' => __('Add New', 'halacoupon'),
        'edit_item' => __('Edit Slide', 'halacoupon'),
        'update_item' => __('Update Slide', 'halacoupon'),
        'view_item' => __('View Slide', 'halacoupon'),
        'search_items' => __('Search Slide', 'halacoupon'),
    );

    $args = array(
        'label' => __('Slide', 'halacoupon'),
        'description' => __('Slides for the homepage slider', 'halacoupon'),
        'labels' => $labels,
        'supports' => array('title', 'thumbnail'),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'menu_position' => 5,
        'show_in_admin_bar' => true,
        'show_in_nav_menus' => false,
        'can_export' => true,
        'has_archive' => false,
        'exclude_from_search' => true,
        'publicly_queryable' => false,
        'capability_type' => 'post',
        'rewrite' => false,
    );

    register_post_type('slides', $args);
}
add_action('init', 'create_slides_cpt', 0);

/**
 * Reviews Custom Post Type
 *
 * Create custom post type for review articles about stores and products
 * Blog-like articles where users can rate the review articles themselves
 */
function halacoupon_create_reviews_cpt() {
    $labels = array(
        'name'                  => _x('Review Articles', 'Post Type General Name', 'halacoupon'),
        'singular_name'         => _x('Review Article', 'Post Type Singular Name', 'halacoupon'),
        'menu_name'             => __('مقالات المراجعات', 'halacoupon'),
        'name_admin_bar'        => _x('Review Article', 'add new on admin bar', 'halacoupon'),
        'archives'              => __('Review Articles Archive', 'halacoupon'),
        'attributes'            => __('Review Article Attributes', 'halacoupon'),
        'parent_item_colon'     => __('Parent Review Article:', 'halacoupon'),
        'all_items'             => __('كل مقالات المراجعات', 'halacoupon'),
        'add_new_item'          => __('Add New Review Article', 'halacoupon'),
        'add_new'               => __('اضف مقال مراجعة', 'halacoupon'),
        'new_item'              => __('New Review Article', 'halacoupon'),
        'edit_item'             => __('Edit Review Article', 'halacoupon'),
        'update_item'           => __('Update Review Article', 'halacoupon'),
        'view_item'             => __('View Review Article', 'halacoupon'),
        'view_items'            => __('View Review Articles', 'halacoupon'),
        'search_items'          => __('Search Review Articles', 'halacoupon'),
        'not_found'             => __('No review articles found', 'halacoupon'),
        'not_found_in_trash'    => __('No review articles found in Trash', 'halacoupon'),
        'featured_image'        => __('Article Featured Image', 'halacoupon'),
        'set_featured_image'    => __('Set article featured image', 'halacoupon'),
        'remove_featured_image' => __('Remove article featured image', 'halacoupon'),
        'use_featured_image'    => __('Use as article featured image', 'halacoupon'),
        'insert_into_item'      => __('Insert into review article', 'halacoupon'),
        'uploaded_to_this_item' => __('Uploaded to this review article', 'halacoupon'),
        'items_list'            => __('Review articles list', 'halacoupon'),
        'items_list_navigation' => __('Review articles list navigation', 'halacoupon'),
        'filter_items_list'     => __('Filter review articles list', 'halacoupon'),
    );

    $args = array(
        'label'                 => __('Review Article', 'halacoupon'),
        'description'           => __('Editorial review articles about stores and products that users can rate', 'halacoupon'),
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail', 'author', 'excerpt', 'comments', 'custom-fields', 'post-formats'),
        'taxonomies'            => array('review_category', 'post_tag'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 6,
        'menu_icon'             => 'dashicons-edit-page',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
        'rewrite'               => array(
            'slug'                  => 'review-article',
            'with_front'            => false,
            'pages'                 => true,
            'feeds'                 => true,
        ),
    );

    register_post_type('review', $args);

    // Register Review Article Categories Taxonomy
    $cat_labels = array(
        'name'                       => _x('Review Article Categories', 'Taxonomy General Name', 'halacoupon'),
        'singular_name'              => _x('Review Article Category', 'Taxonomy Singular Name', 'halacoupon'),
        'menu_name'                  => __('أقسام مقالات المراجعات', 'halacoupon'),
        'all_items'                  => __('All Review Article Categories', 'halacoupon'),
        'parent_item'                => __('Parent Review Article Category', 'halacoupon'),
        'parent_item_colon'          => __('Parent Review Article Category:', 'halacoupon'),
        'new_item_name'              => __('New Review Article Category Name', 'halacoupon'),
        'add_new_item'               => __('Add New Review Article Category', 'halacoupon'),
        'edit_item'                  => __('Edit Review Article Category', 'halacoupon'),
        'update_item'                => __('Update Review Article Category', 'halacoupon'),
        'view_item'                  => __('View Review Article Category', 'halacoupon'),
        'separate_items_with_commas' => __('Separate review article categories with commas', 'halacoupon'),
        'add_or_remove_items'        => __('Add or remove review article categories', 'halacoupon'),
        'choose_from_most_used'      => __('Choose from the most used', 'halacoupon'),
        'popular_items'              => __('Popular Review Article Categories', 'halacoupon'),
        'search_items'               => __('Search Review Article Categories', 'halacoupon'),
        'not_found'                  => __('Not Found', 'halacoupon'),
        'no_terms'                   => __('No review article categories', 'halacoupon'),
        'items_list'                 => __('Review article categories list', 'halacoupon'),
        'items_list_navigation'      => __('Review article categories list navigation', 'halacoupon'),
    );

    $cat_args = array(
        'labels'                     => $cat_labels,
        'hierarchical'               => true,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
        'rewrite'                    => array(
            'slug'                       => 'review-article-category',
            'with_front'                 => false,
            'hierarchical'               => true,
        ),
    );

    register_taxonomy('review_category', array('review'), $cat_args);
}
add_action('init', 'halacoupon_create_reviews_cpt', 0);




if ( is_admin()  ) {

    class  halacoupon_Theme_Edit_Taxs_Columns{

        function __construct() {

            add_filter('manage_edit-coupon_category_columns', array( $this, 'category_columns' ) );
            add_filter('manage_coupon_category_custom_column',  array( $this, 'category_fields' ), 10, 3);

            add_filter('manage_edit-coupon_store_columns', array( $this, 'store_columns' ) );
            add_filter('manage_coupon_store_custom_column',  array( $this, 'store_fields' ), 5, 3);

        }

        function category_columns( $columns ) {
            // add 'My Column'
            $columns['icon'] = __( 'Icon', 'halacoupon' );
            return $columns;
        }

        function category_fields( $term , $column_name, $term_id ){
            switch ( $column_name ) {
                case 'icon':
                    $icon = get_term_meta( $term_id , '_ags_icon', true );
                    if ( trim( $icon ) !== '' ){
                        echo '<span class="c-cat-icon"><i class="'.esc_attr( $icon ).'"></i></span>';
                    }
                    break;
            }
        }

        function store_columns( $columns ) {

            $new_columns = array();
            $new_columns['cb'] = $columns['cb'];
            $new_columns['thumb'] = __( 'Thumbnail', 'halacoupon' );
            $new_columns['name']  =  $columns['name'];
            $new_columns['posts'] =  $columns['posts'];
            $new_columns['url']   = __( 'URL', 'halacoupon' );
            $new_columns['out']   = __( 'Out', 'halacoupon' );
            $new_columns['feature']  = '<span class="dashicons dashicons-star-filled"></span>';
            return $new_columns;
        }

        function store_fields( $unknown , $column_name, $term_id ){
            $s = new halacoupon_Store( $term_id );
            switch ( $column_name ) {
                case 'thumb':
                    echo $s->get_thumbnail();
                    break;
                case 'feature':
                    if ( $s->is_featured() ){
                        echo '<span class="dashicons dashicons-star-filled"></span>';
                    } else {
                        echo '<span class="dashicons dashicons-star-empty"></span>';
                    }
                    break;
                case 'icon':
                    $icon = get_term_meta( $term_id , '_ags_icon', true );
                    if ( trim( $icon ) !== '' ){
                        echo '<span class="c-cat-icon"><i class="'.esc_attr( $icon ).'"></i></span>';
                    }
                    break;
                case 'out':
                    $out = get_term_meta( $term_id , '_ags_go_out', true );
                    echo intval( $out );
                    break;
                case 'url':
                    ?>
                    <div>
                        <span><?php esc_html_e( 'URL:', 'halacoupon' ); ?></span>
                        <?php  echo ( $s->_ags_store_url != '' ) ? '<a href="'.esc_url($s->_ags_store_url).'" title="'.esc_attr( $s->_ags_store_url ).'">'.esc_html($s->_ags_store_url).'</a>' : __( '[empty]', 'halacoupon' ); ?>
                    </div>
                    <div>
                        <span><?php esc_html_e( 'Aff:', 'halacoupon' ); ?></span>
                        <?php  echo ( $s->_ags_store_aff_url != '' ) ? '<a href="'.esc_url($s->_ags_store_aff_url).'" title="'.esc_attr( $s->_ags_store_aff_url ).'">'.esc_html($s->_ags_store_aff_url).'</a>' : __( '[empty]', 'halacoupon' ); ?>
                    </div>
                    <?php
                    break;
            }
        }

    }

    new halacoupon_Theme_Edit_Taxs_Columns();


    function halacoupon_filter_to_coupon_administration(){

        //execute only on the 'post' content type
        global $post_type;
        if($post_type == 'coupon'){

            $post_formats_args = array(
                //'show_option_all'   => 'All Categories',
                'show_option_none'  => 'All Categories',
                'option_none_value'  => '',
                'orderby'           => 'NAME',
                'order'             => 'ASC',
                'name'              => 'coupon_category',
                'taxonomy'          => 'coupon_category'
            );

            //if we have a post format already selected, ensure that its value is set to be selected
            if(isset($_GET['coupon_category'])){
                $post_formats_args['selected'] = sanitize_text_field($_GET['coupon_category']);
            }
            wp_dropdown_categories($post_formats_args);

            $types = halacoupon_get_coupon_types();

            $type = '';
            if(isset($_GET['c_type'])){
                $type = sanitize_text_field($_GET['c_type']);
            }
            ?>
            <select class="postform" id="coupon_type" name="c_type">
                <option value=""><?php esc_html_e( 'All coupon types', 'halacoupon' ); ?></option>
                <?php foreach ( $types as $k => $v ) { ?>
                <option <?php selected( $type, $k ); ?> value="<?php echo esc_attr( $k ); ?>"><?php echo esc_html( $v ); ?></option>
                <?php } ?>
            </select>
            <?php
            $s = '';
            if ( isset ( $_GET['c_s_store'] ) ) {
                $s = $_GET['c_s_store'];
            }
            ?>
            <input type="text" id="search-coupon-store" placeholder="<?php esc_attr_e( 'Filter store','halacoupon' ); ?>" value="<?php echo esc_attr( $s ); ?>" name="c_s_store">
            <?php

        }
    }
    add_action('restrict_manage_posts','halacoupon_filter_to_coupon_administration');

    function halacoupon_disable_months_dropdown( $r, $post_type ){
        if ( $post_type == 'coupon' ) {
            $r = true;
        }
        return $r;
    }

    add_filter( 'disable_months_dropdown', 'halacoupon_disable_months_dropdown', 15, 2 );


    /**
     * restrict the posts by the chosen post format
     * @param $query WP_Query
     */
    function halacoupon_add_filter_to_coupons( &$query){

        global $post_type, $pagenow;

        //if we are currently on the edit screen of the post type listings
        if($pagenow == 'edit.php' && $post_type == 'coupon'){
            if( isset( $_GET['coupon_category'] ) ){

                //get the desired post format
                $cat = sanitize_text_field($_GET['coupon_category']);
                //if the post format is not 0 (which means all)

                if( $cat != 0 ){
                    $query->query_vars['tax_query'] = array(
                        'relation' => 'AND',
                        array(
                            'taxonomy'  => 'coupon_category',
                            'field'     => 'term_id',
                            'terms'     => array( $cat )
                        )
                    );
                }
            }

            /**
             * Search coupon by coupon types
             * @see halacoupon_Search::where()
             */
            if ( isset( $_GET['c_type'] ) ) {
                $t = trim( ( string ) $_GET['c_type'] );

                if ( $t ) {
                    //$query->is_search  = false;
                    //unset( $query->query_vars['s']  );
                    $query->set('meta_query', array(
                        'relation' => 'AND',
                        array(
                            'key'     => '_ags_coupon_type',
                            'value'   => $t,
                            'compare' => 'LIKE',
                        )
                    ));
                }
            }

            // filter store
            if ( isset( $_GET['c_s_store'] ) ) {
                $s = trim( ( string ) $_GET['c_s_store'] );
                if ( $s ) {
                    if ( !isset( $_GET['coupon_category'] ) ) {
                        $query->query_vars['tax_query'] = array(
                            array(
                                'taxonomy'  => 'coupon_store',
                                'field'     => 'name',
                                'terms'     => array($s)
                            )
                        );
                    } else {
                        $query->query_vars['tax_query'][] = array(
                            'taxonomy'  => 'coupon_store',
                            'field'     => 'name',
                            'terms'     => array($s)
                        );
                    }

                }
            }

        }

    }
    add_action('pre_get_posts','halacoupon_add_filter_to_coupons');


} // end is admin




class halacoupon_Coupon_Admin {

    function __construct() {
        if ( is_admin() ) {
            /**
             * Add more custom column for coupon post type
             */
            add_filter('manage_coupon_posts_columns', array($this, 'custom_edit_coupon_columns'));
            add_action('manage_coupon_posts_custom_column', array($this, 'custom_coupon_column'), 10, 2);
            add_filter( 'manage_edit-coupon_sortable_columns', array($this, 'sortable_columns'));
            add_action( 'pre_get_posts', array($this, 'column_orderby'));

        }

        add_action( "wp_insert_post",  array( __CLASS__, 'update_store_data' ), 96, 3 );
        add_action( "wp_insert_post",  array( __CLASS__, 'update_store_count' ), 97, 2 );

        add_action( "after_frontend_coupon_submitted",  array( __CLASS__, 'update_store_data' ), 96, 3 );
        add_action( "after_frontend_coupon_submitted",  array( __CLASS__, 'update_store_count' ), 97, 2 );

        add_action( 'before_delete_post', array( $this, 'delete_coupon' ) );

    }

    function column_orderby( $query ) {
        if( ! is_admin() ) {
            return;
        }

        if ( $query->get( 'post_type' ) != 'coupon' ) {
            return ;
        }

        $orderby = $query->get( 'orderby');


        if( 'stats' == $orderby ) {
            $query->set('meta_key','_ags_used');
            $query->set('orderby','meta_value_num');
        }


    }

    function sortable_columns( $columns ) {
        $columns['stats']   = 'stats';

        //To make a column 'un-sortable' remove it from the array
        //unset($columns['date']);

        return $columns;
    }


    /**
     * Add more coupon columns
     * @since 1.0.0
     * @param $columns
     * @return mixed
     */
    function custom_edit_coupon_columns($columns) {

        $columns['coupon_type'] = esc_html__( 'Coupon', 'halacoupon' );
        $columns['stats']       = esc_html__( 'Clicks', 'halacoupon' );




        if ( isset( $columns['date']  ) ) {
            $title  =  $columns['date'];
            unset( $columns['date'] );
            $columns['date']  = $title;
        }

        return $columns;
    }


    /**
     * Display coupon column data
     *
     * @since 1.0.0
     * @param $column
     * @param $post_id
     */
    function custom_coupon_column( $column, $post_id ) {
        halacoupon_setup_coupon( $post_id );
        switch ( $column ) {
            case 'coupon_type' :

                if ( halacoupon_coupon()->get_type() == 'code' ) {
                    if (  $code = halacoupon_coupon()->get_code()  ) {
                        echo '<br/><code>'.esc_html( $code ).'</code>';
                    } else {
                        echo '<br/>'; esc_html_e( '[No Code]', 'halacoupon' );
                    }
                } else {
                    echo strtoupper( halacoupon_coupon()->get_coupon_type_text() );
                }
                break;
            case 'stats' :
                echo '<span title="'.esc_attr__( 'Total Used' ,'halacoupon' ).'" ><span class="dashicons dashicons-migrate"></span>' . halacoupon_coupon()->get_total_used() .'</span>';
                break;

        }

    }


    public static function update_store_count( $post_ID , $post = null ){
        // Update coupon type count
        if ( ! $post ) {
            $post = get_post( $post_ID );
        }

        $post_type = get_post_type( $post ) ;
        if ( $post_type != 'coupon' ) {
            return ;
        }

        $types = apply_filters( 'store_count_coupons_types', array(
            'code',
            'sale',
        ) );

        $ids =  false;
        $stores =  get_the_terms( $post_ID, 'coupon_store' );

        if ( $stores ) {
            $ids = wp_list_pluck( $stores, 'term_id');

            foreach ( $ids as $id ) {

                foreach ( $types as $c_type ) {
                    $args = array(
                        'post_type' => 'coupon',
                        'meta_key' => '_ags_coupon_type',
                        'meta_value' => $c_type,
                        'meta_compare' => '=',
                        'tax_query' => array(
                            'relation' => 'AND',
                            array(
                                'taxonomy' => 'coupon_store',
                                'field' => 'term_id',
                                'terms' => array($id),
                            ),
                        ),
                    );
                    $query = new WP_Query($args);
                    update_term_meta($id, '_ags_coupon_' . $c_type, $query->found_posts);
                }
            }

        }

        update_post_meta( $post_ID, '_ags_store', $ids );
    }

    function delete_coupon( $post_id ){
        // Update coupon type count
        $post = get_post( $post_id );
        $post_type = get_post_type( $post ) ;
        if ( $post_type != 'coupon' ) {
            return ;
        }

        $c_type = get_post_meta( $post->ID, '_ags_coupon_type',  true );
        if ( ! $c_type ) {
            $c_type = 'code';
        }

        $stores =  update_post_meta(  $post->ID, '_ags_store', true );
        if ( is_array( $stores ) ) {
            foreach ( $stores as $id ) {
                $n = intval( get_term_meta( $id, '_ags_coupon_' . $c_type, true ) );
                if ( $n > 0 ) {
                    $n-=1;
                    update_term_meta( $id, '_ags_coupon_' . $c_type, $n );
                }
            }
        }
    }


    /**
     * Update coupon count for store
     *
     * @param $post_ID
     * @param $post
     * @param null $update
     */
    public static function update_store_data( $post_ID, $post = null, $update = null  ){

        // Get post if not exists
        if ( ! $post ) {
            $post = get_post( $post_ID );
        }

        if ( 'coupon' != get_post_type( $post ) ) {
            return ;
        }

        /**
         * Update tracking data
         */
        $percent = get_post_meta( $post_ID, '_ags_percent_success', true );
        if ( empty( $percent ) ||  $percent == '' ){
            update_post_meta( $post_ID, '_ags_percent_success', 100 );
        }

        $meta = get_post_custom( $post_ID );

        if ( ! isset ( $meta[ '_ags_used' ] ) ) {
            update_post_meta( $post_ID, '_ags_used', 0 );
        }

        if ( ! isset ( $meta[ '_ags_today' ] ) ) {
            update_post_meta( $post_ID, '_ags_today', '' );
        }

        if ( ! isset ( $meta[ '_ags_vote_up' ] ) ) {
            update_post_meta( $post_ID, '_ags_vote_up', 0 );
        }

        if ( ! isset ( $meta[ '_ags_vote_down' ] ) ) {
            update_post_meta( $post_ID, '_ags_vote_down', 0 );
        }

        if ( ! isset ( $meta[ '_ags_expires' ] ) ) {
            update_post_meta( $post_ID, '_ags_expires', '' );
        }

    }

}

new halacoupon_Coupon_Admin();

/**
 * Review Articles Admin Management
 */
class halacoupon_Review_Admin {

    function __construct() {
        if ( is_admin() ) {
            // Add custom columns for review articles
            add_filter('manage_review_posts_columns', array($this, 'custom_review_columns'));
            add_action('manage_review_posts_custom_column', array($this, 'custom_review_column'), 10, 2);
            add_filter('manage_edit-review_sortable_columns', array($this, 'sortable_review_columns'));
            add_action('pre_get_posts', array($this, 'review_column_orderby'));

            // Add admin filters
            add_action('restrict_manage_posts', array($this, 'review_admin_filters'));
            add_action('pre_get_posts', array($this, 'filter_reviews_by_meta'));
        }
    }

    /**
     * Add custom columns for review articles
     */
    function custom_review_columns($columns) {
        $new_columns = array();
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = $columns['title'];
        $new_columns['article_rating'] = esc_html__('Article Rating', 'halacoupon');
        $new_columns['total_ratings'] = esc_html__('Total Ratings', 'halacoupon');
        $new_columns['associated_store'] = esc_html__('Store', 'halacoupon');
        $new_columns['taxonomy'] = esc_html__('Category', 'halacoupon');
        $new_columns['author'] = $columns['author'];
        $new_columns['date'] = $columns['date'];

        return $new_columns;
    }

    /**
     * Display custom column data
     */
    function custom_review_column($column, $post_id) {
        switch ($column) {
            case 'article_rating':
                $rating = get_post_meta($post_id, '_halacoupon_article_average_rating', true) ?: 0;
                $rating_count = get_post_meta($post_id, '_halacoupon_article_total_ratings', true) ?: 0;

                if ($rating_count > 0) {
                    echo '<div class="rating-display">';
                    for ($i = 1; $i <= 5; $i++) {
                        $star_class = $i <= $rating ? '★' : '☆';
                        echo '<span style="color: #EBBF43;">' . $star_class . '</span>';
                    }
                    echo '<br><small>' . number_format($rating, 1) . '/5</small>';
                    echo '</div>';
                } else {
                    echo '<span style="color: #666;">—</span>';
                }
                break;

            case 'total_ratings':
                $total = get_post_meta($post_id, '_halacoupon_article_total_ratings', true) ?: 0;
                echo '<strong>' . intval($total) . '</strong>';
                break;

            case 'associated_store':
                $store_id = get_post_meta($post_id, '_halacoupon_review_store_id', true);
                if ($store_id) {
                    $store = get_term($store_id, 'coupon_store');
                    if ($store && !is_wp_error($store)) {
                        echo '<a href="' . esc_url(get_term_link($store)) . '" target="_blank">';
                        echo esc_html($store->name);
                        echo '</a>';
                    } else {
                        echo '<span style="color: #666;">—</span>';
                    }
                } else {
                    echo '<span style="color: #666;">—</span>';
                }
                break;

            case 'taxonomy':
                $terms = get_the_terms($post_id, 'review_category');
                if ($terms && !is_wp_error($terms)) {
                    $term_names = array();
                    foreach ($terms as $term) {
                        $term_names[] = $term->name;
                    }
                    echo esc_html(implode(', ', $term_names));
                } else {
                    echo '<span style="color: #666;">—</span>';
                }
                break;
        }
    }

    /**
     * Make columns sortable
     */
    function sortable_review_columns($columns) {
        $columns['article_rating'] = 'article_rating';
        $columns['total_ratings'] = 'total_ratings';
        return $columns;
    }

    /**
     * Handle column sorting
     */
    function review_column_orderby($query) {
        if (!is_admin() || !$query->is_main_query()) {
            return;
        }

        if ($query->get('post_type') !== 'review') {
            return;
        }

        $orderby = $query->get('orderby');

        if ('article_rating' === $orderby) {
            $query->set('meta_key', '_halacoupon_article_average_rating');
            $query->set('orderby', 'meta_value_num');
        } elseif ('total_ratings' === $orderby) {
            $query->set('meta_key', '_halacoupon_article_total_ratings');
            $query->set('orderby', 'meta_value_num');
        }
    }

    /**
     * Add admin filters
     */
    function review_admin_filters() {
        global $post_type;

        if ($post_type === 'review') {
            // Store filter
            $stores = get_terms(array(
                'taxonomy' => 'coupon_store',
                'hide_empty' => false,
            ));

            if (!empty($stores) && !is_wp_error($stores)) {
                $selected_store = isset($_GET['filter_store']) ? $_GET['filter_store'] : '';
                echo '<select name="filter_store">';
                echo '<option value="">' . esc_html__('All Stores', 'halacoupon') . '</option>';
                foreach ($stores as $store) {
                    echo '<option value="' . esc_attr($store->term_id) . '" ' . selected($selected_store, $store->term_id, false) . '>';
                    echo esc_html($store->name);
                    echo '</option>';
                }
                echo '</select>';
            }

            // Rating filter
            $selected_rating = isset($_GET['filter_rating']) ? $_GET['filter_rating'] : '';
            echo '<select name="filter_rating">';
            echo '<option value="">' . esc_html__('All Ratings', 'halacoupon') . '</option>';
            echo '<option value="5" ' . selected($selected_rating, '5', false) . '>' . esc_html__('5 Stars', 'halacoupon') . '</option>';
            echo '<option value="4" ' . selected($selected_rating, '4', false) . '>' . esc_html__('4+ Stars', 'halacoupon') . '</option>';
            echo '<option value="3" ' . selected($selected_rating, '3', false) . '>' . esc_html__('3+ Stars', 'halacoupon') . '</option>';
            echo '<option value="2" ' . selected($selected_rating, '2', false) . '>' . esc_html__('2+ Stars', 'halacoupon') . '</option>';
            echo '<option value="1" ' . selected($selected_rating, '1', false) . '>' . esc_html__('1+ Stars', 'halacoupon') . '</option>';
            echo '<option value="0" ' . selected($selected_rating, '0', false) . '>' . esc_html__('No Ratings', 'halacoupon') . '</option>';
            echo '</select>';
        }
    }

    /**
     * Filter reviews by meta values
     */
    function filter_reviews_by_meta($query) {
        global $post_type, $pagenow;

        if ($pagenow === 'edit.php' && $post_type === 'review' && $query->is_main_query()) {
            $meta_query = array();

            // Filter by store
            if (isset($_GET['filter_store']) && !empty($_GET['filter_store'])) {
                $meta_query[] = array(
                    'key' => '_halacoupon_review_store_id',
                    'value' => intval($_GET['filter_store']),
                    'compare' => '='
                );
            }

            // Filter by rating
            if (isset($_GET['filter_rating']) && $_GET['filter_rating'] !== '') {
                $rating_filter = intval($_GET['filter_rating']);
                if ($rating_filter === 0) {
                    // No ratings
                    $meta_query[] = array(
                        'relation' => 'OR',
                        array(
                            'key' => '_halacoupon_article_total_ratings',
                            'compare' => 'NOT EXISTS'
                        ),
                        array(
                            'key' => '_halacoupon_article_total_ratings',
                            'value' => 0,
                            'compare' => '='
                        )
                    );
                } else {
                    // Has ratings >= filter value
                    $meta_query[] = array(
                        'key' => '_halacoupon_article_average_rating',
                        'value' => $rating_filter,
                        'compare' => '>=',
                        'type' => 'DECIMAL'
                    );
                }
            }

            if (!empty($meta_query)) {
                $query->set('meta_query', $meta_query);
            }
        }
    }
}

new halacoupon_Review_Admin();


/**
 *
 * Open comment for single coupon
 *
 * @param $open
 * @param $post_id
 * @return bool
 */
function halacoupon_open_coupon_comments( $open, $post_id ) {
    if ( get_post_type( $post_id ) == 'coupon' ) {
        $open = true;
    }
    return $open;
}

add_filter( 'comments_open', 'halacoupon_open_coupon_comments', 25, 2 );


/**
 * Change coupon permalink
 */
add_filter( 'post_type_link', 'halacoupon_coupon_link', 85, 3 );
function halacoupon_coupon_link( $permalink, $post, $leavename = null ){
    if ( 'coupon' != get_post_type( $post ) ) {
        return $permalink;
    }

    $c = new halacoupon_Coupon( $post );
    global $wp_rewrite;
    if ( $leavename ) {
        if (  $wp_rewrite->using_permalinks() ){
            $store_url = $c->get_store_url();
            if ( $store_url  ) {
                return trailingslashit( $store_url ).'%coupon%/';
            }

        }
        return $permalink;
    }

    if ( ! $wp_rewrite->using_permalinks() ){
        return $permalink;
    }


    return $c->get_href();
}
