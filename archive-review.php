<?php
/**
 * Archive Review Articles Template
 *
 * Review articles archive page with modern grid layout and filtering options
 * Features glass morphism design, brand colors, and comprehensive RTL/LTR support
 *
 * @package HalaCoupon
 * @version 2.0.0 - Review Articles Archive Design
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

get_header();

// Get current language direction for RTL/LTR support
$is_rtl = is_rtl();

// Get archive statistics
$total_articles = wp_count_posts('review')->publish;
$average_article_rating = 0;
$total_article_ratings = 0;

// Calculate overall article rating statistics
$all_articles = get_posts(array(
    'post_type' => 'review',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'fields' => 'ids'
));

if (!empty($all_articles)) {
    $total_rating_sum = 0;
    $articles_with_ratings = 0;

    foreach ($all_articles as $article_id) {
        $article_rating = floatval(get_post_meta($article_id, '_halacoupon_article_average_rating', true));
        $article_rating_count = intval(get_post_meta($article_id, '_halacoupon_article_total_ratings', true));

        if ($article_rating > 0 && $article_rating_count > 0) {
            $total_rating_sum += $article_rating;
            $total_article_ratings += $article_rating_count;
            $articles_with_ratings++;
        }
    }

    $average_article_rating = $articles_with_ratings > 0 ? $total_rating_sum / $articles_with_ratings : 0;
}

?>

<div class="min-h-screen bg-background" dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">

    <section class="relative py-16 sm:py-20 lg:py-24 overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div class="absolute hidden lg:block inset-0 pointer-events-none z-0">
            <div class="absolute top-20 left-20 w-48 h-48 bg-gradient-secondary rounded-full filter blur-3xl opacity-20 animate-float"></div>
            <div class="absolute bottom-20 right-20 w-40 h-40 bg-gradient-primary rounded-full filter blur-2xl opacity-15 animate-float-slow"></div>
            <div class="absolute top-1/3 right-1/4 w-32 h-32 bg-gradient-accent rounded-full filter blur-xl opacity-10 animate-float-fast"></div>
            <!-- Additional glass blur elements -->
            <div class="absolute inset-0 bg-gradient-to-br from-black/50 via-transparent to-black/30 backdrop-blur-sm"></div>
        </div>

        <div class="relative max-w-5xl mx-auto px-6 sm:px-8 z-10">
            <div class="flex justify-center mb-8">
                <span class="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-secondary to-primary shadow-glow text-white font-bold text-sm backdrop-blur-xl border-2 border-white/30 hover:border-white/50 transition-all duration-300 transform hover:scale-105">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <?php esc_html_e('Review Articles', 'halacoupon'); ?>
                </span>
            </div>

            <h1 class="text-center text-5xl sm:text-6xl lg:text-7xl font-black font-rubik mb-8 leading-tight drop-shadow-2xl">
                <span class="bg-gradient-to-r from-white via-yellow-200 to-white bg-clip-text text-transparent">
                    <?php esc_html_e('Review Articles & Insights', 'halacoupon'); ?>
                </span>
            </h1>

            <p class="text-center text-xl sm:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-12 font-medium">
                <?php esc_html_e('Expert reviews and insights about stores, products, and shopping experiences.', 'halacoupon'); ?>
            </p>


            <div class="flex justify-center">
                <span class="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-black/60 backdrop-blur-xl text-white font-medium text-sm border-2 border-white/20 shadow-soft hover:bg-black/70 hover:border-white/30 transition-all duration-300">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <?php esc_html_e('Expert Reviews & Insights', 'halacoupon'); ?>
                </span>
            </div>
        </div>
    </section>

    <main class="relative py-12 md:py-20">
        <div class="container mx-auto px-4">
            
            <!-- Review Articles Display -->
            <section>
                <div class="max-w-7xl mx-auto">

                    <?php
                    // Display review articles using the simplified reviews function
                    halacoupon_display_reviews(array(
                        'posts_per_page' => 12,
                        'show_filters' => false, // No rating filters
                        'show_pagination' => true,
                        'layout' => 'grid',
                        'columns' => 3,
                    ));
                    ?>

                </div>
            </section>

        </div>
    </main>

</div>

<!-- Enhanced Animations -->
<style>
/* Floating animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes float-slow {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

@keyframes float-fast {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-25px); }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-float-slow {
    animation: float-slow 8s ease-in-out infinite;
}

.animate-float-fast {
    animation: float-fast 4s ease-in-out infinite;
}

/* Fade in animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

/* Line clamp utilities */
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Pagination Styles */
.reviews-pagination {
    margin-top: 3rem;
}

.reviews-pagination .flex {
    justify-content: center;
}

.reviews-pagination a,
.reviews-pagination div {
    transition: all 0.3s ease;
}

.reviews-pagination a:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* RTL Pagination Support */
.reviews-pagination .rtl {
    direction: rtl;
}

.reviews-pagination .rtl .inline-flex {
    flex-direction: row-reverse;
}

.reviews-pagination .rtl svg {
    transform: scaleX(-1);
}

/* RTL-aware spacing */
.reviews-pagination .rtl .mr-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
}

.reviews-pagination .rtl .ml-2 {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
}

/* RTL border radius adjustments */
.reviews-pagination .rtl .rounded-l-xl {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    border-top-right-radius: 0.75rem !important;
    border-bottom-right-radius: 0.75rem !important;
}

.reviews-pagination .rtl .rounded-r-xl {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-top-left-radius: 0.75rem !important;
    border-bottom-left-radius: 0.75rem !important;
}

/* Article Card Enhancements */
.article-card {
    transition: all 0.5s ease;
}

.article-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Loading State */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<?php
get_footer();
?>
