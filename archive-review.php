<?php
/**
 * Archive Review Articles Template
 *
 * Review articles archive page with modern grid layout and filtering options
 * Features glass morphism design, brand colors, and comprehensive RTL/LTR support
 *
 * @package HalaCoupon
 * @version 2.0.0 - Review Articles Archive Design
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

get_header();

// Get current language direction for RTL/LTR support
$is_rtl = is_rtl();

// Get archive statistics
$total_articles = wp_count_posts('review')->publish;
$average_article_rating = 0;
$total_article_ratings = 0;

// Calculate overall article rating statistics
$all_articles = get_posts(array(
    'post_type' => 'review',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'fields' => 'ids'
));

if (!empty($all_articles)) {
    $total_rating_sum = 0;
    $articles_with_ratings = 0;

    foreach ($all_articles as $article_id) {
        $article_rating = floatval(get_post_meta($article_id, '_halacoupon_article_average_rating', true));
        $article_rating_count = intval(get_post_meta($article_id, '_halacoupon_article_total_ratings', true));

        if ($article_rating > 0 && $article_rating_count > 0) {
            $total_rating_sum += $article_rating;
            $total_article_ratings += $article_rating_count;
            $articles_with_ratings++;
        }
    }

    $average_article_rating = $articles_with_ratings > 0 ? $total_rating_sum / $articles_with_ratings : 0;
}

?>

<!-- Review Articles Archive Page with Modern Design -->
<div class="min-h-screen bg-background" dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">

    <!-- Enhanced Hero Section with Dark Glass Background & Review Content -->
    <section class="relative py-16 sm:py-20 lg:py-24 overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <!-- Enhanced floating elements with better positioning -->
        <div class="absolute inset-0 pointer-events-none z-0">
            <div class="absolute top-20 left-20 w-48 h-48 bg-gradient-secondary rounded-full filter blur-3xl opacity-20 animate-float"></div>
            <div class="absolute bottom-20 right-20 w-40 h-40 bg-gradient-primary rounded-full filter blur-2xl opacity-15 animate-float-slow"></div>
            <div class="absolute top-1/3 right-1/4 w-32 h-32 bg-gradient-accent rounded-full filter blur-xl opacity-10 animate-float-fast"></div>
            <!-- Additional glass blur elements -->
            <div class="absolute inset-0 bg-gradient-to-br from-black/50 via-transparent to-black/30 backdrop-blur-sm"></div>
        </div>

        <div class="relative max-w-5xl mx-auto px-6 sm:px-8 z-10">
            <!-- Enhanced Brand Badge with better contrast -->
            <div class="flex justify-center mb-8">
                <span class="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-secondary to-primary shadow-glow text-white font-bold text-sm backdrop-blur-xl border-2 border-white/30 hover:border-white/50 transition-all duration-300 transform hover:scale-105">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <?php esc_html_e('Review Articles', 'halacoupon'); ?>
                </span>
            </div>

            <!-- Fixed H1 Title with proper gradient text -->
            <h1 class="text-center text-5xl sm:text-6xl lg:text-7xl font-black font-rubik mb-8 leading-tight drop-shadow-2xl">
                <span class="bg-gradient-to-r from-white via-yellow-200 to-white bg-clip-text text-transparent">
                    <?php esc_html_e('Review Articles & Insights', 'halacoupon'); ?>
                </span>
            </h1>

            <!-- Enhanced Subtitle with better styling -->
            <p class="text-center text-xl sm:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-12 font-medium">
                <?php esc_html_e('Expert reviews and insights about stores, products, and shopping experiences.', 'halacoupon'); ?>
            </p>

            <!-- Enhanced Statistics with better contrast and layout -->
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-8 mb-8 max-w-4xl mx-auto">
                
                <!-- Enhanced Average Rating Card -->
                <?php if ($total_article_ratings > 0) : ?>
                <div class="group bg-white/10 backdrop-blur-2xl rounded-3xl shadow-float-lg border-2 border-white/20 p-8 hover:bg-white/15 hover:border-white/30 transition-all duration-500 transform hover:scale-105 hover:shadow-glow">
                    <div class="text-center">
                        <div class="flex items-center justify-center gap-2 mb-4">
                            <div class="flex items-center gap-1">
                                <?php for ($i = 1; $i <= 5; $i++) : ?>
                                <svg class="w-6 h-6 <?php echo $i <= $average_article_rating ? 'text-yellow-400' : 'text-white/30'; ?>" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                <?php endfor; ?>
                            </div>
                            <span class="text-3xl font-black text-white"><?php echo number_format($average_article_rating, 1); ?></span>
                        </div>
                        <div class="text-sm font-semibold text-white/90 uppercase tracking-wide"><?php esc_html_e('Average Rating', 'halacoupon'); ?></div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Enhanced Total Articles Card -->
                <div class="group bg-white/10 backdrop-blur-2xl rounded-3xl shadow-float-lg border-2 border-white/20 p-8 hover:bg-white/15 hover:border-white/30 transition-all duration-500 transform hover:scale-105 hover:shadow-glow">
                    <div class="text-center">
                        <div class="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-primary to-secondary rounded-2xl shadow-glow group-hover:shadow-glow-secondary transition-all duration-300 mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="text-3xl font-black text-white mb-1"><?php echo number_format($total_articles); ?></div>
                        <div class="text-sm font-semibold text-white/90 uppercase tracking-wide"><?php esc_html_e('Review Articles', 'halacoupon'); ?></div>
                    </div>
                </div>

                <!-- Enhanced Total Ratings Card -->
                <?php if ($total_article_ratings > 0) : ?>
                <div class="group bg-white/10 backdrop-blur-2xl rounded-3xl shadow-float-lg border-2 border-white/20 p-8 hover:bg-white/15 hover:border-white/30 transition-all duration-500 transform hover:scale-105 hover:shadow-glow-secondary">
                    <div class="text-center">
                        <div class="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-secondary to-accent rounded-2xl shadow-glow-secondary group-hover:shadow-glow transition-all duration-300 mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                            </svg>
                        </div>
                        <div class="text-3xl font-black text-white mb-1"><?php echo number_format($total_article_ratings); ?></div>
                        <div class="text-sm font-semibold text-white/90 uppercase tracking-wide"><?php esc_html_e('Total Ratings', 'halacoupon'); ?></div>
                    </div>
                </div>
                <?php endif; ?>

            </div>

            <!-- Enhanced Date Badge with better styling -->
            <div class="flex justify-center">
                <span class="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-black/60 backdrop-blur-xl text-white font-medium text-sm border-2 border-white/20 shadow-soft hover:bg-black/70 hover:border-white/30 transition-all duration-300">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <?php esc_html_e('Expert Reviews & Insights', 'halacoupon'); ?>
                </span>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="relative py-12 md:py-20">
        <div class="container mx-auto px-4">
            
            <!-- Review Articles Display -->
            <section>
                <div class="max-w-7xl mx-auto">

                    <?php
                    // Display review articles using the simplified reviews function
                    halacoupon_display_reviews(array(
                        'posts_per_page' => 12,
                        'show_filters' => false, // No rating filters
                        'show_pagination' => true,
                        'layout' => 'grid',
                        'columns' => 3,
                    ));
                    ?>

                </div>
            </section>

        </div>
    </main>

</div>

<!-- Enhanced Animations -->
<style>
/* Floating animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes float-slow {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

@keyframes float-fast {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-25px); }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-float-slow {
    animation: float-slow 8s ease-in-out infinite;
}

.animate-float-fast {
    animation: float-fast 4s ease-in-out infinite;
}

/* Fade in animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

/* Line clamp utilities */
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Pagination Styles */
.reviews-pagination {
    margin-top: 3rem;
}

.reviews-pagination .flex {
    justify-content: center;
}

.reviews-pagination a,
.reviews-pagination div {
    transition: all 0.3s ease;
}

.reviews-pagination a:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* RTL Pagination Support */
.reviews-pagination .rtl {
    direction: rtl;
}

.reviews-pagination .rtl .inline-flex {
    flex-direction: row-reverse;
}

.reviews-pagination .rtl svg {
    transform: scaleX(-1);
}

/* RTL-aware spacing */
.reviews-pagination .rtl .mr-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
}

.reviews-pagination .rtl .ml-2 {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
}

/* RTL border radius adjustments */
.reviews-pagination .rtl .rounded-l-xl {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    border-top-right-radius: 0.75rem !important;
    border-bottom-right-radius: 0.75rem !important;
}

.reviews-pagination .rtl .rounded-r-xl {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-top-left-radius: 0.75rem !important;
    border-bottom-left-radius: 0.75rem !important;
}

/* Article Card Enhancements */
.article-card {
    transition: all 0.5s ease;
}

.article-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Loading State */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<?php
get_footer();
?>
