<?php
/**
 * Store Taxonomy Page - Modern Creative Design
 *
 * A beautiful, modern store page featuring:
 * - Creative hero section with brand gradients
 * - Interactive statistics cards
 * - Smooth animations and hover effects
 * - Brand color integration
 * - Mobile-first responsive design
 */

get_header();

$term = get_queried_object();

// Setup store with error handling
if (function_exists('halacoupon_setup_store') && function_exists('halacoupon_store')) {
	halacoupon_setup_store( $term );
	$current_link = get_permalink( $term );
	$store_name = halacoupon_store()->name;
} else {
	$current_link = get_term_link( $term );
	$store_name = $term->name;
}
$term_id = get_queried_object_id();

// Get coupon counts with error handling
if (function_exists('halacoupon_get_coupon_counts_by_type')) {
	$coupon_counts = halacoupon_get_coupon_counts_by_type($term_id,'coupon_store');
} else {
	// Fallback counts
	$coupon_counts = array(
		'code' => 0,
		'sale' => 0,
		'all' => 0
	);
}

?>

<!-- Modern Creative Store Page -->
<div id="content-wrap" class="bg-background">

	<div id="primary" class="content-area">
		<main id="main" class="site-main" role="main">

			<!-- Modern Glass Morphism Store Header -->
			<section class="relative overflow-hidden bg-black/60 backdrop-blur-xl">
				<!-- Enhanced Floating Decorative Elements -->
				<div class="absolute inset-0 overflow-hidden pointer-events-none">
					<!-- Primary floating circles -->
					<div class="absolute top-10 left-10 w-32 h-32 bg-primary/20 rounded-full blur-xl animate-float"></div>
					<div class="absolute top-20 right-20 w-24 h-24 bg-secondary/20 rounded-full blur-lg animate-float-slow" style="animation-delay: 2s;"></div>
					<div class="absolute bottom-16 left-1/4 w-20 h-20 bg-primary/15 rounded-full blur-md animate-float-fast" style="animation-delay: 4s;"></div>
					<div class="absolute top-1/3 right-1/3 w-16 h-16 bg-secondary/15 rounded-full blur-sm animate-float" style="animation-delay: 6s;"></div>

					<!-- Additional decorative shapes -->
					<div class="absolute bottom-10 right-10 w-28 h-28 bg-primary/10 rounded-full blur-2xl animate-float-slow" style="animation-delay: 1s;"></div>
					<div class="absolute top-1/2 left-1/6 w-12 h-12 bg-secondary/25 rounded-full blur-md animate-float-fast" style="animation-delay: 3s;"></div>
				</div>

				<!-- Glass overlay for enhanced depth -->
				<div class="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>

				<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">

					<!-- Compact Store Information -->
					<div class="flex flex-col sm:flex-row items-center gap-4 sm:gap-6">

						<!-- Enhanced Store Logo with Glass Morphism -->
						<div class="relative group">
							<!-- Enhanced glow effect -->
							<div class="absolute -inset-2 bg-gradient-to-r from-primary/30 to-secondary/30 rounded-2xl blur-lg opacity-50 group-hover:opacity-80 transition-all duration-500"></div>

							<!-- Glass container -->
							<div class="relative store-page-logo bg-white/10 backdrop-blur-sm rounded-2xl p-2 border border-white/20 group-hover:bg-white/20 transition-all duration-300">
								<a rel="nofollow"
									class="relative block w-20 h-20 sm:w-24 sm:h-24 rounded-xl overflow-hidden border-2 border-white/50 shadow-soft group-hover:scale-105 group-hover:border-white/80 transition-all duration-300"
									target="_blank"
									title="<?php esc_attr_e( 'Visit Store: ', 'halacoupon' ); echo halacoupon_store()->get_display_name(); ?>"
									href="<?php echo halacoupon_store()->get_go_store_url(); ?>">
									<?php echo halacoupon_store()->get_thumbnail(); ?>
								</a>
							</div>

							<!-- Enhanced Hover Tooltip with Glass Effect -->
							<div class="absolute -bottom-10 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-10">
								<div class="bg-white/90 backdrop-blur-sm text-text border border-white/30 text-xs font-semibold px-4 py-2 rounded-xl shadow-lg whitespace-nowrap">
									<?php esc_html_e( 'Visit Store', 'halacoupon' ); ?>
									<div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1 w-2 h-2 bg-white/90 rotate-45 border-l border-t border-white/30"></div>
								</div>
							</div>
						</div>

						<!-- Store Info - Compact -->
						<div class="flex-1">
							<!-- Store Title - Minimized -->
							<h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-2">
								<?php echo halacoupon_store()->get_single_store_name(); ?>
							</h1>

							<!-- Enhanced Store Rating with Glass Effect -->
							<?php if( halacoupon_display_star_rating($term_id) ) : ?>
							<div class="flex items-center justify-center sm:justify-start mb-4">
								<div class="bg-white/15 backdrop-blur-md rounded-xl px-4 py-2 border border-white/30 shadow-soft hover:bg-white/25 transition-all duration-300">
									<?php halacoupon_display_star_rating($term_id); ?>
								</div>
							</div>
							<?php endif; ?>


						</div>
					</div>

					<!-- Clean Filter Bar -->
					<div class="border-t border-white/20 pt-6 mt-6">
						<div class="flex flex-wrap justify-start gap-3" id="coupon-type-filters">
									<!-- All Deals Filter - Active State -->
									<button type="button"
										class="filter-btn active group relative overflow-hidden px-6 py-3 bg-primary text-white font-semibold rounded-xl shadow-soft hover:shadow-medium transform hover:scale-105 transition-all duration-300"
										data-filter="all">
										<span class="relative z-10 flex items-center">
											<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
											</svg>
											<?php esc_html_e('All Deals', 'halacoupon'); ?>
											<span class="ml-2 bg-white/30 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium" id="count-all">0</span>
										</span>
									</button>

									<!-- Coupons Filter - Glass Morphism -->
									<button type="button"
										class="filter-btn group relative overflow-hidden px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 text-white font-semibold rounded-xl shadow-soft hover:bg-white/20 hover:shadow-medium transform hover:scale-105 transition-all duration-300"
										data-filter="code">
										<span class="relative z-10 flex items-center">
											<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
											</svg>
											<?php esc_html_e('Coupons', 'halacoupon'); ?>
											<span class="ml-2 bg-primary/30 backdrop-blur-sm text-white px-2 py-1 rounded-full text-xs font-medium" id="count-code">0</span>
										</span>
									</button>

									<!-- Offers Filter - Glass Morphism -->
									<button type="button"
										class="filter-btn group relative overflow-hidden px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 text-white font-semibold rounded-xl shadow-soft hover:bg-white/20 hover:shadow-medium transform hover:scale-105 transition-all duration-300"
										data-filter="sale">
										<span class="relative z-10 flex items-center">
											<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
											</svg>
											<?php esc_html_e('Offers', 'halacoupon'); ?>
											<span class="ml-2 bg-secondary/30 backdrop-blur-sm text-white px-2 py-1 rounded-full text-xs font-medium" id="count-sale">0</span>
										</span>
									</button>
								</div>
				</div>
			</section>
							
						</div>
					</div>
				</div>
			</section>


					</div>
				</div>
			</section>

			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
				<div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
					<!-- Main Content -->
					<section id="store-content" class="lg:col-span-3">
						<!-- Coupon Listings -->
						<div id="coupon-listings-store" class="space-y-6">
							<?php
							$store_id = get_queried_object_id();

							if (function_exists('halacoupon_get_coupon_query_args')) {
								$args = halacoupon_get_coupon_query_args($store_id);
							} else {
								// Fallback query args
								$args = array(
									'post_type' => 'coupon',
									'post_status' => 'publish',
									'posts_per_page' => 20,
									'tax_query' => array(
										array(
											'taxonomy' => 'coupon_store',
											'field'    => 'term_id',
											'terms'    => $store_id,
										),
									),
								);
							}

							$coupons = new WP_Query($args);

							if ($coupons->have_posts()) {
								while ($coupons->have_posts()) {
									$coupons->the_post();
									halacoupon_setup_coupon();
									get_template_part('loop/loop-coupon');
								}
							} else {
								echo '<div class="relative group">';
								echo '<div class="absolute -inset-1 bg-gradient-accent rounded-3xl blur opacity-20 group-hover:opacity-30 transition duration-300"></div>';
								echo '<div class="relative bg-white rounded-3xl shadow-soft border border-accent-100 p-12 text-center">';
								echo '<div class="max-w-md mx-auto">';
								echo '<div class="w-24 h-24 bg-gradient-to-r from-accent-100 to-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">';
								echo '<svg class="w-12 h-12 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
								echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>';
								echo '</svg>';
								echo '</div>';
								echo '<h3 class="text-2xl font-bold text-text mb-4">' . esc_html__('No deals available right now', 'halacoupon') . '</h3>';
								echo '<p class="text-text/70 text-lg leading-relaxed mb-6">' . esc_html__('We\'re working hard to bring you the best deals. Check back soon for amazing offers!', 'halacoupon') . '</p>';
								echo '<div class="flex justify-center">';
								echo '<a href="' . halacoupon_store()->get_go_store_url() . '" target="_blank" rel="nofollow" class="inline-flex items-center px-6 py-3 bg-gradient-primary text-white font-semibold rounded-2xl shadow-soft hover:shadow-medium transform hover:scale-105 transition-all duration-300">';
								echo '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
								echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>';
								echo '</svg>';
								echo esc_html__('Visit Store', 'halacoupon');
								echo '</a>';
								echo '</div>';
								echo '</div>';
								echo '</div>';
								echo '</div>';
							}

							wp_reset_postdata();
							?>
						</div>



						<!-- Enhanced Store Extra Info -->
						<?php if ( halacoupon_store()->get_extra_info() ) { ?>
						<div class="mt-12">
										<?php
										// Check if store has structured content blocks
										if (function_exists('halacoupon_store_has_content_blocks') && halacoupon_store_has_content_blocks()) {
											// Render structured content blocks with navigation
											echo halacoupon_render_store_content_blocks('', array(
												'show_navigation' => true,
												'responsive'      => true,
												'add_schema'      => true,
												'container_class' => 'store-content-blocks-container enhanced-blocks',
											));
										} else {
											// Fallback to regular content display
											echo '<div class="store-content prose prose-lg prose-gray max-w-none">';
											echo halacoupon_store()->get_extra_info();
											echo '</div>';
										}
										?>
									</div>
						<?php } ?>

						<!-- Store FAQs -->
						<?php
						if (function_exists('display_coupon_store_faqs')) {
							echo display_coupon_store_faqs();
						}
						?>
					</section>

					<aside class="lg:col-span-1">
						<div class="top-24 space-y-3">

							<div class="relative group">

							<!-- Enhanced Store Description -->
							<?php if ( halacoupon_store()->get_content() ) : ?>
							<div class="relative group mb-3">
								<div class="relative bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft border border-secondary/20 p-3 hover:bg-white/80 transition-all duration-300">
									<h2 class="text-sm font-bold text-text mb-2 flex items-center">
										<div class="w-6 h-6 bg-gradient-secondary rounded-lg flex items-center justify-center mr-2">
											<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
											</svg>
										</div>
										<?php esc_html_e('About Store', 'halacoupon'); ?>
									</h2>
									<div class="store-content text-text/80 text-xs leading-relaxed prose prose-sm max-w-none">
										<?php halacoupon_store()->get_content(true, true); ?>
									</div>
								</div>
							</div>
							<?php endif; ?>

								<div class="relative bg-white/80 backdrop-blur-md rounded-2xl shadow-soft border border-primary/20 p-3 hover:bg-white/90 transition-all duration-300">
									<h2 class="text-sm font-bold text-text mb-3 flex items-center">
										<div class="w-6 h-6 bg-gradient-primary rounded-lg flex items-center justify-center mr-2">
											<svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
											</svg>
										</div>
										<?php esc_html_e('Store Statistics', 'halacoupon'); ?>
									</h2>

									<div class="grid grid-cols-2 gap-2 mb-3">
										<div class="group/stat bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl p-2 border border-primary/20 hover:border-primary/40 transition-all duration-300">
											<div class="flex flex-col items-center text-center">
												<div class="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center mb-2 group-hover/stat:bg-primary/30 transition-colors duration-300">
													<svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
													</svg>
												</div>
												<div class="text-lg font-bold text-primary mb-1">
													<?php
													if (function_exists('get_total_used_today_for_term')) {
														echo get_total_used_today_for_term($term_id, 'coupon_store');
													} else {
														echo '0';
													}
													?>
												</div>
												<div class="text-xs text-text/70 font-medium"><?php esc_html_e('Used Today', 'halacoupon'); ?></div>
											</div>
										</div>

										<div class="group/stat bg-gradient-to-br from-secondary/10 to-secondary/5 rounded-xl p-2 border border-secondary/20 hover:border-secondary/40 transition-all duration-300">
											<div class="flex flex-col items-center text-center">
												<div class="w-8 h-8 bg-secondary/20 rounded-lg flex items-center justify-center mb-2 group-hover/stat:bg-secondary/30 transition-colors duration-300">
													<svg class="w-4 h-4 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
													</svg>
												</div>
												<div class="text-lg font-bold text-secondary mb-1"><?php echo esc_html($coupon_counts['code']); ?></div>
												<div class="text-xs text-text/70 font-medium"><?php esc_html_e('Coupons', 'halacoupon'); ?></div>
											</div>
										</div>

										<!-- Total Offers -->
										<div class="group/stat bg-gradient-to-br from-accent/10 to-accent/5 rounded-xl p-2 border border-accent/20 hover:border-accent/40 transition-all duration-300">
											<div class="flex flex-col items-center text-center">
												<div class="w-8 h-8 bg-accent/20 rounded-lg flex items-center justify-center mb-2 group-hover/stat:bg-accent/30 transition-colors duration-300">
													<svg class="w-4 h-4 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														
													</svg>
												</div>
												<div class="text-lg font-bold text-accent mb-1"><?php echo esc_html($coupon_counts['sale']); ?></div>
												<div class="text-xs text-text/70 font-medium"><?php esc_html_e('Offers', 'halacoupon'); ?></div>
											</div>
										</div>

										<!-- Success Rate -->
										<div class="group/stat bg-gradient-to-br from-green-500/10 to-green-500/5 rounded-xl p-2 border border-green-500/20 hover:border-green-500/40 transition-all duration-300">
											<div class="flex flex-col items-center text-center">
												<div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center mb-2 group-hover/stat:bg-green-500/30 transition-colors duration-300">
													<svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
													</svg>
												</div>
												<div class="text-lg font-bold text-green-600 mb-1">95%</div>
												<div class="text-xs text-text/70 font-medium"><?php esc_html_e('Success Rate', 'halacoupon'); ?></div>
											</div>
										</div>
									</div>

									<!-- Additional Stats Row -->
									<div class="flex items-center justify-between p-2 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-xl border border-primary/10">
										<div class="flex items-center">
											<div class="w-6 h-6 bg-primary/20 rounded-lg flex items-center justify-center mr-2">
												<svg class="w-3 h-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
												</svg>
											</div>
											<div>
												<div class="text-xs font-medium text-text"><?php esc_html_e('Last Updated', 'halacoupon'); ?></div>
												<div class="text-xs text-text/60"><?php esc_html_e('Real-time data', 'halacoupon'); ?></div>
											</div>
										</div>
										<span class="text-xs font-bold text-primary bg-primary/10 px-2 py-1 rounded-full"><?php esc_html_e('Today', 'halacoupon'); ?></span>
									</div>
								</div>
							</div>

							<!-- Store Social Profiles & Page Links -->
							<?php
							// Display store social profiles and page links using the new function
							if (function_exists('halacoupon_display_store_social_links_section')) {
								halacoupon_display_store_social_links_section();
							}
							?>



							<!-- Original Sidebar Content -->
							<?php get_sidebar( 'store' );?>
						</div>
					</aside>
				</div>
			</div>


		</main>
	</div>


</div>

<!-- Enhanced Glass Morphism Styles -->
<style>
/* Filter Button States */
.filter-btn {
	position: relative;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Default Glass State */
.filter-btn:not(.active) {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(8px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	color: white;
}

/* Active State - Brand Primary */
.filter-btn.active {
	background: #EBBF43 !important;
	color: white !important;
	border: 1px solid rgba(255, 255, 255, 0.3);
	box-shadow: 0 8px 32px rgba(235, 191, 67, 0.3);
}

/* Hover States */
.filter-btn:not(.active):hover {
	background: rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(12px);
	transform: translateY(-2px) scale(1.05);
	box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.filter-btn.active:hover {
	background: #d4ab3d !important;
	transform: translateY(-2px) scale(1.05);
	box-shadow: 0 12px 40px rgba(235, 191, 67, 0.4);
}

</style>

<!-- Enhanced Filter Button JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
	// Enhanced filter button functionality
	const filterButtons = document.querySelectorAll('.filter-btn');

	filterButtons.forEach(button => {
		button.addEventListener('click', function() {
			// Remove active class from all buttons
			filterButtons.forEach(btn => {
				btn.classList.remove('active');
				btn.style.background = '';
				btn.style.color = '';
				btn.style.border = '';
				btn.style.boxShadow = '';
			});

			// Add active class to clicked button
			this.classList.add('active');

			// Apply active styles
			this.style.background = '#EBBF43';
			this.style.color = 'white';
			this.style.border = '1px solid rgba(255, 255, 255, 0.3)';
			this.style.boxShadow = '0 8px 32px rgba(235, 191, 67, 0.3)';
		});
	});

	// Enhanced floating animation performance
	const floatingElements = document.querySelectorAll('.animate-float');

	// Intersection Observer for performance optimization
	const observer = new IntersectionObserver((entries) => {
		entries.forEach(entry => {
			if (entry.isIntersecting) {
				entry.target.style.animationPlayState = 'running';
			} else {
				entry.target.style.animationPlayState = 'paused';
			}
		});
	});

	floatingElements.forEach(element => {
		observer.observe(element);
	});

	// Glass morphism scroll effect
	const storeHeader = document.querySelector('.store-header-glass');
	if (storeHeader) {
		window.addEventListener('scroll', () => {
			const scrolled = window.pageYOffset;
			const rate = scrolled * -0.5;

			if (scrolled > 50) {
				storeHeader.style.backdropFilter = 'blur(25px)';
				storeHeader.style.background = 'rgba(0, 0, 0, 0.8)';
			} else {
				storeHeader.style.backdropFilter = 'blur(20px)';
				storeHeader.style.background = 'rgba(0, 0, 0, 0.6)';
			}
		});
	}
});
</script>

<?php get_footer(); ?>