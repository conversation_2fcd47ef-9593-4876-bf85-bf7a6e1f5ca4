/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './*.php',
    './inc/**/*.php',
    './loop/**/*.php',
    './template-parts/**/*.php',
    './assets/js/**/*.js',
  ],

  // Safelist to ensure gradient classes are always included
  safelist: [
    'bg-gradient-primary',
    'bg-gradient-secondary',
    'bg-gradient-cta',
    'bg-gradient-sunset',
    'bg-gradient-accent',
    // Animation classes
    'animate-float',
    'animate-float-slow',
    'animate-float-fast',
    // Glass morphism classes
    'backdrop-blur-sm',
    'backdrop-blur-md',
    'backdrop-blur-lg',
    'backdrop-blur-xl',
    // Shadow classes
    'shadow-soft',
    'shadow-glow',
    'shadow-glow-secondary',
    'shadow-card',
    'shadow-card-hover',
    'shadow-float',
    'shadow-float-lg',
    'shadow-medium',
  ],

  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#EBBF43',
        },
        secondary: {
          DEFAULT: '#D56030',
        },
        accent: {
          DEFAULT: '#66E093',
        },
        text: '#1f2937',
        background: '#F9FAFB',
      },

      // Custom shadows
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'glow': '0 0 20px rgba(235, 191, 67, 0.3)',
        'glow-secondary': '0 0 20px rgba(255, 188, 2, 0.3)',
        'card': '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
        'card-hover': '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)',
        'float': '0 4px 20px -2px rgba(0, 0, 0, 0.1), 0 2px 8px -2px rgba(0, 0, 0, 0.06)',
        'float-lg': '0 8px 30px -4px rgba(0, 0, 0, 0.15), 0 4px 12px -4px rgba(0, 0, 0, 0.1)',
        'medium': '0 6px 20px -6px rgba(0, 0, 0, 0.15), 0 4px 16px -4px rgba(0, 0, 0, 0.1)',
      },

      // Custom gradients
      backgroundImage: {
        'gradient-primary': 'linear-gradient(to right, #EBBF43, #D56030)',
        'gradient-secondary': 'linear-gradient(to right, #D56030, #B94B27)',
        'gradient-accent': 'linear-gradient(to right, #66E093, #4CAF50)',
        'gradient-cta': 'linear-gradient(to right, #D56030, #EBBF43)',
      },

      // Custom animations
      animation: {
        'fade-in-up': 'fadeInUp 0.5s ease-out',
        'float': 'float 6s ease-in-out infinite',
        'float-slow': 'float 8s ease-in-out infinite',
        'float-fast': 'float 4s ease-in-out infinite',
      },

      // Custom keyframes
      keyframes: {
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        float: {
          '0%, 100%': {
            transform: 'translateY(0px) rotate(0deg)',
            opacity: '0.6'
          },
          '50%': {
            transform: 'translateY(-20px) rotate(180deg)',
            opacity: '0.8'
          },
        },
      },

      // Typography
      fontFamily: {
        'sans': ['Rubik', 'system-ui', 'sans-serif'],
        'rubik': ['Rubik', 'system-ui', 'sans-serif'],
        'mono': ['JetBrains Mono', 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', 'Consolas', 'Courier New', 'monospace'],
      },
    },
  },
  plugins: [
	    require('@tailwindcss/typography'),
  ],
};