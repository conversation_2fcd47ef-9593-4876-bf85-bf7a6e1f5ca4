<?php
/**
 * Front Page Template
 * @package HalaCoupon
 * @version 2.0.0 - 
 */

get_header();



?>
<main id="content-wrap" class="relative min-h-screen bg-background overflow-hidden">
	<div class="absolute inset-0 bg-mesh-1 opacity-5"></div>
	<div class="absolute top-0 right-0 w-96 h-96 bg-gradient-primary rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>

	<div class="relative z-10">

		<section class="relative py-4 sm:py-6 lg:py-8 xl:py-12 overflow-hidden"
		         role="banner"
		         aria-label="<?php esc_attr_e('Hero section with featured content slider', 'halacoupon'); ?>"
		         dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">

			<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-10">
				<!-- Premium Hero Slider Container -->
				<div class="relative grid grid-cols-1 lg:grid-cols-4 gap-8 mb-8 lg:mb-12 xl:mb-16">
					<div class="lg:col-span-3 rounded-2xl overflow-hidden group">
						<!-- Slider Content -->
						<div class="relative overflow-hidden">
							<?php
							// Display enhanced home slider with error handling
							if (function_exists('display_home_slider')) {
								display_home_slider();
							} else {
								?>
								<div class="relative h-80 sm:h-96 lg:h-[505px] flex items-center justify-center bg-gradient-to-br from-primary/20 via-secondary/15 to-accent/10 rounded-2xl lg:rounded-3xl overflow-hidden">
									<div class="absolute inset-0 opacity-10">
										<div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12"></div>
									</div>

									<div class="relative text-center text-white z-10 max-w-4xl mx-auto px-6">
										<div class="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full border border-white/30 mb-6 animate-fade-in-down">
											<svg class="w-5 h-5 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
											</svg>
											<span class="text-sm font-medium"><?php esc_html_e('Featured', 'halacoupon'); ?></span>
										</div>

										<h1 class="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 leading-tight animate-fade-in-up">
											<?php esc_html_e('Welcome to', 'halacoupon'); ?>
											<span class="block text-secondary mt-2"><?php bloginfo('name'); ?></span>
										</h1>

										<p class="text-lg sm:text-xl lg:text-2xl opacity-90 mb-8 leading-relaxed animate-fade-in-up" style="animation-delay: 0.2s;">
											<?php esc_html_e('Discover amazing deals and exclusive coupons from top brands', 'halacoupon'); ?>
										</p>

										<!-- CTA Button -->
										<div class="animate-fade-in-up" style="animation-delay: 0.4s;">
											<a href="<?php echo esc_url(get_post_type_archive_link('coupon')); ?>"
											   class="inline-flex items-center gap-3 bg-gradient-to-r from-secondary to-primary text-white px-8 py-4 rounded-2xl font-semibold text-lg shadow-glow hover:shadow-glow-secondary transform hover:scale-105 transition-all duration-300">
												<span><?php esc_html_e('Explore Deals', 'halacoupon'); ?></span>
												<svg class="w-5 h-5 <?php echo is_rtl() ? 'rotate-180' : ''; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
												</svg>
											</a>
										</div>
									</div>
								</div>
								<?php
							}
							?>
						</div>
					</div>
					<div class="lg:col-span-1">
						<?php list_stores_in_divided_list( 0, 'pinned', 6 ); ?>
					</div>
				</div>
			</div>

			<!-- Schema markup for SEO -->
			<script type="application/ld+json">
			{
				"@context": "https://schema.org",
				"@type": "WebPageElement",
				"name": "<?php echo esc_js(__('Hero Section', 'halacoupon')); ?>",
				"description": "<?php echo esc_js(__('Featured content and popular categories for coupon deals', 'halacoupon')); ?>",
				"url": "<?php echo esc_js(home_url('/')); ?>",
				"mainEntity": {
					"@type": "ItemList",
					"name": "<?php echo esc_js(__('Popular Categories', 'halacoupon')); ?>",
					"description": "<?php echo esc_js(__('Discover amazing deals across all your favorite categories', 'halacoupon')); ?>"
				}
			}
			</script>
		</section>


		<!-- Featured Stores Section -->
		<section class="relative pb-10 lg:pb-14">
			<div class="absolute inset-0 bg-gradient-to-br from-secondary-50 via-white to-accent-50"></div>
			<div class="absolute top-10 left-10 w-32 h-32 bg-gradient-secondary rounded-full mix-blend-multiply filter blur-2xl opacity-30 animate-pulse"></div>
			<div class="absolute bottom-10 right-10 w-40 h-40 bg-gradient-primary rounded-full mix-blend-multiply filter blur-2xl opacity-25 animate-pulse" style="animation-delay: 1s;"></div>

			<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

				<!-- Section Header -->
				<div class="text-center mb-12">
					<div class="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-soft border border-white/20 mb-6 animate-fade-in-down">
						<svg class="w-5 h-5 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
						</svg>
						<span class="text-sm font-medium text-gray-700"><?php esc_html_e('Featured', 'halacoupon'); ?></span>
					</div>

					<h2 class="text-4xl lg:text-5xl font-bold text-text mb-6 animate-fade-in-up">
						<span class="text-secondary">
							<?php esc_html_e('Featured Stores', 'halacoupon'); ?>
						</span>
					</h2>

					<p class="text-xl text-text/70 max-w-3xl mx-auto leading-relaxed animate-fade-in-up" style="animation-delay: 0.2s;">
						<?php esc_html_e('Discover exclusive deals from our top-rated partner stores and unlock incredible savings', 'halacoupon'); ?>
					</p>
				</div>

				<!-- Featured Stores Slider -->
				<div class="animate-fade-in-up" style="animation-delay: 0.4s;">
					<?php
					if( function_exists('halacoupon_get_featured_stores_slider')){
						halacoupon_get_featured_stores_slider();
					}
					?>
				</div>
			</div>
		</section>






		<!-- Today's Offers Section -->
		<section class="relative py-16 lg:py-20 bg-white">
			<div class="absolute inset-0 bg-gradient-to-r from-primary-50 via-transparent to-accent-50"></div>
			<div class="absolute top-0 left-1/4 w-64 h-64 bg-gradient-primary rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-float"></div>

			<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

				<!-- Section Header -->
				<div class="text-center mb-12">
					<div class="inline-flex items-center gap-2 bg-gradient-primary text-white px-6 py-3 rounded-full shadow-glow mb-6 animate-fade-in-down">
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
						</svg>
						<span class="font-bold"><?php esc_html_e("Today's Hot Deals", 'halacoupon'); ?></span>
					</div>

					<h2 class="text-4xl lg:text-5xl font-bold text-text mb-6 animate-fade-in-up">
						<span class="text-primary">
							<?php esc_html_e("Today's Offers", 'halacoupon'); ?>
						</span>
					</h2>

					<p class="text-xl text-text/70 max-w-2xl mx-auto leading-relaxed animate-fade-in-up" style="animation-delay: 0.2s;">
						<?php esc_html_e('Fresh deals updated daily - grab them before they expire!', 'halacoupon'); ?>
					</p>
				</div>

				<!-- Today's Coupons Grid -->
				<div class="relative">
					<div class="absolute inset-0 bg-white/60 backdrop-blur-sm rounded-3xl shadow-float border border-white/20"></div>
					<div class="relative p-8 animate-fade-in-up" style="animation-delay: 0.4s;">
						<?php
						if( function_exists('get_latest_coupons')){
							get_latest_coupons();
						}
						?>
					</div>
				</div>
			</div>
		</section>

		<!-- Enhanced All Stores Section -->
		<section class="relative py-12 lg:py-16 xl:py-20"
		         role="region"
		         aria-label="<?php esc_attr_e('Partner stores showcase', 'halacoupon'); ?>"
		         dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">

			<!-- Enhanced Background Elements -->
			<div class="absolute inset-0 overflow-hidden pointer-events-none">
				<!-- Primary gradient background -->
				<div class="absolute inset-0 bg-gradient-to-br from-gray-50/80 via-white to-secondary-50/60"></div>

				<!-- Floating gradient orbs -->
				<div class="absolute top-16 <?php echo is_rtl() ? 'left-16' : 'right-16'; ?> w-48 h-48 lg:w-64 lg:h-64 bg-gradient-to-br from-secondary/20 via-accent/15 to-transparent rounded-full mix-blend-multiply filter blur-3xl opacity-40 animate-pulse"></div>
				<div class="absolute bottom-16 <?php echo is_rtl() ? 'right-16' : 'left-16'; ?> w-40 h-40 lg:w-56 lg:h-56 bg-gradient-to-tr from-primary/15 via-secondary/10 to-transparent rounded-full mix-blend-multiply filter blur-2xl opacity-30 animate-pulse" style="animation-delay: 2s;"></div>

				<!-- Accent elements -->
				<div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 lg:w-48 lg:h-48 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-float"></div>
			</div>

			<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-10">

				<!-- Enhanced Section Header -->
				<header class="text-center mb-12 lg:mb-16">
					<!-- Badge with glass effect -->
					<div class="inline-flex items-center gap-3 bg-white/20 backdrop-blur-lg px-6 py-3 rounded-full shadow-float border border-white/30 mb-6 lg:mb-8 animate-fade-in-down">
						<div class="w-8 h-8 bg-gradient-to-br from-secondary to-accent rounded-lg flex items-center justify-center">
							<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
							</svg>
						</div>
						<span class="text-sm font-semibold text-text/80 tracking-wide">
							<?php esc_html_e('Partner Stores', 'halacoupon'); ?>
						</span>
					</div>

					<!-- Main heading with gradient text -->
					<h2 class="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-text mb-4 lg:mb-6 animate-fade-in-up">
						<span class="">
							<?php esc_html_e('Trusted Stores', 'halacoupon'); ?>
						</span>
					</h2>

					<!-- Enhanced description -->
					<p class="text-base sm:text-lg lg:text-xl text-text/70 max-w-4xl mx-auto leading-relaxed mb-8 lg:mb-12 animate-fade-in-up" style="animation-delay: 0.2s;">
						<?php esc_html_e('Discover exclusive deals from our carefully selected partner stores. Each store is verified to bring you the best savings and authentic offers.', 'halacoupon'); ?>
					</p>

				</header>

				<!-- Enhanced Stores Grid Container -->
				<div class="animate-fade-in-up" style="animation-delay: 0.6s;">
					<!-- Glass morphism container -->
					<div class="relative bg-white rounded-2xl border border-white/20 shadow-float overflow-hidden">

						<!-- Content container -->
						<div class="relative p-6 lg:p-8">
							<?php
							// Display stores grid with enhanced parameters
							if (function_exists('display_stores_grid')) {
								display_stores_grid(18, true); 
							} else {
								// Fallback content
								?>
								<div class="text-center py-12 lg:py-16">
									<div class="w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-2xl flex items-center justify-center mx-auto mb-6">
										<svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
										</svg>
									</div>
									<h3 class="text-lg font-semibold text-gray-700 mb-2">
										<?php esc_html_e('Stores Coming Soon', 'halacoupon'); ?>
									</h3>
									<p class="text-gray-600">
										<?php esc_html_e('We are adding amazing partner stores for you.', 'halacoupon'); ?>
									</p>
								</div>
								<?php
							}
							?>
						</div>

						<!-- Decorative corner elements -->
						<div class="absolute top-4 <?php echo is_rtl() ? 'left-4' : 'right-4'; ?> w-12 h-12 bg-gradient-to-br from-accent/20 to-transparent rounded-full blur-lg"></div>
						<div class="absolute bottom-4 <?php echo is_rtl() ? 'right-4' : 'left-4'; ?> w-8 h-8 bg-gradient-to-tr from-secondary/20 to-transparent rounded-full blur-sm"></div>
					</div>
				</div>
			</div>

			<!-- Schema markup for SEO -->
			<script type="application/ld+json">
			{
				"@context": "https://schema.org",
				"@type": "WebPageElement",
				"name": "<?php echo esc_js(__('Partner Stores Section', 'halacoupon')); ?>",
				"description": "<?php echo esc_js(__('Trusted partner stores offering exclusive deals and verified coupons', 'halacoupon')); ?>",
				"url": "<?php echo esc_js(home_url('/')); ?>",
				"mainEntity": {
					"@type": "ItemList",
					"name": "<?php echo esc_js(__('Partner Stores', 'halacoupon')); ?>",
					"description": "<?php echo esc_js(__('Carefully selected partner stores with verified offers', 'halacoupon')); ?>"
				}
			}
			</script>
		</section>


		<!-- Browse Categories Section -->
		<section class="relative py-16 lg:py-20 bg-white">
			<div class="absolute inset-0 bg-gradient-to-r from-accent-50 via-transparent to-primary-50"></div>
			<div class="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-accent rounded-full mix-blend-multiply filter blur-3xl opacity-15 animate-float"></div>

			<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

				<!-- Section Header -->
				<div class="text-center mb-12">
					<div class="inline-flex items-center gap-2 bg-gradient-accent text-white px-6 py-3 rounded-full shadow-glow-accent mb-6 animate-fade-in-down">
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
						</svg>
						<span class="font-bold"><?php esc_html_e('Categories', 'halacoupon'); ?></span>
					</div>

					<h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 animate-fade-in-up">
						<span class="text-accent">
							<?php esc_html_e('Browse Categories', 'halacoupon'); ?>
						</span>
					</h2>

					<p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed animate-fade-in-up" style="animation-delay: 0.2s;">
						<?php esc_html_e('Explore our diverse categories and discover the perfect deals for every aspect of your life', 'halacoupon'); ?>
					</p>
				</div>

				<!-- Categories Grid -->
				<div class="relative">
					<div class="absolute inset-0 bg-white/40 backdrop-blur-sm rounded-3xl shadow-float border border-white/20"></div>
					<div class="relative p-8 animate-fade-in-up" style="animation-delay: 0.4s;">
						<?php
						if( function_exists('list_coupon_categories_with_icons')){
							list_coupon_categories_with_icons();
						}
						?>
					</div>
				</div>
			</div>
		</section>


		<!-- Best Deals Section -->
		<section class="relative py-16 lg:py-20">
			<div class="absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-sunset-50"></div>
			<div class="absolute top-10 right-10 w-56 h-56 bg-gradient-sunset rounded-full mix-blend-multiply filter blur-2xl opacity-25 animate-pulse"></div>
			<div class="absolute bottom-20 left-20 w-40 h-40 bg-gradient-primary rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" style="animation-delay: 1.5s;"></div>

			<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

				<!-- Section Header -->
				<div class="text-center mb-12">
					<div class="inline-flex items-center gap-2 bg-gradient-sunset text-white px-6 py-3 rounded-full shadow-large mb-6 animate-fade-in-down">
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
						</svg>
						<span class="font-bold"><?php esc_html_e('Best Reviews', 'halacoupon'); ?></span>
					</div>

					<h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 animate-fade-in-up">
						<span class="text-primary">
							<?php esc_html_e('Best Deals For Saving', 'halacoupon'); ?>
						</span>
					</h2>

					<p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed animate-fade-in-up" style="animation-delay: 0.2s;">
						<?php esc_html_e('Handpicked premium Reviews with maximum savings - your wallet will thank you!', 'halacoupon'); ?>
					</p>
				</div>

				<!-- Best Deals Grid -->
				<div class="relative">
					<div class="absolute inset-0 bg-white/50 backdrop-blur-sm rounded-3xl shadow-float border border-white/20"></div>
					<div class="relative p-8 animate-fade-in-up" style="animation-delay: 0.4s;">
						<?php
						if( function_exists('halacoupon_featured_reviews')){
							halacoupon_featured_reviews();
						}
						?>
					</div>
				</div>
			</div>
		</section>



		<!-- Blog Section -->
		<section class="relative py-16 lg:py-20">
			<div class="absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-secondary-50"></div>
			<div class="absolute top-0 right-1/4 w-64 h-64 bg-gradient-primary rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-pulse"></div>
			<div class="absolute bottom-10 left-10 w-48 h-48 bg-gradient-secondary rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-pulse" style="animation-delay: 1s;"></div>

			<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

				<!-- Section Header -->
				<div class="text-center mb-12">
					<div class="inline-flex items-center gap-2 bg-gradient-primary text-white px-6 py-3 rounded-full shadow-large mb-6 animate-fade-in-down">
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
						</svg>
						<span class="font-bold"><?php esc_html_e('Latest Articles', 'halacoupon'); ?></span>
					</div>

					<h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 animate-fade-in-up">
						<span class="text-primary">
							<?php esc_html_e('Blog', 'halacoupon'); ?>
						</span>
					</h2>

					<p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed animate-fade-in-up" style="animation-delay: 0.2s;">
						<?php esc_html_e('Stay updated with the latest tips, trends, and exclusive insights from our experts', 'halacoupon'); ?>
					</p>
				</div>

				<!-- Blog Grid -->
				<div class="relative">
					<div class="relative animate-fade-in-up" style="animation-delay: 0.4s;">
						<?php
						if( function_exists('halacoupon_latest_blogs')){
							halacoupon_latest_blogs();
						}
						?>
					</div>
				</div>
			</div>
		</section>

		<!-- Page Content Section -->
		<?php
		wp_reset_query();
		$current_page_id = get_the_ID();
		$page = get_post($current_page_id);
		if ( !empty($page->post_content) ) : ?>
		<section class="relative py-16 bg-white page-content">
			<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="prose prose-lg max-w-none">
					<?php get_template_part('content'); ?>
				</div>
			</div>
		</section>
		<?php endif; ?>

	</div>
</main>


<?php get_footer(); ?>