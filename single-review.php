<?php
/**
 * Single Review Article Template
 *
 * A complete redesign focusing on a modern, creative, and immersive user experience,
 * leveraging glass morphism, parallax effects, and interactive elements.
 *
 * @package HalaCoupon
 * @version 3.0.0
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

get_header();

// Main loop
while (have_posts()) :
    the_post();

    // --- Pre-fetch all necessary data ---
    $article_id = get_the_ID();
    $is_rtl = is_rtl();

    $average_rating = get_post_meta($article_id, '_halacoupon_article_average_rating', true) ?: 0;
    $total_ratings = get_post_meta($article_id, '_halacoupon_article_total_ratings', true) ?: 0;
    $featured_image_url = get_the_post_thumbnail_url($article_id, 'full');

    // Author data
    $author_id = get_the_author_meta('ID');
    $author_name = get_the_author();
    $author_avatar_url = get_avatar_url($author_id, ['size' => 96]);

    // Navigation
    $prev_post = get_previous_post();
    $next_post = get_next_post();

?>

<!-- Enhanced Article Schema Markup -->
<article <?php post_class('modern-review-entry'); ?> itemscope itemtype="https://schema.org/Review">

    <!-- Modern Hero Section with Parallax Effect -->
    <section class="relative h-[50vh] overflow-hidden">

        <!-- Background Image with Parallax -->
        <?php if ($featured_image_url) : ?>
        <div class="absolute inset-0 parallax-bg">
            <img src="<?php echo esc_url($featured_image_url); ?>" class="w-full h-full object-cover scale-110" alt="<?php echo esc_attr(get_the_title()); ?>" itemprop="image">
        </div>
        <?php else : ?>
        <!-- Fallback gradient background -->
        <div class="absolute inset-0 bg-gradient-primary"></div>
        <?php endif; ?>

        <!-- Overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/60 to-transparent"></div>

        <!-- Hero Content -->
        <div class="relative h-full flex items-end">
            <div class="container mx-auto px-4 pb-34 md:pb-32">
                <div class="max-w-4xl mx-auto text-white">

                    <!-- Title -->
                    <h1 class="text-4xl md:text-6xl font-bold text-white leading-tight my-6" itemprop="name">
                        <?php the_title(); ?>
                    </h1>

                    <!-- Meta and Rating -->
                    <div class="flex flex-wrap items-center gap-x-6 gap-y-4 text-white/90">
                        <!-- Rating Summary -->
                        <?php if ($total_ratings > 0) : ?>
                        <div class="flex items-center gap-3" itemprop="reviewRating" itemscope itemtype="https://schema.org/Rating">
                            <div class="flex items-center gap-1">
                                <?php for ($i = 1; $i <= 5; $i++) : ?>
                                <svg class="w-5 h-5 <?php echo $i <= $average_rating ? 'text-primary' : 'text-gray-400'; ?>" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                <?php endfor; ?>
                            </div>
                            <span class="font-bold text-lg"><?php echo number_format($average_rating, 1); ?></span>
                            <span class="text-sm text-white/70">(<?php echo $total_ratings; ?> <?php echo _n('rating', 'ratings', $total_ratings, 'halacoupon'); ?>)</span>
                            <meta itemprop="ratingValue" content="<?php echo esc_attr($average_rating); ?>">
                            <meta itemprop="ratingCount" content="<?php echo esc_attr($total_ratings); ?>">
                            <meta itemprop="worstRating" content="1">
                            <meta itemprop="bestRating" content="5">
                        </div>
                        <span class="hidden md:inline text-white/40">•</span>
                        <?php endif; ?>

                        <!-- Author -->
                        <div class="flex items-center gap-2" itemprop="author" itemscope itemtype="https://schema.org/Person">
                            <img src="<?php echo esc_url($author_avatar_url); ?>" alt="<?php echo esc_attr($author_name); ?>" class="w-8 h-8 rounded-full border border-white/30">
                            <span itemprop="name" class="font-medium"><?php echo esc_html($author_name); ?></span>
                        </div>
                        <span class="hidden md:inline text-white/40">•</span>

                        <!-- Date -->
                        <time datetime="<?php echo esc_attr(get_the_date('c')); ?>" itemprop="datePublished" class="flex items-center gap-2 text-sm">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg>
                            <?php echo esc_html(get_the_date()); ?>
                        </time>
                    </div>

                </div>
            </div>
        </div>

        <!-- Scroll Down Indicator -->
        <div class="absolute bottom-16 left-1/2 -translate-x-1/2 animate-bounce">
            <div class="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
            </div>
        </div>
    </section>

    <!-- Floating Content Card -->
    <div class="relative -mt-16 z-10">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">

                <!-- Main Content Card -->
                <div class="glass-card rounded-3xl border border-white/20 overflow-hidden shadow-2xl">
                
                    <!-- Sticky Social Sharing Bar -->
                    <div class="sticky top-0 z-20 backdrop-blur-md border-b border-gray-100 px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex-1 <?php echo $is_rtl ? 'ml-6' : 'mr-6'; ?>">
                                <div class="h-1 bg-gray-200 rounded-full overflow-hidden">
                                    <div id="reading-progress" class="h-full bg-gradient-primary transition-all duration-300 ease-out" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <span class="text-sm text-text/70 hidden md:block"><?php esc_html_e('Share:', 'halacoupon'); ?></span>
                                <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" target="_blank" rel="noopener" class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200" aria-label="<?php esc_attr_e('Share on Twitter', 'halacoupon'); ?>"><svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg></a>
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" target="_blank" rel="noopener" class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200" aria-label="<?php esc_attr_e('Share on Facebook', 'halacoupon'); ?>"><svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg></a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(get_permalink()); ?>" target="_blank" rel="noopener" class="w-8 h-8 bg-blue-700 text-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200" aria-label="<?php esc_attr_e('Share on LinkedIn', 'halacoupon'); ?>"><svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg></a>
                                <button onclick="copyToClipboard('<?php echo esc_js(get_permalink()); ?>', this)" class="w-8 h-8 bg-gray-600 text-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200" aria-label="<?php esc_attr_e('Copy link', 'halacoupon'); ?>"><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button>
                            </div>
                        </div>
                    </div>

                    <!-- Main Article Content -->
                    <div class="px-6 md:px-12 py-8 md:py-12">
                        
                        <!-- Table of Contents (if headings exist) -->
                        <div id="table-of-contents" class="hidden mb-8 p-6 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl border border-primary/10">
                            <h3 class="text-lg font-semibold text-text mb-4 flex items-center gap-2">
                                <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>
                                <?php esc_html_e('In This Review', 'halacoupon'); ?>
                            </h3>
                            <ul id="toc-list" class="space-y-2"></ul>
                        </div>

                        <!-- Main Content -->
                        <div class="entry-content prose prose-lg max-w-none" itemprop="reviewBody">
                            <?php the_content(); ?>
                        </div>

                        <!-- Interactive Rating Section -->
                        <div class="article-rating-section mt-12 pt-8 border-t border-gray-200">
                            <h3 class="text-xl font-bold text-text mb-2"><?php esc_html_e('Rate this Review', 'halacoupon'); ?></h3>
                            <p class="text-text/70 text-sm mb-4"><?php esc_html_e('Was this review helpful? Let us know what you think!', 'halacoupon'); ?></p>
                            <?php halacoupon_display_article_rating($article_id, true); ?>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Post Footer Section -->
    <div class="container mx-auto px-4 py-12">
        <div class="max-w-4xl mx-auto space-y-12">

            <!-- Author Bio -->
            <section class="glass-card rounded-2xl p-6 md:p-8 border border-white/20">
                <div class="flex flex-col md:flex-row gap-6 items-center justify-between md:justify-start ?>">
                    <div class="flex-shrink-0 group transition-transform duration-200 hover:scale-105">
                        <img class="w-24 h-24 rounded-2xl shadow-lg border-4 border-white/40 group-hover:border-primary transition-all duration-200" src="<?php echo esc_url($author_avatar_url); ?>" alt="<?php echo esc_attr($author_name); ?>" itemprop="image">
                    </div>
                    <div class="flex-1 <?php echo $is_rtl ? 'md:text-right' : 'md:text-left'; ?>">
                        <p class="text-sm text-primary font-semibold tracking-wider uppercase mb-1"><?php esc_html_e('Written By', 'halacoupon'); ?></p>
                        <h3 class="text-xl font-bold text-text mt-1 mb-2" itemprop="name">
                            <?php echo esc_html($author_name); ?>
                        </h3>
                        <?php $author_bio = get_the_author_meta('description'); ?>
                        <?php if ($author_bio) : ?>
                        <p class="text-text/80 mb-2" itemprop="description">
                            <?php echo esc_html($author_bio); ?>
                        </p>
                        <?php endif; ?>
                        <?php
                        // Social links (optional, add your own meta fields as needed)
                        $author_socials = array();
                        $social_fields = array(
                            'facebook' => 'Facebook',
                            'twitter' => 'Twitter',
                            'linkedin' => 'LinkedIn',
                            'website' => 'Website',
                        );
                        foreach ($social_fields as $key => $label) {
                            $val = get_the_author_meta($key);
                            if ($val) {
                                $author_socials[$key] = $val;
                            }
                        }
                        if (!empty($author_socials)) : ?>
                        <div class="flex gap-3 mt-2 <?php echo $is_rtl ? 'justify-end' : 'justify-start'; ?>">
                            <?php foreach ($author_socials as $key => $url) : ?>
                                <a href="<?php echo esc_url($url); ?>" target="_blank" rel="noopener" aria-label="<?php echo esc_attr($key . ' profile'); ?>" class="w-8 h-8 rounded-full flex items-center justify-center bg-gray-100 hover:bg-primary/90 text-gray-600 hover:text-white transition-colors duration-200">
                                    <?php if ($key === 'facebook') : ?>
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>
                                    <?php elseif ($key === 'twitter') : ?>
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>
                                    <?php elseif ($key === 'linkedin') : ?>
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg>
                                    <?php elseif ($key === 'website') : ?>
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"/><path d="M2 12h20M12 2a15.3 15.3 0 010 20M12 2a15.3 15.3 0 000 20"/></svg>
                                    <?php endif; ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </section>
            
            <!-- Post Navigation -->
            <?php if (($prev_post instanceof WP_Post) || ($next_post instanceof WP_Post)) : ?>
            <nav class="grid grid-cols-1 md:grid-cols-2 gap-6 <?php echo $is_rtl ? 'direction-rtl' : 'direction-ltr'; ?>" aria-label="<?php esc_attr_e('Post navigation', 'halacoupon'); ?>">
                <?php if ($prev_post instanceof WP_Post) : ?>
                <a href="<?php echo esc_url(get_permalink($prev_post->ID)); ?>" class="group nav-card <?php echo $is_rtl ? 'flex-row-reverse text-right' : 'flex-row text-left'; ?>">
                    <div class="flex-1 min-w-0">
                        <p class="text-sm text-text/60 mb-1"><?php esc_html_e('Previous Review', 'halacoupon'); ?></p>
                        <h4 class="font-semibold text-text group-hover:text-primary transition-colors duration-200 line-clamp-2">
                            <?php echo esc_html(get_the_title($prev_post->ID)); ?>
                        </h4>
                    </div>
                    <div class="nav-arrow-icon bg-primary text-white hover:bg-slate-950">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $is_rtl ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'; ?>"></path></svg>
                    </div>
                </a>
                <?php else: ?>
                <div></div>
                <?php endif; ?>

                <?php if ($next_post instanceof WP_Post) : ?>
                <a href="<?php echo esc_url(get_permalink($next_post->ID)); ?>" class="group nav-card <?php echo $is_rtl ? 'flex-row-reverse text-left' : 'flex-row-reverse text-right'; ?>">
                     <div class="nav-arrow-icon bg-primary text-white hover:bg-slate-950">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $is_rtl ? 'M15 19l-7-7 7-7' : 'M9 5l7 7-7 7'; ?>"></path></svg>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm text-text/60 mb-1"><?php esc_html_e('Next Review', 'halacoupon'); ?></p>
                        <h4 class="font-semibold text-text group-hover:text-primary transition-colors duration-200 line-clamp-2">
                            <?php echo esc_html(get_the_title($next_post->ID)); ?>
                        </h4>
                    </div>
                </a>
                <?php endif; ?>
            </nav>
            <?php endif; ?>

        </div>
    </div>
</article>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const isRtl = <?php echo json_encode($is_rtl); ?>;
    const articleBody = document.querySelector('[itemprop="reviewBody"]');
    const progressBar = document.getElementById('reading-progress');
    const tocContainer = document.getElementById('table-of-contents');
    const tocList = document.getElementById('toc-list');
    const parallaxBg = document.querySelector('.parallax-bg img');

    // 1. Reading Progress Bar
    if (progressBar && articleBody) {
        function updateProgress() {
            const contentRect = articleBody.getBoundingClientRect();
            const windowHeight = window.innerHeight;
            const progress = (windowHeight - contentRect.top) / (contentRect.height);
            const clampedProgress = Math.min(1, Math.max(0, progress));
            progressBar.style.width = (clampedProgress * 100) + '%';
        }
        window.addEventListener('scroll', updateProgress, { passive: true });
        updateProgress();
    }

    // 2. Table of Contents Generation
    if (tocContainer && tocList && articleBody) {
        const headings = articleBody.querySelectorAll('h2, h3');
        if (headings.length > 1) {
            tocContainer.classList.remove('hidden');
            headings.forEach((heading, index) => {
                if (!heading.id) {
                    heading.id = 'toc-heading-' + index;
                }
                const li = document.createElement('li');
                const a = document.createElement('a');
                a.href = '#' + heading.id;
                a.textContent = heading.textContent;
                a.className = 'text-sm text-text/70 hover:text-primary transition-colors duration-200 block py-1';
                if (heading.tagName === 'H3') {
                    a.className += isRtl ? ' mr-4' : ' ml-4';
                }
                li.appendChild(a);
                tocList.appendChild(li);
            });
        }
    }




</script>

<style>

.glass-card {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
}
@supports not ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
    .glass-card {
        background: rgba(255, 255, 255, 0.95);
    }
}

.reviewed-item-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    background-color: rgba(249, 250, 251, 0.5); 
    border-radius: 1rem; /* rounded-2xl */
    border: 1px solid rgba(229, 231, 235, 0.6);
    transition: all 0.2s ease-out;
}
.reviewed-item-card:hover {
    border-color: #d56030; 
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    transform: translateY(-2px);
}
.reviewed-item-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem; 
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}
.reviewed-item-card .arrow-icon {
    color: #9ca3af;
    transition: transform 0.2s ease-out;
}
.reviewed-item-card:hover .arrow-icon {
    color: #d56030; 
    transform: scale(1.1);
}

/* Post Navigation Cards */
.nav-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.6);
    transition: all 0.2s ease-out;
}
html.no-glass .nav-card { background-color: #fff; }
.nav-card:hover {
    border-color: #d56030;
    transform: scale(1.03);
}
.nav-arrow-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: white;
    transition: transform 0.2s ease-out;
}
.nav-card:hover .nav-arrow-icon {
    transform: scale(1.1);
}

/* Utility */
.line-clamp-1 { display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden; }
.line-clamp-2 { display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; }

/* Prose enhancements */
.prose h2, .prose h3, .prose h4 {
    scroll-margin-top: 100px;
}
.prose img, .prose video {
    border-radius: 1rem; 
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.07);
}

/* Parallax container */
.parallax-bg {
    will-change: transform;
}
</style>

<?php
endwhile;

get_footer();
?>