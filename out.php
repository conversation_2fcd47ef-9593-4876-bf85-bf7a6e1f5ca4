<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */

function agcoupon_out_url_redirect() {
    if (get_option('permalink_structure') != '') {
        $out = get_query_var('out');
    } else {
        $out = isset($_GET['out']) ? sanitize_text_field($_GET['out']) : 'false';
    }

    // Extract coupon ID from the URL
    if (strpos($out, '#') !== false) {
        $out = explode('#', $out)[1];
    } elseif (preg_match('/\/(\d+)$/', $out, $matches)) {
        $out = $matches[1];
    }

    if (is_numeric($out)) {
        $id = intval($out);
        $coupon = new halacoupon_Coupon($id);

        if ($coupon->is_coupon()) {
            halacoupon_Coupon_Tracking::update_used($coupon->ID);
            $url = $coupon->get_destination_url() ?: site_url('/');

            // Add query args to URL for redirection
            $redirect_url = add_query_arg(array(
                'coupon_redirect' => $id,
                'redirect_url' => urlencode($url),
            ), home_url());

            wp_safe_redirect($redirect_url);
            exit;
        }
    }
}
add_action('wp', 'agcoupon_out_url_redirect', 5);

function agcoupon_store_out_url_redirect() {
    if (get_option('permalink_structure') != '') {
        $out = get_query_var('go_store_id');
    } else {
        $out = isset($_GET['go_store_id']) ? sanitize_text_field($_GET['go_store_id']) : 'go_store_id';
    }

    if (is_numeric($out)) {
        $id = intval($out);
        $store = new halacoupon_Store($id);

        if ($store->is_store()) {
            $go_out = (int) get_term_meta($store->term_id, '_ags_go_out', true);
            update_term_meta($store->term_id, '_ags_go_out', $go_out + 1);

            $url = $store->get_website_url() ?: $store->get_url();

            // Add query args to URL for redirection
            $redirect_url = add_query_arg(array(
                'store_redirect' => $id,
                'redirect_url' => urlencode($url),
            ), home_url());

            wp_safe_redirect($redirect_url);
            exit;
        }
    }
}
add_action('wp', 'agcoupon_store_out_url_redirect', 100);

function agcoupon_handle_redirects() {
    // Check for coupon redirect
    if (isset($_GET['coupon_redirect'])) {
        $coupon_id = intval($_GET['coupon_redirect']);
        $url = isset($_GET['redirect_url']) ? esc_url_raw(urldecode($_GET['redirect_url'])) : site_url('/');

        agcoupon_render_coupon_redirect_page($coupon_id, $url);
        exit;
    }

    // Check for store redirect
    if (isset($_GET['store_redirect'])) {
        $store_id = intval($_GET['store_redirect']);
        $url = isset($_GET['redirect_url']) ? esc_url_raw(urldecode($_GET['redirect_url'])) : site_url('/');

        agcoupon_render_store_redirect_page($store_id, $url);
        exit;
    }
}
add_action('template_redirect', 'agcoupon_handle_redirects');

function agcoupon_render_coupon_redirect_page($coupon_id, $url) {
    $coupon = new halacoupon_Coupon($coupon_id);

    if ($coupon->is_coupon()) {
        ?>
<!DOCTYPE html>
<html lang="<?php echo esc_attr(get_bloginfo('language')); ?>">

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="<?php echo esc_url(get_stylesheet_directory_uri() . '/assets/css/leave.css'); ?>"
		type="text/css" media="all" />
	<meta name="robots" content="noindex, nofollow" />
	<meta name="googlebot" content="noindex, nofollow">
	<title><?php esc_html_e('جاري فتح المتجر', 'halacoupon'); ?> | <?php echo esc_html(get_the_title($coupon_id)); ?>
	</title>
</head>

<body>
	<section class="redirect-page">
		<div class="loading_animate"></div>
		<span class="loading_Animate_Text"></span>
		<?php if (halacoupon_get_coupon_type($coupon_id) === 'code') : ?>
		<div class="confirmation">
			<h3 class="confirmation-text"><?php esc_html_e('تم نسخ الكوبون بنجاح', 'halacoupon'); ?></h3>
			<div class="coupon-store-logo">
				<?php
			halacoupon_setup_coupon($coupon_id);
			$store_id = halacoupon_coupon()->get_store_id();
			$store_image = get_term_meta($store_id, '_ags_store_image', true);
			$store_name = get_term_meta($store_id, '_ags_store_name', true);
			 ?>


				<?php echo '<img width="180" src="' . esc_url($store_image) . '" alt="' . esc_attr($store_name) . '" />'; ?>

			</div>


		</div>
		<span
			class="loading_store"><?php esc_html_e('قم بلصق الكوبون في صفحه الدفع واستمتع بالخصم', 'halacoupon'); ?></span>
		<code><?php echo esc_html(halacoupon_get_coupon_code($coupon_id)); ?></code>
		<p><?php esc_html_e('جاري تحويلك لصفحه المتجر', 'halacoupon'); ?></p>
		<?php else : ?>
		<h2><?php esc_html_e('لا تحتاج رمز', 'halacoupon'); ?></h2>
		<span><?php esc_html_e('تم تفعيل الصفقه داخل المتجر', 'halacoupon'); ?></span>
		<p><?php esc_html_e('استمتع بالشراء مع الخصم الفوري في الحال', 'halacoupon'); ?></p>
		<?php endif; ?>
	</section>
	<script type="text/javascript">
	document.addEventListener('DOMContentLoaded', function() {
		setTimeout(function() {
			window.location.href = '<?php echo esc_url($url); ?>';
		}, 100);
	});
	</script>
</body>

</html>
<?php
    }
}

function agcoupon_render_store_redirect_page($store_id, $url) {
    $store = new halacoupon_Store($store_id);
    $store_name = $store->get_display_name();
    ?>
<!DOCTYPE html>
<html lang="<?php echo esc_attr(get_bloginfo('language')); ?>">

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="<?php echo esc_url(get_stylesheet_directory_uri() . '/assets/css/leave.css'); ?>"
		type="text/css" media="all" />
	<meta name="robots" content="noindex, nofollow" />
	<meta name="googlebot" content="noindex, nofollow">
	<title><?php echo esc_html($store_name) . ' - ' . esc_html__('Opening Store', 'halacoupon'); ?></title>
</head>

<body>
	<section class="redirect-page">
		<div class="loading_animate"></div>
		<span class="loading_Animate_Text"></span>
		<h1><?php echo esc_html($store_name) . ' - ' . esc_html__('Opening Store', 'halacoupon'); ?></h1>
		<a rel="nofollow" href="<?php echo esc_url(get_term_meta($store->term_id, '_ags_store_aff_url', true)); ?>">
			<img width="180" src="<?php echo esc_url(get_term_meta($store->term_id, '_ags_store_image', true)); ?>"
				alt="<?php echo esc_attr($store_name); ?>" />
		</a>
		<span><?php esc_html_e('Deal Activated Successfully ... Redirecting To Store Page', 'halacoupon'); ?></span>
	</section>
	<script type="text/javascript">
	setTimeout(function() {
		window.location.href = '<?php echo esc_url($url); ?>';
	}, 100);
	</script>
</body>

</html>
<?php
}