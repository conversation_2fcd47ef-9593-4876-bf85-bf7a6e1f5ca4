<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */

get_header();
$cate_title = single_cat_title( '', false );
$cate_id =  get_queried_object_id();

?>

<div id="content-wrap" class="container">

    <div id="primary" class="content-area">
        <main id="main" class="site-main ajax-coupons" role="main">

            <section id="store-listings-wrapper" class="st-list-coupons wpb_content_element">
				<?php the_archive_title( '<h2 class="section-heading">', '</h2>' ); ?>
				<?php
				global $wp_rewrite;
				$paged =  halacoupon_get_paged();
				$args = array(
					'tax_query' => array(
						'relation' => 'AND',
						array(
							'taxonomy' => 'coupon_country',
							'field'    => 'term_id',
							'terms'    => array( $cate_id ),
							'operator' => 'IN',
						),
					),
				);

				$coupons =  halacoupon_get_coupons( $args, $paged , $max_pages );
				$current_link = $_SERVER['REQUEST_URI'];
				if (  $coupons )  {
					foreach ( $coupons as $post ) {
						halacoupon_setup_coupon( $post , $current_link );
						get_template_part( 'loop/loop-coupon' );
					}
				}

				?>
            </section>
			<?php
				if ($max_pages > $paged) { ?>
                    <div class="load-more wpb_content_element">
                        <a href="<?php next_posts($max_pages); ?>" class="ui button btn btn_primary btn_large" data-next-page="<?php echo($paged + 1); ?>"
                           data-link="<?php echo esc_attr($current_link); ?>" data-cat-id="<?php echo esc_attr( $cate_id ); ?>"
                           data-type="coupon_tag"
                           data-loading-text="<?php esc_attr_e('Loading...', 'halacoupon'); ?>"><?php esc_html_e('Load More Coupons', 'halacoupon'); ?> <i class="arrow circle outline down icon"></i></a>
                    </div>
				<?php }
			?>
        </main><!-- #main -->
    </div><!-- #primary -->

    <div id="secondary" class="widget-area sidebar" role="complementary">
		<?php
		dynamic_sidebar( 'sidebar-coupon-category' );
		?>
    </div>
</div> <!-- /#content-wrap -->

<?php
get_footer();
?>
