<?php
/**
 * Pagination Template
 * Modern pagination component with Tailwind CSS and RTL/LTR support
 */

// Get pagination data
global $wp_query;

$total_pages = $wp_query->max_num_pages;
$current_page = max(1, get_query_var('paged'));

if ($total_pages <= 1) {
    return;
}

// Get RTL status
$is_rtl = is_rtl();

// Calculate page range
$range = 2; // Number of pages to show on each side of current page
$start_page = max(1, $current_page - $range);
$end_page = min($total_pages, $current_page + $range);

// Adjust range if we're near the beginning or end
if ($current_page <= $range + 1) {
    $end_page = min($total_pages, $range * 2 + 1);
}
if ($current_page >= $total_pages - $range) {
    $start_page = max(1, $total_pages - $range * 2);
}

// RTL-aware icon classes
$prev_icon_class = $is_rtl ? 'ml-1' : 'mr-1';
$next_icon_class = $is_rtl ? 'mr-1' : 'ml-1';

?>

<nav class="pagination-container" aria-label="<?php esc_attr_e('Pagination Navigation', 'halacoupon'); ?>" dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">
    <div class="pagination">
        
        <?php if ($current_page > 1) : ?>
        <!-- Previous Page -->
        <a href="<?php echo esc_url(get_pagenum_link($current_page - 1)); ?>" 
           class="pagination-item pagination-prev"
           style="<?php echo $is_rtl ? 'border-top-right-radius: 0.75rem !important; border-bottom-right-radius: 0.75rem !important; border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important;' : 'border-top-left-radius: 0.75rem !important; border-bottom-left-radius: 0.75rem !important; border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important;'; ?>"
           aria-label="<?php esc_attr_e('Previous page', 'halacoupon'); ?>">
            <svg class="w-4 h-4 <?php echo $prev_icon_class; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $is_rtl ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'; ?>"></path>
            </svg>
            <span class="hidden sm:inline <?php echo $is_rtl ? 'mr-1' : 'ml-1'; ?>"><?php esc_html_e('Previous', 'halacoupon'); ?></span>
        </a>
        <?php else : ?>
        <span class="pagination-item pagination-prev disabled" 
              style="<?php echo $is_rtl ? 'border-top-right-radius: 0.75rem !important; border-bottom-right-radius: 0.75rem !important; border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important;' : 'border-top-left-radius: 0.75rem !important; border-bottom-left-radius: 0.75rem !important; border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important;'; ?>"
              aria-disabled="true">
            <svg class="w-4 h-4 <?php echo $prev_icon_class; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $is_rtl ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'; ?>"></path>
            </svg>
            <span class="hidden sm:inline <?php echo $is_rtl ? 'mr-1' : 'ml-1'; ?>"><?php esc_html_e('Previous', 'halacoupon'); ?></span>
        </span>
        <?php endif; ?>

        <?php if ($start_page > 1) : ?>
        <!-- First Page -->
        <a href="<?php echo esc_url(get_pagenum_link(1)); ?>" 
           class="pagination-item"
           aria-label="<?php esc_attr_e('Go to page 1', 'halacoupon'); ?>">1</a>
        
        <?php if ($start_page > 2) : ?>
        <!-- Dots -->
        <span class="pagination-dots">...</span>
        <?php endif; ?>
        <?php endif; ?>

        <?php for ($i = $start_page; $i <= $end_page; $i++) : ?>
        <?php if ($i == $current_page) : ?>
        <!-- Current Page -->
        <span class="pagination-item active" aria-current="page" aria-label="<?php echo esc_attr(sprintf(__('Page %d, current page', 'halacoupon'), $i)); ?>">
            <?php echo $i; ?>
        </span>
        <?php else : ?>
        <!-- Other Pages -->
        <a href="<?php echo esc_url(get_pagenum_link($i)); ?>" 
           class="pagination-item"
           aria-label="<?php echo esc_attr(sprintf(__('Go to page %d', 'halacoupon'), $i)); ?>">
            <?php echo $i; ?>
        </a>
        <?php endif; ?>
        <?php endfor; ?>

        <?php if ($end_page < $total_pages) : ?>
        <?php if ($end_page < $total_pages - 1) : ?>
        <!-- Dots -->
        <span class="pagination-dots">...</span>
        <?php endif; ?>
        
        <!-- Last Page -->
        <a href="<?php echo esc_url(get_pagenum_link($total_pages)); ?>" 
           class="pagination-item"
           aria-label="<?php echo esc_attr(sprintf(__('Go to page %d', 'halacoupon'), $total_pages)); ?>">
            <?php echo $total_pages; ?>
        </a>
        <?php endif; ?>

        <?php if ($current_page < $total_pages) : ?>
        <!-- Next Page -->
        <a href="<?php echo esc_url(get_pagenum_link($current_page + 1)); ?>" 
           class="pagination-item pagination-next"
           style="<?php echo $is_rtl ? 'border-top-left-radius: 0.75rem !important; border-bottom-left-radius: 0.75rem !important; border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important;' : 'border-top-right-radius: 0.75rem !important; border-bottom-right-radius: 0.75rem !important; border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important;'; ?>"
           aria-label="<?php esc_attr_e('Next page', 'halacoupon'); ?>">
            <span class="hidden sm:inline <?php echo $is_rtl ? 'ml-1' : 'mr-1'; ?>"><?php esc_html_e('Next', 'halacoupon'); ?></span>
            <svg class="w-4 h-4 <?php echo $next_icon_class; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $is_rtl ? 'M15 19l-7-7 7-7' : 'M9 5l7 7-7 7'; ?>"></path>
            </svg>
        </a>
        <?php else : ?>
        <span class="pagination-item pagination-next disabled" 
              style="<?php echo $is_rtl ? 'border-top-left-radius: 0.75rem !important; border-bottom-left-radius: 0.75rem !important; border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important;' : 'border-top-right-radius: 0.75rem !important; border-bottom-right-radius: 0.75rem !important; border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important;'; ?>"
              aria-disabled="true">
            <span class="hidden sm:inline <?php echo $is_rtl ? 'ml-1' : 'mr-1'; ?>"><?php esc_html_e('Next', 'halacoupon'); ?></span>
            <svg class="w-4 h-4 <?php echo $next_icon_class; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $is_rtl ? 'M15 19l-7-7 7-7' : 'M9 5l7 7-7 7'; ?>"></path>
            </svg>
        </span>
        <?php endif; ?>

    </div>

    <!-- Page Info -->
    <div class="pagination-info text-center mt-4 text-sm text-gray-600">
        <?php
        $start_item = ($current_page - 1) * get_option('posts_per_page') + 1;
        $end_item = min($current_page * get_option('posts_per_page'), $wp_query->found_posts);
        
        printf(
            esc_html__('Showing %1$s to %2$s of %3$s results', 'halacoupon'),
            number_format_i18n($start_item),
            number_format_i18n($end_item),
            number_format_i18n($wp_query->found_posts)
        );
        ?>
    </div>
</nav>

<script>
// Enhanced pagination functionality with RTL support
jQuery(document).ready(function($) {
    // Add keyboard navigation
    $(document).on('keydown', function(e) {
        if (e.target.tagName.toLowerCase() === 'input' || e.target.tagName.toLowerCase() === 'textarea') {
            return;
        }
        
        var currentPage = <?php echo $current_page; ?>;
        var totalPages = <?php echo $total_pages; ?>;
        var isRTL = <?php echo $is_rtl ? 'true' : 'false'; ?>;
        
        // Handle arrow keys based on RTL/LTR
        if (isRTL) {
            // RTL: Right arrow = previous page, Left arrow = next page
            if (e.keyCode === 39 && currentPage > 1) { // Right arrow
                window.location.href = '<?php echo esc_url(get_pagenum_link($current_page - 1)); ?>';
            }
            if (e.keyCode === 37 && currentPage < totalPages) { // Left arrow
                window.location.href = '<?php echo esc_url(get_pagenum_link($current_page + 1)); ?>';
            }
        } else {
            // LTR: Left arrow = previous page, Right arrow = next page
            if (e.keyCode === 37 && currentPage > 1) { // Left arrow
                window.location.href = '<?php echo esc_url(get_pagenum_link($current_page - 1)); ?>';
            }
            if (e.keyCode === 39 && currentPage < totalPages) { // Right arrow
                window.location.href = '<?php echo esc_url(get_pagenum_link($current_page + 1)); ?>';
            }
        }
    });
    
    // Add loading states
    $('.pagination-item:not(.disabled):not(.active)').on('click', function() {
        $(this).addClass('btn-loading');
    });
    
    // Smooth scroll to top on page change
    if (window.location.hash === '#pagination') {
        $('html, body').animate({
            scrollTop: $('.pagination-container').offset().top - 100
        }, 500);
    }
});
</script>
