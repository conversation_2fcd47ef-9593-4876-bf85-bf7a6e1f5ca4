<?php
/**
 * Rating Stars Component
 * Modern rating display and input component with Tailwind CSS
 */

// Default values
$rating = isset($args['rating']) ? floatval($args['rating']) : 0;
$max_rating = isset($args['max_rating']) ? intval($args['max_rating']) : 5;
$size = isset($args['size']) ? $args['size'] : 'default'; // sm, default, lg
$interactive = isset($args['interactive']) ? $args['interactive'] : false;
$show_text = isset($args['show_text']) ? $args['show_text'] : true;
$show_count = isset($args['show_count']) ? $args['show_count'] : false;
$count = isset($args['count']) ? intval($args['count']) : 0;
$id = isset($args['id']) ? $args['id'] : 'rating-' . uniqid();

// Size classes
$size_classes = [
    'sm' => 'rating-sm',
    'default' => '',
    'lg' => 'rating-lg'
];

$size_class = isset($size_classes[$size]) ? $size_classes[$size] : '';

?>

<div class="rating-container flex items-center space-x-2" data-rating-id="<?php echo esc_attr($id); ?>">
    <!-- Rating Stars -->
    <div class="rating-stars <?php echo esc_attr($size_class); ?> <?php echo $interactive ? 'rating-interactive' : ''; ?>" 
         data-rating="<?php echo esc_attr($rating); ?>" 
         data-max-rating="<?php echo esc_attr($max_rating); ?>">
        
        <?php for ($i = 1; $i <= $max_rating; $i++) : ?>
        <?php
        $star_class = 'rating-star';
        
        if ($rating >= $i) {
            $star_class .= ' filled';
        } elseif ($rating >= $i - 0.5) {
            $star_class .= ' half-filled';
        }
        ?>
        
        <svg class="<?php echo esc_attr($star_class); ?>" 
             data-rating="<?php echo $i; ?>"
             fill="currentColor" 
             viewBox="0 0 24 24"
             <?php if ($interactive) : ?>
             role="button"
             tabindex="0"
             aria-label="<?php echo esc_attr(sprintf(__('Rate %d stars', 'halacoupon'), $i)); ?>"
             <?php endif; ?>>
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
        <?php endfor; ?>
        
        <?php if ($interactive) : ?>
        <!-- Hidden input for form submission -->
        <input type="hidden" name="rating" value="<?php echo esc_attr($rating); ?>" class="rating-input">
        <?php endif; ?>
    </div>

    <?php if ($show_text || $show_count) : ?>
    <!-- Rating Text -->
    <div class="rating-text text-sm text-gray-600">
        <?php if ($show_text) : ?>
        <span class="rating-value font-semibold text-gray-900"><?php echo number_format($rating, 1); ?></span>
        <span class="rating-separator">/</span>
        <span class="rating-max"><?php echo $max_rating; ?></span>
        <?php endif; ?>
        
        <?php if ($show_count && $count > 0) : ?>
        <span class="rating-count ml-1">
            (<?php echo number_format_i18n($count); ?> 
            <?php echo _n('review', 'reviews', $count, 'halacoupon'); ?>)
        </span>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<?php if ($interactive) : ?>
<script>
// Rating interaction functionality
jQuery(document).ready(function($) {
    var $container = $('[data-rating-id="<?php echo esc_js($id); ?>"]');
    var $stars = $container.find('.rating-star');
    var $input = $container.find('.rating-input');
    var $ratingValue = $container.find('.rating-value');
    
    // Star click handler
    $stars.on('click', function() {
        var rating = $(this).data('rating');
        setRating(rating);
    });
    
    // Star hover handler
    $stars.on('mouseenter', function() {
        var rating = $(this).data('rating');
        previewRating(rating);
    });
    
    // Container mouse leave handler
    $container.find('.rating-stars').on('mouseleave', function() {
        var currentRating = $input.val();
        setRating(currentRating);
    });
    
    // Keyboard navigation
    $stars.on('keydown', function(e) {
        var currentIndex = $stars.index(this);
        var newIndex = currentIndex;
        
        switch(e.keyCode) {
            case 37: // Left arrow
                newIndex = Math.max(0, currentIndex - 1);
                break;
            case 39: // Right arrow
                newIndex = Math.min($stars.length - 1, currentIndex + 1);
                break;
            case 13: // Enter
            case 32: // Space
                e.preventDefault();
                $(this).click();
                return;
        }
        
        if (newIndex !== currentIndex) {
            e.preventDefault();
            $stars.eq(newIndex).focus();
        }
    });
    
    function setRating(rating) {
        $stars.each(function(index) {
            var $star = $(this);
            if (index < rating) {
                $star.addClass('filled').removeClass('half-filled preview-filled');
            } else {
                $star.removeClass('filled half-filled preview-filled');
            }
        });
        
        $input.val(rating);
        if ($ratingValue.length) {
            $ratingValue.text(rating + '.0');
        }
        
        // Trigger custom event
        $container.trigger('ratingChanged', [rating]);
    }
    
    function previewRating(rating) {
        $stars.each(function(index) {
            var $star = $(this);
            if (index < rating) {
                $star.addClass('preview-filled');
            } else {
                $star.removeClass('preview-filled');
            }
        });
    }
});
</script>
<?php endif; ?>

<style>
/* Additional rating styles */
.rating-star.preview-filled {
    color: #f59e0b;
    transform: scale(1.1);
}

.rating-star:focus {
    outline: 2px solid #ea580c;
    outline-offset: 2px;
    border-radius: 2px;
}

.rating-interactive .rating-star {
    cursor: pointer;
}

.rating-interactive .rating-star:hover {
    transform: scale(1.1);
}

/* Half-filled star effect */
.rating-star.half-filled {
    position: relative;
}

.rating-star.half-filled::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    background: #fbbf24;
    z-index: 1;
}

/* Animation for rating changes */
.rating-stars {
    transition: all 0.2s ease;
}

.rating-star {
    transition: all 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .rating-text {
        font-size: 0.75rem;
    }
    
    .rating-sm .rating-star {
        width: 0.75rem;
        height: 0.75rem;
    }
    
    .rating-lg .rating-star {
        width: 1rem;
        height: 1rem;
    }
}
</style>
