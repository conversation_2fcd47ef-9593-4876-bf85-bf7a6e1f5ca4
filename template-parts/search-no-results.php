<?php
/**
 * Template part for displaying no search results
 * Creative and helpful no-results page with suggestions
 * 
 * @package HalaCoupon
 * @version 2.0
 */

$search_query = get_search_query();
?>

<!-- No Results Section -->
<div class="text-center py-16 md:py-24" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
    
    <!-- Illustration -->
    <div class="mb-8">
        <div class="inline-flex items-center justify-center w-32 h-32 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-full mb-6">
            <svg class="w-16 h-16 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0V3a1 1 0 00-1-1h-4a1 1 0 00-1 1v3.306z"></path>
            </svg>
        </div>
    </div>
    
    <!-- Main Message -->
    <div class="max-w-2xl mx-auto mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-text mb-4">
            <?php esc_html_e('No Results Found', 'halacoupon'); ?>
        </h2>
        <p class="text-lg text-text/70 mb-6">
            <?php printf(
                __('We couldn\'t find anything matching "%s". Try adjusting your search or browse our suggestions below.', 'halacoupon'),
                '<span class="font-semibold text-primary">' . esc_html($search_query) . '</span>'
            ); ?>
        </p>
    </div>
    
    <!-- Search Suggestions -->
    <div class="max-w-4xl mx-auto mb-16">
        <h3 class="text-xl font-semibold text-text mb-6"><?php esc_html_e('Search Suggestions', 'halacoupon'); ?></h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
            
            <!-- Search Tips -->
            <div class="glass-card rounded-2xl p-6 border border-white/30">
                <div class="flex items-start gap-4">
                    <div class="flex-shrink-0 w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-semibold text-text mb-2"><?php esc_html_e('Search Tips', 'halacoupon'); ?></h4>
                        <ul class="text-sm text-text/70 space-y-1">
                            <li>• <?php esc_html_e('Try different keywords', 'halacoupon'); ?></li>
                            <li>• <?php esc_html_e('Use more general terms', 'halacoupon'); ?></li>
                            <li>• <?php esc_html_e('Check your spelling', 'halacoupon'); ?></li>
                            <li>• <?php esc_html_e('Try fewer words', 'halacoupon'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Popular Searches -->
            <div class="glass-card rounded-2xl p-6 border border-white/30">
                <div class="flex items-start gap-4">
                    <div class="flex-shrink-0 w-10 h-10 bg-gradient-secondary rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-semibold text-text mb-2"><?php esc_html_e('Popular Searches', 'halacoupon'); ?></h4>
                        <div class="flex flex-wrap gap-2">
                            <?php
                            // Get popular search terms (you can customize this)
                            $popular_terms = ['discount', 'sale', 'coupon', 'deal', 'offer', 'promo'];
                            foreach ($popular_terms as $term) :
                            ?>
                            <a href="<?php echo esc_url(home_url('/?s=' . urlencode($term))); ?>" 
                               class="inline-block px-3 py-1 bg-primary/10 text-primary text-sm rounded-full hover:bg-primary/20 transition-colors duration-200">
                                <?php echo esc_html($term); ?>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
    
    <!-- Browse Categories -->
    <div class="max-w-6xl mx-auto mb-16">
        <h3 class="text-xl font-semibold text-text mb-8"><?php esc_html_e('Browse by Category', 'halacoupon'); ?></h3>
        
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <?php
            // Get popular categories
            $categories = get_terms([
                'taxonomy' => 'coupon_category',
                'number' => 12,
                'hide_empty' => true,
                'orderby' => 'count',
                'order' => 'DESC'
            ]);
            
            if ($categories && !is_wp_error($categories)) :
                foreach ($categories as $category) :
            ?>
            <a href="<?php echo esc_url(get_term_link($category)); ?>" 
               class="group flex flex-col items-center p-4 glass-card rounded-xl border border-white/30 hover:border-primary/40 hover:scale-105 transition-all duration-300">
                
                <!-- Category Icon -->
                <div class="w-12 h-12 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center mb-3 group-hover:from-primary/20 group-hover:to-secondary/20 transition-all duration-300">
                    <svg class="w-6 h-6 text-primary" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                        <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                
                <!-- Category Name -->
                <span class="text-sm font-medium text-text group-hover:text-primary transition-colors duration-200 text-center">
                    <?php echo esc_html($category->name); ?>
                </span>
                
                <!-- Count -->
                <span class="text-xs text-text/60 mt-1">
                    <?php printf(_n('%d item', '%d items', $category->count, 'halacoupon'), $category->count); ?>
                </span>
            </a>
            <?php 
                endforeach;
            endif;
            ?>
        </div>
    </div>
    
    <!-- Featured Content -->
    <div class="max-w-4xl mx-auto">
        <h3 class="text-xl font-semibold text-text mb-8"><?php esc_html_e('You Might Also Like', 'halacoupon'); ?></h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php
            // Get featured posts/coupons
            $featured_query = new WP_Query([
                'post_type' => ['post', 'coupon'],
                'posts_per_page' => 3,
                'meta_query' => [
                    [
                        'key' => '_ags_featured',
                        'value' => 'on',
                        'compare' => '='
                    ]
                ]
            ]);
            
            if ($featured_query->have_posts()) :
                while ($featured_query->have_posts()) : $featured_query->the_post();
            ?>
            <article class="group glass-card rounded-xl border border-white/30 hover:border-primary/40 hover:scale-105 transition-all duration-300 overflow-hidden">
                
                <?php if (has_post_thumbnail()) : ?>
                <div class="aspect-video overflow-hidden">
                    <?php the_post_thumbnail('medium', [
                        'class' => 'w-full h-full object-cover group-hover:scale-110 transition-transform duration-500'
                    ]); ?>
                </div>
                <?php endif; ?>
                
                <div class="p-4">
                    <h4 class="font-semibold text-text group-hover:text-primary transition-colors duration-200 mb-2 line-clamp-2">
                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                    </h4>
                    <p class="entry-content text-sm text-text/70 line-clamp-2 mb-3">
                        <?php echo wp_trim_words(get_the_excerpt(), 15); ?>
                    </p>
                    <a href="<?php the_permalink(); ?>" 
                       class="inline-flex items-center gap-1 text-sm text-primary font-medium hover:text-primary-600 transition-colors duration-200">
                        <?php esc_html_e('Learn More', 'halacoupon'); ?>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </article>
            <?php 
                endwhile;
                wp_reset_postdata();
            endif;
            ?>
        </div>
    </div>
    
    <!-- Contact Support -->
    <div class="max-w-2xl mx-auto mt-16 text-center">
        <div class="glass-card rounded-2xl p-8 border border-white/30">
            <h3 class="text-xl font-semibold text-text mb-4"><?php esc_html_e('Still Need Help?', 'halacoupon'); ?></h3>
            <p class="text-text/70 mb-6">
                <?php esc_html_e('Our support team is here to help you find what you\'re looking for.', 'halacoupon'); ?>
            </p>
            <a href="<?php echo esc_url(home_url('/contact')); ?>" 
               class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-primary text-white font-semibold rounded-xl hover:shadow-glow hover:scale-105 transition-all duration-300">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                </svg>
                <?php esc_html_e('Contact Support', 'halacoupon'); ?>
            </a>
        </div>
    </div>
    
</div>

<style>
/* Glass morphism effects */
.glass-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
}

.glass-card:hover {
    backdrop-filter: blur(15px);
    background: rgba(255, 255, 255, 0.9);
}

/* Line clamp utility */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
