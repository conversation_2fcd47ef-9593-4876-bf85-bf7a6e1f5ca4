<?php
/**
 * Template Part: Global Coupon Modal
 *
 * @package HalaCoupon
 *
 */
?>

<div id="global-coupon-modal"
     role="dialog"
     aria-modal="true"
     aria-labelledby="modal-title"
     aria-describedby="modal-content"
     tabindex="-1"
     onclick="if(event.target === this) closeSimpleModal()">
    <div class="global-coupon-modal__container" onclick="event.stopPropagation()">
        <!-- Header -->
        <div class="global-coupon-modal-header flex gap-4 items-center backdrop-blur-md ps-10 pe-5 pt-5 pb-5">
            <button class="global-coupon-modal__close flex-shrink-0"
                    onclick="closeSimpleModal()"
                    aria-label="Close modal"
                    onmouseover="this.style.background='rgba(255,255,255,0.35)'; this.style.transform='scale(1.1)'"
                    onmouseout="this.style.background='rgba(255,255,255,0.25)'; this.style.transform='scale(1)'">
                <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <div id="modal-title" class="global-coupon-modal__title flex-grow-1">
                <div style="font-size: 24px; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">✨ <?php if(function_exists('esc_html_e')){esc_html_e('Coupon Details', 'halacoupon');}else{echo 'Coupon Details';} ?></div>
            </div>
        </div>

        <!-- Feedback area for screen readers -->
        <div class="modal-feedback" aria-live="polite" style="position:absolute;left:-9999px;top:auto;width:1px;height:1px;overflow:hidden;"></div>
        <!-- Visible feedback area (toast/alert) -->
        <div class="modal-feedback-visible" style="display:none;position:absolute;top:20px;left:50%;transform:translateX(-50%);z-index:1000;"></div>

        <!-- Content -->
        <div id="modal-content" class="global-coupon-modal__content">
            <div class="global-coupon-modal__loading">
                <div class="icon">🎁</div>
                <div class="title"><?php if(function_exists('esc_html_e')){esc_html_e('Loading Amazing Deal...', 'halacoupon');}else{echo 'Loading Amazing Deal...';} ?></div>
                <div class="desc"><?php if(function_exists('esc_html_e')){esc_html_e('Please wait while we prepare your coupon', 'halacoupon');}else{echo 'Please wait while we prepare your coupon';} ?></div>
            </div>
        </div>
    </div>
</div>

<?php
