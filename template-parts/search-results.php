<?php
/**
 * Template part for displaying search results
 * Modern card-based layout with glass morphism design
 * 
 * @package HalaCoupon
 * @version 2.0
 */

// Get current post type for different layouts
$post_type = get_post_type();
$post_index = 0;
?>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
    
    <?php while (have_posts()) : the_post(); $post_index++; ?>
        
        <article <?php post_class('group relative glass-card rounded-2xl border border-white/30 hover:border-primary/40 transition-all duration-500 hover:shadow-card-hover hover:scale-105 overflow-hidden animate-fade-in-up'); ?> 
                 itemscope itemtype="https://schema.org/<?php echo ($post_type === 'coupon') ? 'Offer' : 'Article'; ?>"
                 style="animation-delay: <?php echo ($post_index * 0.1); ?>s;">
            
            <!-- Featured Image -->
            <?php if (has_post_thumbnail()) : ?>
            <div class="relative aspect-video overflow-hidden">
                <?php 
                the_post_thumbnail('medium_large', [
                    'class' => 'w-full h-full object-cover group-hover:scale-110 transition-transform duration-700',
                    'loading' => 'lazy',
                    'itemprop' => 'image'
                ]); 
                ?>
                
                <!-- Image Overlay -->
                <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                <!-- Post Type Badge -->
                <div class="absolute top-3 <?php echo is_rtl() ? 'right-3' : 'left-3'; ?>">
                    <?php if ($post_type === 'coupon') : ?>
                        <span class="inline-flex items-center gap-1 px-3 py-1 bg-gradient-secondary text-white text-xs font-semibold rounded-full">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"></path>
                                <path fill-rule="evenodd" d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <?php esc_html_e('Coupon', 'halacoupon'); ?>
                        </span>
                    <?php else : ?>
                        <span class="inline-flex items-center gap-1 px-3 py-1 bg-gradient-primary text-white text-xs font-semibold rounded-full">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path fill-rule="evenodd" d="M2 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 002 2H4a2 2 0 01-2-2V5zm3 1h6v4H5V6zm6 6H5v2h6v-2z" clip-rule="evenodd"></path>
                                <path d="M15 7h1a2 2 0 012 2v5.5a1.5 1.5 0 01-3 0V7z"></path>
                            </svg>
                            <?php esc_html_e('Article', 'halacoupon'); ?>
                        </span>
                    <?php endif; ?>
                </div>
                
                <!-- Reading Time (for articles) -->
                <?php if ($post_type !== 'coupon') : ?>
                <div class="absolute top-3 <?php echo is_rtl() ? 'left-3' : 'right-3'; ?>">
                    <span class="inline-flex items-center gap-1 px-2 py-1 bg-black/50 backdrop-blur-sm text-white text-xs rounded-full">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        <?php echo halacoupon_get_reading_time(); ?> <?php esc_html_e('min', 'halacoupon'); ?>
                    </span>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <!-- Card Content -->
            <div class="p-6 space-y-4">
                
                <!-- Meta Information -->
                <div class="flex items-center gap-3 text-sm text-text/70">
                    <!-- Date -->
                    <span class="flex items-center gap-1">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        <time datetime="<?php echo esc_attr(get_the_date('c')); ?>" itemprop="datePublished">
                            <?php echo esc_html(get_the_date()); ?>
                        </time>
                    </span>
                    
                    <!-- Author (for articles) -->
                    <?php if ($post_type !== 'coupon') : ?>
                    <span class="text-text/50">•</span>
                    <span class="flex items-center gap-1" itemprop="author" itemscope itemtype="https://schema.org/Person">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                        <span itemprop="name"><?php echo esc_html(get_the_author()); ?></span>
                    </span>
                    <?php endif; ?>
                    
                    <!-- Category -->
                    <?php 
                    $categories = ($post_type === 'coupon') ? get_the_terms(get_the_ID(), 'coupon_category') : get_the_category();
                    if ($categories && !is_wp_error($categories)) : 
                        $first_category = $categories[0];
                    ?>
                    <span class="text-text/50">•</span>
                    <a href="<?php echo esc_url(get_term_link($first_category)); ?>" 
                       class="flex items-center gap-1 hover:text-primary transition-colors duration-200">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path>
                        </svg>
                        <span><?php echo esc_html($first_category->name); ?></span>
                    </a>
                    <?php endif; ?>
                </div>
                
                <!-- Title -->
                <h3 class="text-xl font-bold text-text group-hover:text-primary transition-colors duration-300 leading-tight" itemprop="headline">
                    <a href="<?php the_permalink(); ?>" class="hover:underline">
                        <?php the_title(); ?>
                    </a>
                </h3>
                
                <!-- Excerpt -->
                <div class="entry-content text-text/80 line-clamp-3" itemprop="description">
                    <?php
                    if (has_excerpt()) {
                        the_excerpt();
                    } else {
                        echo wp_trim_words(get_the_content(), 20, '...');
                    }
                    ?>
                </div>
                
                <!-- Coupon Specific Content -->
                <?php if ($post_type === 'coupon') : ?>
                <div class="flex items-center justify-between pt-2 border-t border-gray-100">
                    <!-- Discount Value -->
                    <?php 
                    $discount_value = get_post_meta(get_the_ID(), '_ags_coupon_discount_value', true);
                    if ($discount_value) :
                    ?>
                    <div class="flex items-center gap-2">
                        <span class="text-2xl font-bold text-primary"><?php echo esc_html($discount_value); ?></span>
                        <span class="text-sm text-text/60"><?php esc_html_e('OFF', 'halacoupon'); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Store -->
                    <?php 
                    $stores = get_the_terms(get_the_ID(), 'coupon_store');
                    if ($stores && !is_wp_error($stores)) :
                        $store = $stores[0];
                    ?>
                    <div class="text-sm text-text/70">
                        <?php esc_html_e('at', 'halacoupon'); ?> 
                        <span class="font-semibold text-primary"><?php echo esc_html($store->name); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
                <!-- Call to Action -->
                <div class="pt-2">
                    <a href="<?php the_permalink(); ?>" 
                       class="inline-flex items-center gap-2 text-primary font-semibold hover:text-primary-600 transition-colors duration-200 group/cta">
                        <?php if ($post_type === 'coupon') : ?>
                            <?php esc_html_e('Get Coupon', 'halacoupon'); ?>
                        <?php else : ?>
                            <?php esc_html_e('Read More', 'halacoupon'); ?>
                        <?php endif; ?>
                        <svg class="w-4 h-4 <?php echo is_rtl() ? 'group-hover/cta:-translate-x-1' : 'group-hover/cta:translate-x-1'; ?> transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo is_rtl() ? 'M7 16l-4-4m0 0l4-4m-4 4h18' : 'M17 8l4 4m0 0l-4 4m4-4H3'; ?>"></path>
                        </svg>
                    </a>
                </div>
                
            </div>
            
        </article>
        
    <?php endwhile; ?>
    
</div>

<!-- Load More Button (if needed) -->
<?php if ($GLOBALS['wp_query']->max_num_pages > 1) : ?>
<div class="text-center mt-12">
    <button id="load-more-results" 
            class="inline-flex items-center gap-3 px-8 py-4 bg-gradient-primary text-white font-semibold rounded-2xl hover:shadow-glow hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary/30"
            data-page="<?php echo get_query_var('paged') ? get_query_var('paged') : 1; ?>"
            data-max-pages="<?php echo $GLOBALS['wp_query']->max_num_pages; ?>">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <?php esc_html_e('Load More Results', 'halacoupon'); ?>
    </button>
</div>
<?php endif; ?>

<style>
/* Enhanced animations and effects */
.search-results-grid .glass-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
}

.search-results-grid .glass-card:hover {
    backdrop-filter: blur(15px);
    background: rgba(255, 255, 255, 0.9);
}

/* Line clamp utility */
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Smooth loading animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}
</style>
