<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */


get_header();

// Get search query and results count
$search_query = get_search_query();
$results_count = $GLOBALS['wp_query']->found_posts;
$current_page = max(1, get_query_var('paged'));
$total_pages = $GLOBALS['wp_query']->max_num_pages;
?>

<!-- Search Results Schema -->
<div itemscope itemtype="https://schema.org/SearchResultsPage">

    <!-- Modern Hero Section -->
    <section class="relative py-16 md:py-24 bg-gradient-primary overflow-hidden">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0">
            <div class="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl animate-float"></div>
            <div class="absolute bottom-10 right-10 w-24 h-24 bg-primary/20 rounded-full blur-lg animate-float" style="animation-delay: 2s;"></div>
            <div class="absolute top-1/2 left-1/3 w-16 h-16 bg-accent/15 rounded-full blur-md animate-float" style="animation-delay: 4s;"></div>
        </div>

        <!-- Hero Content -->
        <div class="relative container mx-auto px-4 text-center text-white">
            <div class="max-w-4xl mx-auto">
                <!-- Search Icon -->
                <div class="inline-flex items-center justify-center w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl mb-6">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>

                <!-- Title -->
                <h1 class="text-3xl md:text-5xl font-bold mb-4 leading-tight" itemprop="name">
                    <?php if ($results_count > 0) : ?>
                        <?php printf(__('Search Results', 'halacoupon')); ?>
                    <?php else : ?>
                        <?php printf(__('No Results Found', 'halacoupon')); ?>
                    <?php endif; ?>
                </h1>

                <!-- Search Query Display -->
                <div class="inline-flex items-center gap-3 bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 mb-6" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
                    <span class="text-white/80 text-sm md:text-base"><?php esc_html_e('Searching for:', 'halacoupon'); ?></span>
                    <span class="font-semibold text-lg">"<?php echo esc_html($search_query); ?>"</span>
                </div>

                <!-- Results Count -->
                <?php if ($results_count > 0) : ?>
                <p class="text-lg md:text-xl text-white/90 mb-8">
                    <?php printf(
                        _n(
                            'Found %s result',
                            'Found %s results',
                            $results_count,
                            'halacoupon'
                        ),
                        '<span class="font-bold text-secondary">' . number_format($results_count) . '</span>'
                    ); ?>
                </p>
                <?php endif; ?>

                <!-- New Search Form -->
                <div class="max-w-md mx-auto">
                    <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>" class="relative" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
                        <input type="search"
                               name="s"
                               value="<?php echo esc_attr($search_query); ?>"
                               placeholder="<?php esc_attr_e('Try a different search...', 'halacoupon'); ?>"
                               class="w-full px-6 py-4 <?php echo is_rtl() ? 'pl-14 pr-6' : 'pr-14 pl-6'; ?> bg-white/90 backdrop-blur-sm rounded-2xl border border-white/30 focus:border-primary focus:ring-2 focus:ring-primary/30 focus:outline-none transition-all duration-300 text-text placeholder-text/60"
                               aria-label="<?php esc_attr_e('Search', 'halacoupon'); ?>">
                        <button type="submit"
                                class="absolute <?php echo is_rtl() ? 'left-2' : 'right-2'; ?> top-1/2 -translate-y-1/2 w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center text-white hover:scale-105 transition-transform duration-200"
                                aria-label="<?php esc_attr_e('Submit search', 'halacoupon'); ?>">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content Area -->
    <main class="relative py-12 md:py-20 bg-background min-h-screen">
        <div class="container mx-auto px-4">

            <?php if (have_posts()) : ?>

                <!-- Results Header with Filters -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
                    <!-- Results Info -->
                    <div class="flex items-center gap-4">
                        <h2 class="text-xl font-semibold text-text">
                            <?php printf(__('Page %d of %d', 'halacoupon'), $current_page, $total_pages); ?>
                        </h2>
                        <span class="text-text/60">•</span>
                        <span class="text-text/80"><?php printf(__('%s total results', 'halacoupon'), number_format($results_count)); ?></span>
                    </div>

                    <!-- Sort Options -->
                    <div class="flex items-center gap-3">
                        <label for="sort-results" class="text-sm font-medium text-text/80"><?php esc_html_e('Sort by:', 'halacoupon'); ?></label>
                        <select id="sort-results" class="px-4 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:border-primary focus:ring-2 focus:ring-primary/30 focus:outline-none transition-all duration-200" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
                            <option value="relevance"><?php esc_html_e('Relevance', 'halacoupon'); ?></option>
                            <option value="date"><?php esc_html_e('Date', 'halacoupon'); ?></option>
                            <option value="title"><?php esc_html_e('Title', 'halacoupon'); ?></option>
                        </select>
                    </div>
                </div>

                <!-- Search Results Grid -->
                <div class="search-results-grid" itemscope itemtype="https://schema.org/ItemList">
                    <?php get_template_part('template-parts/search', 'results'); ?>
                </div>

                <!-- Enhanced Pagination -->
                <div class="mt-16">
                    <?php get_template_part('content', 'paging'); ?>
                </div>

            <?php else : ?>

                <!-- No Results Section -->
                <?php get_template_part('template-parts/search', 'no-results'); ?>

            <?php endif; ?>

        </div>
    </main>

</div>

<?php get_footer(); ?>