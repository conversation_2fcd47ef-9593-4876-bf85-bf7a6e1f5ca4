/* 🎨 GRADIENT FIXES FOR MISSING TAILWIND CLASSES */
/* This file ensures all custom gradient classes are available */

/* ========================================================================== */
/*   CUSTOM BRAND GRADIENT CLASSES - MISSING FROM TAILWIND V4 BUILD
/* ========================================================================== */

/* Primary Gradient - Golden */
.bg-gradient-primary {
  background-image: linear-gradient(135deg, #ebbf43 0%, #f4d156 100%);
}

/* Secondary Gradient - Secondary to Primary */
.bg-gradient-secondary {
  background-image: linear-gradient(135deg, #ffbc02 0%, #ebbf43 100%);
}

/* CTA Gradient - Call to Action */
.bg-gradient-cta {
  background-image: linear-gradient(135deg, #ffbc02 0%, #f4d156 50%, #ebbf43 100%);
}

/* Sunset Gradient - Multi-stop Brand Gradient */
.bg-gradient-sunset {
  background-image: linear-gradient(135deg, #ebbf43 0%, #ffbc02 50%, #f4d156 100%);
}

/* Accent Gradient - Accent Color */
.bg-gradient-accent {
  background-image: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

/* Dark Hero Gradient - Gray to Black to Gray */
.bg-gradient-to-br.from-gray-900.via-black.to-gray-800 {
  background-image: linear-gradient(to bottom right, #111827 0%, #000000 50%, #1f2937 100%);
}

/* Alternative Dark Gradients for Hero */
.bg-gradient-dark-hero {
  background-image: linear-gradient(135deg, #111827 0%, #000000 50%, #1f2937 100%);
}

.bg-gradient-dark-glass {
  background-image: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
}

.bg-gradient-dark-premium {
  background-image: linear-gradient(135deg, #000000 0%, #111827 50%, #1f2937 100%);
}

/* ========================================================================== */
/*   CUSTOM SHADOW CLASSES - MISSING FROM TAILWIND V4 BUILD
/* ========================================================================== */

/* Soft Shadow */
.shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

/* Glow Shadows */
.shadow-glow {
  box-shadow: 0 0 20px rgba(235, 191, 67, 0.3);
}

.shadow-glow-secondary {
  box-shadow: 0 0 20px rgba(255, 188, 2, 0.3);
}

/* Card Shadows */
.shadow-card {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.shadow-card-hover {
  box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
}

/* Float Shadows */
.shadow-float {
  box-shadow: 0 4px 20px -2px rgba(0, 0, 0, 0.1), 0 2px 8px -2px rgba(0, 0, 0, 0.06);
}

.shadow-float-lg {
  box-shadow: 0 8px 30px -4px rgba(0, 0, 0, 0.15), 0 4px 12px -4px rgba(0, 0, 0, 0.1);
}

/* Medium Shadow */
.shadow-medium {
  box-shadow: 0 6px 20px -6px rgba(0, 0, 0, 0.15), 0 4px 16px -4px rgba(0, 0, 0, 0.1);
}

/* ========================================================================== */
/*   BRAND COLOR SHADES - MISSING FROM TAILWIND V4 BUILD
/* ========================================================================== */

/* Primary Color Shades (#EBBF43) */
.bg-primary { background-color: #ebbf43; }
.bg-primary-50 { background-color: #fefbf3; }
.bg-primary-100 { background-color: #fef7e6; }
.bg-primary-200 { background-color: #fdecc0; }
.bg-primary-300 { background-color: #fbe09a; }
.bg-primary-400 { background-color: #f8d474; }
.bg-primary-500 { background-color: #ebbf43; }
.bg-primary-600 { background-color: #d4a93c; }
.bg-primary-700 { background-color: #bd9335; }
.bg-primary-800 { background-color: #a67d2e; }
.bg-primary-900 { background-color: #8f6727; }


.text-primary { color: #ebbf43; }
.text-primary-50 { color: #fefbf3; }
.text-primary-100 { color: #fef7e6; }
.text-primary-200 { color: #fdecc0; }
.text-primary-300 { color: #fbe09a; }
.text-primary-400 { color: #f8d474; }
.text-primary-500 { color: #ebbf43; }
.text-primary-600 { color: #d4a93c; }
.text-primary-700 { color: #bd9335; }
.text-primary-800 { color: #a67d2e; }
.text-primary-900 { color: #8f6727; }

.border-primary-50 { border-color: #fefbf3; }
.border-primary-100 { border-color: #fef7e6; }
.border-primary-200 { border-color: #fdecc0; }
.border-primary-300 { border-color: #fbe09a; }
.border-primary-400 { border-color: #f8d474; }
.border-primary-500 { border-color: #ebbf43; }
.border-primary-600 { border-color: #d4a93c; }
.border-primary-700 { border-color: #bd9335; }
.border-primary-800 { border-color: #a67d2e; }
.border-primary-900 { border-color: #8f6727; }

/* Secondary Color Shades (#FFBC02) */
.bg-secondary { background-color: #ffbc02; }
.bg-secondary-50 { background-color: #fffbf0; }
.bg-secondary-100 { background-color: #fff6e0; }
.bg-secondary-200 { background-color: #ffecb3; }
.bg-secondary-300 { background-color: #ffe285; }
.bg-secondary-400 { background-color: #ffd858; }
.bg-secondary-500 { background-color: #ffbc02; }
.bg-secondary-600 { background-color: #e6a902; }
.bg-secondary-700 { background-color: #cc9602; }
.bg-secondary-800 { background-color: #b38302; }
.bg-secondary-900 { background-color: #997001; }

.text-secondary-50 { color: #fffbf0; }
.text-secondary-100 { color: #fff6e0; }
.text-secondary-200 { color: #ffecb3; }
.text-secondary-300 { color: #ffe285; }
.text-secondary-400 { color: #ffd858; }
.text-secondary-500 { color: #ffbc02; }
.text-secondary-600 { color: #e6a902; }
.text-secondary-700 { color: #cc9602; }
.text-secondary-800 { color: #b38302; }
.text-secondary-900 { color: #997001; }

.border-secondary-50 { border-color: #fffbf0; }
.border-secondary-100 { border-color: #fff6e0; }
.border-secondary-200 { border-color: #ffecb3; }
.border-secondary-300 { border-color: #ffe285; }
.border-secondary-400 { border-color: #ffd858; }
.border-secondary-500 { border-color: #ffbc02; }
.border-secondary-600 { border-color: #e6a902; }
.border-secondary-700 { border-color: #cc9602; }
.border-secondary-800 { border-color: #b38302; }
.border-secondary-900 { border-color: #997001; }

/* Accent Color Shades (#000000) */
.bg-accent-50 { background-color: #f8f8f8; }
.bg-accent-100 { background-color: #f0f0f0; }
.bg-accent-200 { background-color: #e0e0e0; }
.bg-accent-300 { background-color: #c0c0c0; }
.bg-accent-400 { background-color: #a0a0a0; }
.bg-accent-500 { background-color: #808080; }
.bg-accent-600 { background-color: #606060; }
.bg-accent-700 { background-color: #404040; }
.bg-accent-800 { background-color: #202020; }
.bg-accent-900 { background-color: #000000; }

.text-accent-50 { color: #f8f8f8; }
.text-accent-100 { color: #f0f0f0; }
.text-accent-200 { color: #e0e0e0; }
.text-accent-300 { color: #c0c0c0; }
.text-accent-400 { color: #a0a0a0; }
.text-accent-500 { color: #808080; }
.text-accent-600 { color: #606060; }
.text-accent-700 { color: #404040; }
.text-accent-800 { color: #202020; }
.text-accent-900 { color: #000000; }

.border-accent-50 { border-color: #f8f8f8; }
.border-accent-100 { border-color: #f0f0f0; }
.border-accent-200 { border-color: #e0e0e0; }
.border-accent-300 { border-color: #c0c0c0; }
.border-accent-400 { border-color: #a0a0a0; }
.border-accent-500 { border-color: #808080; }
.border-accent-600 { border-color: #606060; }
.border-accent-700 { border-color: #404040; }
.border-accent-800 { border-color: #202020; }
.border-accent-900 { border-color: #000000; }

/* Gray Color Shades for Dark Gradients */
.bg-gray-900 { background-color: #111827; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-600 { background-color: #4b5563; }
.bg-gray-500 { background-color: #6b7280; }
.bg-gray-400 { background-color: #9ca3af; }
.bg-gray-300 { background-color: #d1d5db; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-50 { background-color: #f9fafb; }

.text-gray-900 { color: #111827; }
.text-gray-800 { color: #1f2937; }
.text-gray-700 { color: #374151; }
.text-gray-600 { color: #4b5563; }
.text-gray-500 { color: #6b7280; }
.text-gray-400 { color: #9ca3af; }
.text-gray-300 { color: #d1d5db; }
.text-gray-200 { color: #e5e7eb; }
.text-gray-100 { color: #f3f4f6; }
.text-gray-50 { color: #f9fafb; }

.border-gray-900 { border-color: #111827; }
.border-gray-800 { border-color: #1f2937; }
.border-gray-700 { border-color: #374151; }
.border-gray-600 { border-color: #4b5563; }
.border-gray-500 { border-color: #6b7280; }
.border-gray-400 { border-color: #9ca3af; }
.border-gray-300 { border-color: #d1d5db; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-100 { border-color: #f3f4f6; }
.border-gray-50 { border-color: #f9fafb; }



/* Background Opacity Classes */
.bg-white\/90 { background-color: rgb(255 255 255 / 0.9); }
.bg-white\/95 { background-color: rgb(255 255 255 / 0.95); }
.bg-white\/80 { background-color: rgb(255 255 255 / 0.8); }
.bg-white\/70 { background-color: rgb(255 255 255 / 0.7); }
.bg-white\/60 { background-color: rgb(255 255 255 / 0.6); }
.bg-white\/50 { background-color: rgb(255 255 255 / 0.5); }
.bg-white\/40 { background-color: rgb(255 255 255 / 0.4); }
.bg-white\/30 { background-color: rgb(255 255 255 / 0.3); }
.bg-white\/20 { background-color: rgb(255 255 255 / 0.2); }
.bg-white\/10 { background-color: rgb(255 255 255 / 0.1); }
.bg-white\/5 { background-color: rgb(255 255 255 / 0.05); }

.bg-black\/60 { background-color: rgb(0 0 0 / 0.6); }
.bg-black\/50 { background-color: rgb(0 0 0 / 0.5); }

/* Border Opacity Classes */
.border-white\/10 { border-color: rgb(255 255 255 / 0.1); }
.border-white\/20 { border-color: rgb(255 255 255 / 0.2); }
.border-white\/30 { border-color: rgb(255 255 255 / 0.3); }
.border-primary\/10 { border-color: rgb(235 191 67 / 0.1); }
.border-primary\/20 { border-color: rgb(235 191 67 / 0.2); }
.border-primary\/30 { border-color: rgb(235 191 67 / 0.3); }
.border-secondary\/10 { border-color: rgb(255 188 2 / 0.1); }
.border-secondary\/20 { border-color: rgb(255 188 2 / 0.2); }
.border-secondary\/30 { border-color: rgb(255 188 2 / 0.3); }
.border-accent\/10 { border-color: rgb(0 0 0 / 0.1); }
.border-accent\/20 { border-color: rgb(0 0 0 / 0.2); }
.border-accent\/30 { border-color: rgb(0 0 0 / 0.3); }

/* Animation Classes */
.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-slide-out-right {
  animation: slideOutRight 0.3s ease-in;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-scale-out {
  animation: scaleOut 0.2s ease-in;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

/* Keyframes */
@keyframes slideInRight {
  0% { transform: translateX(100%); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
  0% { transform: translateX(0); opacity: 1; }
  100% { transform: translateX(100%); opacity: 0; }
}

@keyframes scaleIn {
  0% { transform: scale(0.9); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes scaleOut {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(0.9); opacity: 0; }
}

@keyframes fadeInUp {
  0% { transform: translateY(30px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes fadeInDown {
  0% { transform: translateY(-30px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes fadeInLeft {
  0% { transform: translateX(-30px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes fadeInRight {
  0% { transform: translateX(30px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

/* ========================================================================== */
/*   GRADIENT TEXT CLASSES - FOR HERO TITLES
/* ========================================================================== */

/* White to Yellow Gradient Text */
.bg-gradient-to-r.from-white.via-yellow-200.to-white.bg-clip-text.text-transparent {
  background-image: linear-gradient(to right, #ffffff 0%, #fde047 50%, #ffffff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}

/* Alternative Gradient Text Classes */
.text-gradient-white-yellow {
  background-image: linear-gradient(to right, #ffffff 0%, #fde047 50%, #ffffff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}

.text-gradient-primary {
  background-image: linear-gradient(135deg, #ebbf43 0%, #f4d156 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}

.text-gradient-secondary {
  background-image: linear-gradient(135deg, #ffbc02 0%, #ebbf43 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}

.text-gradient-cta {
  background-image: linear-gradient(135deg, #ffbc02 0%, #f4d156 50%, #ebbf43 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}

/* ========================================================================== */
/*   GRAY COLOR PALETTE - MISSING FROM TAILWIND
/* ========================================================================== */

/* Gray Background Colors */
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-300 { background-color: #d1d5db; }
.bg-gray-400 { background-color: #9ca3af; }
.bg-gray-500 { background-color: #6b7280; }
.bg-gray-600 { background-color: #4b5563; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-900 { background-color: #111827; }

/* Gray Text Colors */
.text-gray-50 { color: #f9fafb; }
.text-gray-100 { color: #f3f4f6; }
.text-gray-200 { color: #e5e7eb; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }

/* Gray Border Colors */
.border-gray-50 { border-color: #f9fafb; }
.border-gray-100 { border-color: #f3f4f6; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-gray-400 { border-color: #9ca3af; }
.border-gray-500 { border-color: #6b7280; }
.border-gray-600 { border-color: #4b5563; }
.border-gray-700 { border-color: #374151; }
.border-gray-800 { border-color: #1f2937; }
.border-gray-900 { border-color: #111827; }

/* ========================================================================== */
/*   YELLOW COLOR PALETTE - MISSING FROM TAILWIND
/* ========================================================================== */

/* Yellow Background Colors */
.bg-yellow-200 { background-color: #fde047; }
.bg-yellow-300 { background-color: #facc15; }
.bg-yellow-400 { background-color: #fbbf24; }
.bg-yellow-500 { background-color: #f59e0b; }

/* Yellow Text Colors */
.text-yellow-200 { color: #fde047; }
.text-yellow-300 { color: #facc15; }
.text-yellow-400 { color: #fbbf24; }
.text-yellow-500 { color: #f59e0b; }

/* ========================================================================== */
/*   UTILITY CLASSES - MISSING FROM TAILWIND
/* ========================================================================== */

/* Background Clip Text */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

/* Text Transparent */
.text-transparent {
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}

/* Backdrop Blur */
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur-md { backdrop-filter: blur(12px); }
.backdrop-blur-lg { backdrop-filter: blur(16px); }
.backdrop-blur-xl { backdrop-filter: blur(24px); }
.backdrop-blur-2xl { backdrop-filter: blur(40px); }

/* ========================================================================== */
/*   RESPONSIVE UTILITIES - MISSING FROM TAILWIND
/* ========================================================================== */

/* Responsive Text Sizes */
@media (min-width: 640px) {
  .sm\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .sm\:text-6xl { font-size: 3.75rem; line-height: 1; }
  .sm\:px-8 { padding-left: 2rem; padding-right: 2rem; }
}

@media (min-width: 768px) {
  .md\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .md\:py-20 { padding-top: 5rem; padding-bottom: 5rem; }
  .md\:py-24 { padding-top: 6rem; padding-bottom: 6rem; }
}

@media (min-width: 1024px) {
  .lg\:text-7xl { font-size: 4.5rem; line-height: 1; }
  .lg\:py-24 { padding-top: 6rem; padding-bottom: 6rem; }
}

/* ========================================================================== */
/*   ANIMATION UTILITIES - MISSING FROM TAILWIND
/* ========================================================================== */

/* Float Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes float-slow {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes float-fast {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-25px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-float-fast {
  animation: float-fast 4s ease-in-out infinite;
}

/* Pulse Animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}



/* ========================================================================== */
/*   FILTER UTILITIES - MISSING FROM TAILWIND
/* ========================================================================== */

/* Blur */
.blur-xl { filter: blur(12px); }
.blur-2xl { filter: blur(16px); }
.blur-3xl { filter: blur(24px); }





/* Enhanced Floating Animations */
@keyframes float {
	0%, 100% {
		transform: translateY(0px) rotate(0deg);
		opacity: 0.6;
	}
	50% {
		transform: translateY(-20px) rotate(180deg);
		opacity: 0.8;
	}
}

@keyframes floatSlow {
	0%, 100% {
		transform: translateY(0px) rotate(0deg);
		opacity: 0.4;
	}
	50% {
		transform: translateY(-15px) rotate(90deg);
		opacity: 0.7;
	}
}

@keyframes floatFast {
	0%, 100% {
		transform: translateY(0px) rotate(0deg);
		opacity: 0.5;
	}
	50% {
		transform: translateY(-10px) rotate(270deg);
		opacity: 0.8;
	}
}

/* Apply Float Animations */
.animate-float {
	animation: float 6s ease-in-out infinite;
}

.animate-float-slow {
	animation: floatSlow 8s ease-in-out infinite;
}

.animate-float-fast {
	animation: floatFast 4s ease-in-out infinite;
}

/* Staggered Animation Delays */
.animate-float:nth-child(1) { animation-delay: 0s; }
.animate-float:nth-child(2) { animation-delay: 2s; }
.animate-float:nth-child(3) { animation-delay: 4s; }
.animate-float:nth-child(4) { animation-delay: 6s; }
.animate-float:nth-child(5) { animation-delay: 1s; }
.animate-float:nth-child(6) { animation-delay: 3s; }

/* Glass Morphism Enhancement */
.glass-card {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.store-page-logo img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.glass-card:hover {
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(15px);
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Store Header Glass Effect */
.store-header-glass {
	background: rgba(0, 0, 0, 0.6);
	backdrop-filter: blur(20px);
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
	.filter-btn {
		padding: 12px 16px;
		font-size: 14px;
	}

	.animate-float {
		animation-duration: 4s;
	}
}

/* Ensure animations work on all floating elements */
.animate-float {
	animation-name: float;
	animation-duration: 6s;
	animation-timing-function: ease-in-out;
	animation-iteration-count: infinite;
	animation-fill-mode: both;
}

/* Accessibility Enhancements */
.filter-btn:focus {
	outline: 2px solid #EBBF43;
	outline-offset: 2px;
}

.filter-btn:focus:not(:focus-visible) {
	outline: none;
}

/* Brand Color Utilities */
.bg-brand-primary { background-color: #EBBF43; }
.bg-brand-secondary { background-color: #FFBC02; }
.bg-brand-accent { background-color: #000000; }
.text-brand-text { color: #220e07; }
.bg-brand-background { background-color: #fdf9f7; }