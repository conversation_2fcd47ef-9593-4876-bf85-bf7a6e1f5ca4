/* Coupon Card Styles - Matching Layout */

/* 📸 Image Styling */
.coupon-card img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* 🎨 Card Hover Effects */
.rounded-lg {
    transition: all 0.3s ease;
}

.rounded-lg:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 📋 Description Toggle */
.description-content {
    transition: all 0.3s ease-in-out;
}

.description-content.hidden {
    display: none;
}

.description-content.show {
    display: block;
    animation: fadeInDown 0.3s ease-out;
}

/* Icon switching handled by JavaScript */
.description-icon-down,
.description-icon-up {
    transition: all 0.3s ease;
}

.description-toggle:hover {
    color: #d56030; /* primary color */
}

/* 🎭 Code Button with Peel Effect */


.scratch-coupon-btn:hover .code-text {
    animation: reveal-code 0.5s ease-out forwards;
}

.OfferPeel {
    background: linear-gradient(38deg, #cbd5e1, #f1f5f9 49%, #ff960000 50%, #fff0) no-repeat 0 4px;
    border-radius: 0 0 5px 5px;
    transform: rotate(22deg) translateY(-10px) scaleX(.9);
    transition: transform .2s, background-position .2s;
    z-index: 1;
}

/* CTA Colors now defined in Tailwind config - no custom CSS needed */

@keyframes reveal-code {
    0% {
        filter: blur(2px);
        transform: scale(0.95);
    }
    100% {
        filter: blur(0);
        transform: scale(1);
    }
}

@keyframes fadeInDown {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ❤️ Like Button */
.like-btn:hover svg {
    animation: heart-pulse 0.6s ease-in-out;
}

@keyframes heart-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* 🎯 Full Width Footer */
.coupon-card footer {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
}

.coupon-card section {
    width: 100%;
}
