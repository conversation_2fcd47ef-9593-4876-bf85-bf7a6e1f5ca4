/**
 * CMB2 Coupon Stores Taxonomy Admin Interface - RTL Support
 * RTL-specific styles for Arabic and other right-to-left languages
 */

/* ==========================================================================
   RTL Base Adjustments
   ========================================================================== */

[dir="rtl"] .taxonomy-coupon_store .cmb2-metabox h2.hndle,
.rtl .taxonomy-coupon_store .cmb2-metabox h2.hndle {
    text-align: right;
    padding: 16px 20px 16px 20px;
}

[dir="rtl"] .taxonomy-coupon_store .cmb2-metabox h2.hndle::before,
.rtl .taxonomy-coupon_store .cmb2-metabox h2.hndle::before {
    right: 0;
    left: 0;
}

/* ==========================================================================
   RTL Form Fields
   ========================================================================== */

/* Field Labels */
[dir="rtl"] .taxonomy-coupon_store .cmb-th label,
.rtl .taxonomy-coupon_store .cmb-th label {
    text-align: right;
}

/* Text Inputs RTL */
[dir="rtl"] .taxonomy-coupon_store .cmb2-text-input,
[dir="rtl"] .taxonomy-coupon_store .cmb2-text-medium,
[dir="rtl"] .taxonomy-coupon_store .cmb2-text-url,
.rtl .taxonomy-coupon_store .cmb2-text-input,
.rtl .taxonomy-coupon_store .cmb2-text-medium,
.rtl .taxonomy-coupon_store .cmb2-text-url {
    direction: rtl;
    text-align: right;
}

/* Textareas RTL */
[dir="rtl"] .taxonomy-coupon_store .cmb2-textarea,
.rtl .taxonomy-coupon_store .cmb2-textarea {
    direction: rtl;
    text-align: right;
}

/* Select Dropdowns RTL */
[dir="rtl"] .taxonomy-coupon_store .cmb2-select,
.rtl .taxonomy-coupon_store .cmb2-select {
    direction: rtl;
    text-align: right;
}

/* Checkboxes RTL */
[dir="rtl"] .taxonomy-coupon_store .cmb2-checkbox,
.rtl .taxonomy-coupon_store .cmb2-checkbox {
    margin-right: 0;
    margin-left: 8px;
}

/* ==========================================================================
   RTL Group/Repeater Fields
   ========================================================================== */

/* Group Title RTL */
[dir="rtl"] .taxonomy-coupon_store .cmb-group-title,
.rtl .taxonomy-coupon_store .cmb-group-title {
    text-align: right;
    flex-direction: row-reverse;
}

/* Group Controls RTL */
[dir="rtl"] .taxonomy-coupon_store .cmb-group-title .cmb-group-controls,
.rtl .taxonomy-coupon_store .cmb-group-title .cmb-group-controls {
    flex-direction: row-reverse;
}

/* Drag Handle RTL */
[dir="rtl"] .taxonomy-coupon_store .cmb-group-title .cmb-handle,
.rtl .taxonomy-coupon_store .cmb-group-title .cmb-handle {
    margin-left: 0;
    margin-right: 8px;
}

/* ==========================================================================
   RTL Select2 Enhancement
   ========================================================================== */

[dir="rtl"] .taxonomy-coupon_store .select2-container,
.rtl .taxonomy-coupon_store .select2-container {
    direction: rtl;
}

[dir="rtl"] .taxonomy-coupon_store .select2-selection,
.rtl .taxonomy-coupon_store .select2-selection {
    text-align: right;
}

[dir="rtl"] .taxonomy-coupon_store .select2-selection__rendered,
.rtl .taxonomy-coupon_store .select2-selection__rendered {
    padding-right: 12px;
    padding-left: 20px;
}

[dir="rtl"] .taxonomy-coupon_store .select2-selection__arrow,
.rtl .taxonomy-coupon_store .select2-selection__arrow {
    left: 12px;
    right: auto;
}

/* ==========================================================================
   RTL WYSIWYG Editor
   ========================================================================== */

[dir="rtl"] .taxonomy-coupon_store .wp-editor-area,
.rtl .taxonomy-coupon_store .wp-editor-area {
    direction: rtl;
    text-align: right;
}

[dir="rtl"] .taxonomy-coupon_store .wp-editor-tools,
.rtl .taxonomy-coupon_store .wp-editor-tools {
    direction: rtl;
}

/* ==========================================================================
   RTL File Upload
   ========================================================================== */

[dir="rtl"] .taxonomy-coupon_store .cmb2-upload-file,
.rtl .taxonomy-coupon_store .cmb2-upload-file {
    text-align: center; /* Keep centered for upload areas */
}

/* ==========================================================================
   RTL Responsive Adjustments
   ========================================================================== */

@media (max-width: 768px) {
    [dir="rtl"] .taxonomy-coupon_store .cmb-group-title,
    .rtl .taxonomy-coupon_store .cmb-group-title {
        align-items: flex-end;
    }
}

/* ==========================================================================
   RTL Typography Enhancements
   ========================================================================== */

/* Arabic Font Optimization */
[dir="rtl"] .taxonomy-coupon_store,
.rtl .taxonomy-coupon_store {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
}

[dir="rtl"] .taxonomy-coupon_store .cmb-th label,
.rtl .taxonomy-coupon_store .cmb-th label {
    font-weight: 600;
    line-height: 1.6;
}

[dir="rtl"] .taxonomy-coupon_store .cmb2-metabox-description,
.rtl .taxonomy-coupon_store .cmb2-metabox-description {
    line-height: 1.7;
}

/* ==========================================================================
   RTL Animation Adjustments
   ========================================================================== */

[dir="rtl"] .taxonomy-coupon_store .cmb-add-group-row:hover,
.rtl .taxonomy-coupon_store .cmb-add-group-row:hover {
    transform: translateY(-1px);
}

[dir="rtl"] .taxonomy-coupon_store .cmb-remove-group-row:hover,
.rtl .taxonomy-coupon_store .cmb-remove-group-row:hover {
    transform: translateY(-1px);
}

[dir="rtl"] .taxonomy-coupon_store .cmb2-upload-button:hover,
.rtl .taxonomy-coupon_store .cmb2-upload-button:hover {
    transform: translateY(-1px);
}

/* ==========================================================================
   RTL Gradient Adjustments
   ========================================================================== */

[dir="rtl"] .taxonomy-coupon_store .cmb2-metabox h2.hndle::before,
.rtl .taxonomy-coupon_store .cmb2-metabox h2.hndle::before {
    background: linear-gradient(270deg, #EBBF43, #FFBC02); /* Reversed gradient for RTL */
}

/* ==========================================================================
   RTL Accessibility Enhancements
   ========================================================================== */

[dir="rtl"] .taxonomy-coupon_store .cmb2-text-input:focus,
[dir="rtl"] .taxonomy-coupon_store .cmb2-text-medium:focus,
[dir="rtl"] .taxonomy-coupon_store .cmb2-text-url:focus,
.rtl .taxonomy-coupon_store .cmb2-text-input:focus,
.rtl .taxonomy-coupon_store .cmb2-text-medium:focus,
.rtl .taxonomy-coupon_store .cmb2-text-url:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

[dir="rtl"] .taxonomy-coupon_store .cmb2-textarea:focus,
.rtl .taxonomy-coupon_store .cmb2-textarea:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* ==========================================================================
   RTL Print Styles
   ========================================================================== */

@media print {
    [dir="rtl"] .taxonomy-coupon_store .cmb2-metabox,
    .rtl .taxonomy-coupon_store .cmb2-metabox {
        direction: rtl;
        text-align: right;
    }
    
    [dir="rtl"] .taxonomy-coupon_store .cmb-group-title .cmb-group-controls,
    .rtl .taxonomy-coupon_store .cmb-group-title .cmb-group-controls {
        display: none; /* Hide controls in print */
    }
}
