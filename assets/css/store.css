
/*Single Store Page*/

.single-store-content {
	margin-top: 0px !important;
  }


  .store_meta.flex{
	gap: 20px;
  }


  .header-store-title {
	flex: auto
  }



  .rating-star-rating {
	display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .rating-star-rating .rating .rating-summary {
	text-align: center;

  }


  .rating-star-rating .rate_this_btn {
	display: none;
	visibility: hidden;
  }



  .section-heading {
	background-color: #fff;
	padding-block: 6px;
	padding-inline: 15px;
	border-top-right-radius: var(--small-border-radius);
	border-top-left-radius: var(--small-border-radius);
  }



  
  .single-store-content .coupon-store-content {
	display: flex;
	margin-top: 30px;
  }
  

  .store-main-content .store-extra-info{
	padding: 20px;
	border-radius: 4px;
	background: #fff;
  }

  
  .single-store-content .singel-store-sidebar {
	flex: 0 0 24%;
	margin-inline-start: 15px;
  }
  
  .single-store-content .store_info_tpl {
	border: 1px solid #ededed;
	border-radius: var(--small-border-radius);
	overflow: hidden;
	flex: 0 0 24%;
  }



  .single-store-content .store_info_tpl .item_list {
	display: flex;
	justify-content: space-between;
	border-bottom: 1px dashed rgb(27 28 29 / 10%);
	padding: 12px 20px;
  }

  .single-store-content .store_info_tpl .item_list:last-child {
	border-bottom: none;
  }
  
  .single-store-content .store_info_tpl .item_list .item_list-right {
	font-weight: 700;
	color: #000;
  }

  .single-store-content .store_info_tpl .item_list .item_list-left {
	display: flex;

  }
  .single-store-content .store_info_tpl .item_list .item_list-left p{
	font-weight: 600;
	color: var(--text-color);
  }

  .single-store-content .header-content .header-store-thumb {
	flex: 0 0 280px;
	border: 1px solid #ededed;
	border-radius: var(--large-border-radius);
	overflow: hidden;
	min-width: 280px;
	height: 140px;

  }
  
  .single-store-content .header-content .header-store-thumb img {
	display: block;
	width: 100%;
	margin: auto;
	min-width: 280px;
	height: 140px;
	object-fit: cover;
	aspect-ratio: 16 / 9;
}


.single-store-content .header-content .revealed_store_link {
	display: flex!important;
    align-items: center;
    justify-content: center;
    height: -webkit-fill-available;
    background-color: var(--primary-color);
    color: #fff;
}
  
  .store-info-content .icon,
  .store-info-content .icons {
	color: var(--primary-color);
	margin-inline-end: 5px;
	font-size: 1.2rem;

  }


  .related_Stores_list {
	padding-inline: 15px!important;
  }


  
  
  .sidebar .sidebar-section {
	padding: 20px 15px;
	background: #fff;
	margin-bottom: 20px;
	border-radius: 10px;
  }
  .sidebar-section .widget-area .widget .widget-title {
	font-size: 16px;
	font-weight: 700 !important;
  }
  .sidebar-section h4 {
	color: #1a202c;
  }
  .tax-coupon_store .sidebar-section .shadow-box {
	box-shadow: none;
  }
  .sidebar .related-stores .front-store-items,
  .sidebar .related-categories .cate-item {
	padding: 5px 0px;
  }
  .sidebar .related-stores .front-store-items a,
  .sidebar .related-categories .cate-item a {
	color: var(--text-color);
  }
  .sidebar .related-stores .front-store-items a:hover,
  .sidebar .related-categories .cate-item a:hover {
	color: #38b2ac;
  }
  .tax-coupon_store .sidebar .widget {
	margin-bottom: 10px !important;
  }
  .tax-coupon_store .coupon-store-main .header-content{
	background-color: var(--background-light-color);
	padding-block:  35px;

}

.tax-coupon_category .coupon-store-main .header-content{
	background-color: #fff;
	padding-block-start: 24px;
	padding-block-end: 13px;
	position: relative;
	height: 22vh;
}





.tax-coupon_category .coupon-store-main .header-content .header-filter-buttons {
	position: absolute;
    bottom: -38px;
}

.tax-coupon_store .coupon-store-main .header-content .header-filter-buttons{
	position: absolute;
    bottom: -45px;
}

  .tax-coupon_store .coupon-store-main .header-content h1 {
	font-size: 24px;
  }
  
  .inner.shadow-box.Coupon-extra-info {
	padding: 20px;
	border-radius: 10px;
  }


  
.single-store-header .header-thumb {
	text-align: left;
  }
  #secondary .header-thumb .header-store-thumb {
	text-align: center;
  }
  .single-store-header .header-thumb .header-store-thumb a {
	line-height: 114px;
	border: 2px solid #f5f5f5;
	text-align: center;
	margin-bottom: 10px;
	min-width: 200px;
	padding: 5px;
	display: block;
  }
  .single-store-header .header-thumb .header-store-thumb a:hover {
	border-color: #319799;
  }
  .single-store-header .header-thumb img {
	max-width: 200px;
  }
  .single-store-header .add-favorite {
	font-size: 12px;
	letter-spacing: 0.5px;
	text-transform: uppercase;
  }
  .single-store-header .add-favorite:hover {
	color: #00979d;
  }
  


  .tax-coupon_store .site-content #content-wrap{
	padding-top: 0;


  }


  .mini-store-description {
    position: relative;
	padding-block-start: 10px;

}

.mini-store-description p {
    font-size: 14px;
    line-height: 24px;
    width: 96%;
    color: var(--text-color);
}

.mini-store-description .close-btn {
    background: none;
    border: none;
    color: #727272;
    font-size: 16px;
    position: absolute;
    top: 10px;
    left: 10px;
    cursor: pointer;
}

.mini-store-description .close-btn .icon {
    font-size: 16px;
    margin-right: 5px;
	color: #727272;
}


.tax-coupon_store .fixed-div {
	justify-content: flex-start;
	margin-block-start: 22px;
}

.tax-coupon_store .fixed-div .fixed_title {
	font-size: 16px ;
}


#coupon-listings-store .is_exclusive_coupon {
    box-shadow: 0px 5px 12px 1px #f2711c33;
    border-right: 4px solid var(--primary-color);
}


#coupon-listings-store .no-coupons-found {
	padding-block: 40px;
	font-size: 16px;
    font-weight: 600;
    text-align: center;
	align-items: center;
	justify-content: center;
	border-radius: var(--small-border-radius);
	width: 100%;
	background-color: #fff;
}





.tax-coupon_category .coupon-store-main .header-content {
	background-color: #F1F5FE;
}



.header-store-thumb .cate-icon {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-direction: column;
	height: 144px;
	padding-block: 30px;
	background-color: #fff;

}

.header-store-thumb .cate-icon span{
    font-size: 16px;
    font-weight: 600;
    margin-block-start: 5px;
    letter-spacing: 0;
    color: #000;
}


.header-store-thumb .cate-icon i {
	font-size: 2.8rem;
	color: var(--primary-color);

}


.jq-stars {
	display: inline-block;
  }
  
  .jq-rating-label {
	font-size: 22px;
	display: inline-block;
	position: relative;
	vertical-align: top;
	font-family: helvetica, arial, verdana;
  }
  
  .jq-star {
	width: 100px;
	height: 100px;
	display: inline-block;
	cursor: pointer;
  }
  
  .jq-star-svg {
	padding-left: 3px;
	width: 100%;
	height: 100% ;
  }
  
  
  .jq-star-svg path {
	/* stroke: #000; */
	stroke-linejoin: round;
  }
  
  /* un-used */
  .jq-shadow {
	-webkit-filter: drop-shadow( -2px -2px 2px #888 );
	filter: drop-shadow( -2px -2px 2px #888 );
  }




/* tablet css */

@media(min-width: 768px) and (max-width: 1024px) {




	section#store-content {
		width: 100%;
		max-width: 100%;
		flex: 0 0 100%;
		z-index: auto;
	}


	.single-store-content .singel-store-sidebar {
		flex: 0 0 100%;
		max-width: 100%;
		width: 100%;
		margin-inline-start: 0;
		margin-block-start: 30px;

	}

	.related_Stores_list {
		display: flex;
		flex-wrap: wrap;
		gap: 15px;
	}



	.related_Stores_list .item{
		flex: 0 0 calc(50% - 7.5px);
		max-width: calc(50% - 7.5px);

	}



}




/* Mobile css */

@media screen and (max-width: 767px) {

	.single-store-content .coupon-store-content {
		flex-direction: column;

	}


	section#store-content {
		width: 100%;
		max-width: 100%;
		flex: 0 0 100%;
		z-index: auto;
	}


	.tax-coupon_store .coupon-store-main .header-content .header-filter-buttons, .tax-coupon_category .coupon-store-main .header-content .header-filter-buttons{
		position: relative;
		bottom: auto;
		margin-top: 20px;
	}


	.tax-coupon_store .coupon-store-main .header-content, .tax-coupon_category .coupon-store-main .header-content {
		height: auto;
		padding-block-end: 50px;
	}


	.coupon-listing-item .coupon-item-header {
		flex-direction: row
	}

	.single-store-content .header-content .header-store-thumb,
	.single-store-content .header-content .header-store-thumb img {
	    min-width: 150px;
       height: 75px;
	   flex: 0 0 25%;
	  }

	  .header-store-thumb .cate-icon {
		padding-block: 22px;
	  }

	  .header-store-thumb .cate-icon span {
		display: none;
	  }

	  .tax-coupon_store .coupon-store-main .header-content h1 {
		font-size: 14px;
		margin: 0;
	  }


	.mini-store-description p {
		font-size: 12px;
		line-height: 20px;
	}


	.single-store-content .singel-store-sidebar {
		flex: 0 0 100%;
		max-width: 100%;
		width: 100%;
		margin-inline-start: 0;
		margin-block-start: 30px;

	}





	.reviews_modal {
		padding: 30px;
		border-radius: var(--small-border-radius)!important;
	}


	.reviews_modal .rating-wrapper {
		display: flex;
		flex-direction: column;
		gap: 10px;
	}

    .rating-star-rating{
		position: relative;
	}


	.rating-star-rating .rate_this_btn {
		display: block;
		visibility: visible;
	  }

	  .rating-star-rating .rate_this_btn .open_review_modal  {
		position: absolute;
		left: 0;
		bottom: -60px;
		font-size: 12px;
		padding: 5px 10px 5px 0px;
		color: #000;
		min-width: max-content;
		display: flex;
        align-items: center;
	  }







	  .content-area {
		padding-bottom: 0px;
	  }


	  .tax-coupon_store .fixed-div .fixed_title {
		font-size: 14px;
	  }


	  .store_meta.flex {
		flex-direction: row;
		align-items: center;
	}

	.single-store-content .store_info_tpl {
		width: 100%;
		max-width: 100%;
		flex: 0 0 100%;
	}


	
}