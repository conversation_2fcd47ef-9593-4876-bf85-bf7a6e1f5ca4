/**
 * Modern Glass Header Styles
 * 
 * @package HalaCoupon
 * @version 4.0.0 - Modern Glass Rebuild
 * @description Creative glass morphism effects, backdrop blur, and smooth transitions
 */

/* ==========================================================================
   Missing Tailwind Gradient Classes - Manual Implementation
   ========================================================================== */

/* Background gradient utilities */
.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

/* Gradient stops for brand colors */
.from-primary {
    --tw-gradient-from: #EBBF43;
    --tw-gradient-to: rgb(235 191 67 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-secondary {
    --tw-gradient-to: #FFBC02;
}

.from-secondary {
    --tw-gradient-from: #FFBC02;
    --tw-gradient-to: rgb(255 188 2 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-accent {
    --tw-gradient-to: #000000;
}

.from-accent {
    --tw-gradient-from: #000000;
    --tw-gradient-to: rgb(0 0 0 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

/* Background clip text utility */
.bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
}

/* Text transparent utility */
.text-transparent {
    color: transparent;
}

/* Hover gradient states */
.hover\:from-secondary:hover {
    --tw-gradient-from: #a2e993;
    --tw-gradient-to: rgb(162 233 147 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-accent:hover {
    --tw-gradient-to: #66e093;
}

.hover\:from-accent:hover {
    --tw-gradient-from: #66e093;
    --tw-gradient-to: rgb(102 224 147 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:via-primary:hover {
    --tw-gradient-to: rgb(213 96 48 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), #d56030, var(--tw-gradient-to);
}

/* Via gradient utilities */
.via-secondary {
    --tw-gradient-to: rgb(162 233 147 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), #a2e993, var(--tw-gradient-to);
}

.via-primary {
    --tw-gradient-to: rgb(213 96 48 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), #d56030, var(--tw-gradient-to);
}

.via-accent {
    --tw-gradient-to: rgb(102 224 147 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), #66e093, var(--tw-gradient-to);
}

/* Additional gradient directions */
.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-bl {
    background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
}

.bg-gradient-to-tr {
    background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}

.bg-gradient-to-tl {
    background-image: linear-gradient(to top left, var(--tw-gradient-stops));
}

/* Ensure transitions work with gradients */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.duration-300 {
    transition-duration: 300ms;
}

.duration-500 {
    transition-duration: 500ms;
}

/* Logo specific gradient classes using exact brand colors - Golden/Yellow scheme */
.logo-gradient-text {
    background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 50%, #f4d156 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    transition: all 0.5s ease;
}

.logo-gradient-text:hover {
    background: linear-gradient(135deg, #f4d156 0%, #EBBF43 50%, #FFBC02 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

/* Enhanced logo gradient for sticky/scrolled header state */
.header-scrolled .logo-gradient-text {
    background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 40%, #f4d156 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    text-shadow: 0 0 20px rgba(235, 191, 67, 0.3);
    filter: drop-shadow(0 2px 4px rgba(235, 191, 67, 0.2));
}

.header-scrolled .logo-gradient-text:hover {
    background: linear-gradient(135deg, #f4d156 0%, #EBBF43 40%, #FFBC02 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    text-shadow: 0 0 25px rgba(235, 191, 67, 0.4);
    filter: drop-shadow(0 2px 6px rgba(235, 191, 67, 0.3));
    transform: scale(1.02);
}

/* ==========================================================================
   LTR/RTL Support for Header Elements
   ========================================================================== */

/* Base RTL/LTR layout adjustments */
[dir="rtl"] #masthead .flex.items-center.justify-between {
    direction: rtl;
}

[dir="ltr"] #masthead .flex.items-center.justify-between {
    direction: ltr;
}

/* Mobile menu toggle positioning */
[dir="rtl"] #nav-toggle {
    order: 3; /* Move to right side in RTL */
}

[dir="ltr"] #nav-toggle {
    order: 1; /* Keep on left side in LTR */
}

/* Logo positioning */
[dir="rtl"] .flex-1.lg\\:flex-initial {
    order: 2;
    text-align: right;
}

[dir="ltr"] .flex-1.lg\\:flex-initial {
    order: 2;
    text-align: left;
}

/* Header actions positioning */
[dir="rtl"] .flex.items-center.space-x-3 {
    order: 1; /* Move to left side in RTL */
    flex-direction: row-reverse;
    gap: 0.75rem;
}

[dir="ltr"] .flex.items-center.space-x-3 {
    order: 3; /* Keep on right side in LTR */
    flex-direction: row;
}

/* Navigation menu RTL support */
[dir="rtl"] #site-header-nav {
    direction: rtl;
}

[dir="rtl"] #site-header-nav .flex.items-center.space-x-2 {
    flex-direction: row;
    gap: 0.5rem;
}

[dir="rtl"] #site-header-nav .absolute.top-full.left-0 {
    left: auto;
    right: 0;
}

/* Search modal RTL support */
[dir="rtl"] #search_modal .absolute.top-full.right-0 {
    right: auto;
    left: 0;
}


/* Mobile menu RTL support */
[dir="rtl"] #mobile_menu_modal .flex.items-start.justify-end {
    justify-content: flex-start;
}

[dir="rtl"] #mobile_menu_modal .modal-content {
    border-left: none;
    border-right: 1px solid rgba(255, 255, 255, 0.3);
}

[dir="rtl"] .animate-slide-in-right {
    animation: slide-in-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Likes popup RTL support */
[dir="rtl"] #total-likes-popup .absolute.top-full.right-0 {
    right: auto;
    left: 0;
}

/* Country dropdown RTL support */
[dir="rtl"] .country-selector-wrapper .country-popup {
    left: auto;
    right: 0;
}

/* ==========================================================================
   Header Base Styles
   ========================================================================== */

/* Default header state - Black background with golden accents */
.header-default {
    background: rgba(0, 0, 0, 1) !important;
    border-bottom: 1px solid rgba(235, 191, 67, 0.2) !important;
    box-shadow: 0 1px 3px rgba(235, 191, 67, 0.1) !important;
}

/* Scrolled header state - Enhanced black glass backdrop effect */
.header-scrolled {
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border-bottom: 1px solid rgba(235, 191, 67, 0.3) !important;
    box-shadow:
        0 4px 25px -5px rgba(0, 0, 0, 0.3),
        0 10px 30px -5px rgba(235, 191, 67, 0.15) !important;
}

/* Enhanced transition for header states */
#masthead {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: background, backdrop-filter, box-shadow;
}

/* Header text colors for black background */
#masthead {
    color: white;
}

#masthead .text-gray-700 {
    color: rgba(255, 255, 255, 0.9) !important;
}

#masthead .hover\\:text-primary:hover {
    color: #EBBF43 !important;
}

#masthead .text-gray-600 {
    color: rgba(255, 255, 255, 0.8) !important;
}

#masthead .text-gray-500 {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Navigation link colors */
#masthead nav a {
    color: rgba(255, 255, 255, 0.9);
}

#masthead nav a:hover {
    color: #EBBF43;
}

/* ==========================================================================
   Creative Background Elements
   ========================================================================== */

/* Animated background elements on scroll */
.header-scrolled .absolute.top-0.right-0 {
    background: linear-gradient(135deg, rgba(213, 96, 48, 0.15), transparent);
    transform: translateX(8px) translateY(-8px) scale(1.1);
}

.header-scrolled .absolute.bottom-0.left-0 {
    background: linear-gradient(45deg, rgba(162, 233, 147, 0.15), transparent);
    transform: translateX(-8px) translateY(8px) scale(1.1);
}

/* ==========================================================================
   Mobile Menu Toggle Animations
   ========================================================================== */

/* Hamburger menu animation */
#nav-toggle span {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
}

/* Enhanced hover effects for mobile toggle */
#nav-toggle:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(235, 191, 67, 0.15);
}

/* Mobile menu open state */
#nav-toggle.menu-open {
    background: rgba(235, 191, 67, 0.3) !important;
    border-color: rgba(235, 191, 67, 0.5) !important;
    box-shadow: 0 0 20px rgba(235, 191, 67, 0.3) !important;
}

/* Enhanced mobile toggle button for black header */
#nav-toggle {
    background: rgba(235, 191, 67, 0.2) !important;
    border: 1px solid rgba(235, 191, 67, 0.3) !important;
    box-shadow: 0 0 20px rgba(235, 191, 67, 0.15) !important;
}

#nav-toggle:hover {
    background: rgba(235, 191, 67, 0.3) !important;
    border-color: rgba(235, 191, 67, 0.5) !important;
    box-shadow: 0 0 25px rgba(235, 191, 67, 0.25) !important;
}

/* Mobile toggle spans (hamburger lines) */
#nav-toggle span {
    background-color: #ffffff !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#nav-toggle:hover span {
    background-color: #EBBF43 !important;
}

/* Mobile toggle button colors for black header */
#nav-toggle {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(235, 191, 67, 0.3) !important;
}

#nav-toggle:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(235, 191, 67, 0.5) !important;
}

#nav-toggle span {
    background-color: rgba(255, 255, 255, 0.9) !important;
}

#nav-toggle:hover span {
    background-color: #EBBF43 !important;
}


/* ==========================================================================
   Navigation Glass Effects
   ========================================================================== */



#site-header-nav a:hover {
    color: #EBBF43 !important;
    background: rgba(235, 191, 67, 0.1) !important;
}

/* Navigation dropdown enhancements */
#site-header-nav .group:hover ul {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(235, 191, 67, 0.3);
    box-shadow:
        0 20px 60px -12px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(235, 191, 67, 0.2);
}

/* ==========================================================================
   Header Actions Glass Effects
   ========================================================================== */

/* Enhanced search button for black header */
#search_p_button {
    background: rgba(255, 188, 2, 0.2) !important;
    border: 1px solid rgba(255, 188, 2, 0.3) !important;
    box-shadow: 0 0 20px rgba(255, 188, 2, 0.15) !important;
}

#search_p_button:hover {
    background: rgba(255, 188, 2, 0.3) !important;
    border-color: rgba(255, 188, 2, 0.5) !important;
    box-shadow: 0 0 25px rgba(255, 188, 2, 0.25) !important;
}

#search_p_button svg {
    color: #ffffff !important;
}

#search_p_button:hover svg {
    color: #FFBC02 !important;
}

/* Action buttons glass enhancement on scroll */
.header-scrolled .flex.items-center.space-x-3 button {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(235, 191, 67, 0.3);
}

/* Header action button colors for black background */
#masthead .flex.items-center.space-x-3 button {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(235, 191, 67, 0.3) !important;
}

#masthead .flex.items-center.space-x-3 button:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(235, 191, 67, 0.5) !important;
}

#masthead .flex.items-center.space-x-3 button svg {
    color: rgba(255, 255, 255, 0.9) !important;
}

#masthead .flex.items-center.space-x-3 button:hover svg {
    color: #EBBF43 !important;
}

/* Enhanced hover states for action buttons */
.flex.items-center.space-x-3 button:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(213, 96, 48, 0.2);
    box-shadow: 
        0 8px 25px rgba(213, 96, 48, 0.15),
        0 0 20px rgba(213, 96, 48, 0.1);
    transform: translateY(-1px) scale(1.05);
}

/* Likes counter enhancement */
.has-likes {
    position: relative;
}

.has-likes::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, #d56030, #a2e993);
    border-radius: inherit;
    z-index: -1;
    opacity: 0.3;
    animation: pulse-glow 2s ease-in-out infinite;
}

/* ==========================================================================
   Enhanced Search Modal
   ========================================================================== */

/* Search modal backdrop */
#search_modal {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
}

/* Search input focus enhancements */
#search_input:focus {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow:
        0 0 0 4px rgba(213, 96, 48, 0.1),
        0 8px 25px rgba(213, 96, 48, 0.15);
    transform: translateY(-1px);
}

/* Search loading spinner */
#search_loading {
    z-index: 10;
}

/* Search tag cloud styling */
.search-tag {
    cursor: pointer;
    user-select: none;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.search-tag:active {
    transform: scale(0.95);
}

/* Store tags specific styling */
.store-tag:hover {
    background: rgba(213, 96, 48, 0.1) !important;
    border-color: rgba(213, 96, 48, 0.3) !important;
    color: #d56030 !important;
    box-shadow: 0 4px 12px rgba(213, 96, 48, 0.15);
}

/* Category tags specific styling */
.category-tag:hover {
    background: rgba(162, 233, 147, 0.1) !important;
    border-color: rgba(162, 233, 147, 0.3) !important;
    color: #a2e993 !important;
    box-shadow: 0 4px 12px rgba(162, 233, 147, 0.15);
}

/* Search results styling */
#search_results {
    scrollbar-width: thin;
    scrollbar-color: rgba(213, 96, 48, 0.3) transparent;
}

#search_results::-webkit-scrollbar {
    width: 6px;
}

#search_results::-webkit-scrollbar-track {
    background: transparent;
}

#search_results::-webkit-scrollbar-thumb {
    background: rgba(213, 96, 48, 0.3);
    border-radius: 3px;
}

#search_results::-webkit-scrollbar-thumb:hover {
    background: rgba(213, 96, 48, 0.5);
}

/* Enhanced search result items */
.search-result-item {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-result-item:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(213, 96, 48, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(213, 96, 48, 0.1);
}

/* Search result type badges */
.result-type-badge {
    font-size: 0.625rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.result-type-store {
    background: linear-gradient(135deg, #d56030, #a2e993);
    color: white;
}

.result-type-coupon {
    background: linear-gradient(135deg, #a2e993, #66e093);
    color: white;
}

/* ==========================================================================
   Animations & Keyframes
   ========================================================================== */

@keyframes pulse-glow {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.02);
    }
}

@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scale-in {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Animation classes */
.animate-fade-in {
    animation: fade-in 0.3s ease-out;
}

.animate-scale-in {
    animation: scale-in 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-right {
    animation: slide-in-right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-down {
    animation: slide-down 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-up {
    animation: slide-up 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Additional keyframes */
@keyframes slide-in-right {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slide-in-left {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slide-down {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slide-up {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
}

/* ==========================================================================
   WordPress Admin Bar Support
   ========================================================================== */

/* Admin bar adjustments for logged-in users */
.logged-in.admin-bar #masthead {
    top: 32px; /* Default admin bar height */
}

/* Content area adjustment when admin bar is present */
.logged-in.admin-bar #content {
    padding-top: calc(3rem + 32px); /* Header height + admin bar height */
}

.logged-in.admin-bar #content.lg\:pt-20 {
    padding-top: calc(4rem + 32px); /* Large screen header height + admin bar height */
}

/* Mobile admin bar adjustments */
@media screen and (max-width: 782px) {
    .logged-in.admin-bar #masthead {
        top: 46px; /* Mobile admin bar height */
    }

    .logged-in.admin-bar #content {
        padding-top: calc(3rem + 46px); /* Header height + mobile admin bar height */
    }
}

/* Admin bar hover state - prevent interference with header glass effects */
.logged-in.admin-bar #wpadminbar {
    z-index: 99999; /* Ensure admin bar stays above header */
}

/* Smooth transition when admin bar appears/disappears */
#masthead {
    transition: top 0.3s ease-in-out, background 0.5s cubic-bezier(0.4, 0, 0.2, 1), backdrop-filter 0.5s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

#content {
    transition: padding-top 0.3s ease-in-out;
}

/* Fix for admin bar z-index conflicts with modals */
.logged-in.admin-bar #search_modal,
.logged-in.admin-bar #mobile_menu_modal {
    z-index: 100000; /* Higher than admin bar */
}

/* Ensure header glass effects work properly with admin bar */
.logged-in.admin-bar .header-scrolled {
    border-top: 1px solid rgba(213, 96, 48, 0.1);
}

/* Admin bar responsive breakpoint adjustments */
@media screen and (max-width: 600px) {
    .logged-in.admin-bar #masthead {
        top: 46px; /* Mobile admin bar height for very small screens */
    }

    .logged-in.admin-bar #content {
        padding-top: calc(4rem + 46px);
    }
}

/* Tablet admin bar adjustments */
@media screen and (min-width: 783px) and (max-width: 1023px) {
    .logged-in.admin-bar #masthead {
        top: 32px; /* Desktop admin bar height for tablets */
    }

    .logged-in.admin-bar #content {
        padding-top: calc(4rem + 32px);
    }
}

/* ==========================================================================
   Responsive Enhancements
   ========================================================================== */

/* Mobile optimizations */
@media (max-width: 1023px) {
    .header-scrolled {
        background: rgba(0, 0, 0, 0.95);
        backdrop-filter: blur(15px);
    }

    /* Reduce blur on mobile for performance */
    .header-scrolled #site-header-nav > div,
    .header-scrolled .flex-1 a,
    .header-scrolled .flex-1 div {
        backdrop-filter: blur(8px);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .header-default {
        background: rgb(0, 0, 0);
        border-bottom: 2px solid rgb(235, 191, 67);
    }

    .header-scrolled {
        background: rgb(0, 0, 0);
        backdrop-filter: none;
    }

    #masthead {
        color: rgb(255, 255, 255);
    }

    #masthead nav a {
        color: rgb(255, 255, 255);
    }

    #masthead nav a:hover {
        color: rgb(235, 191, 67);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    #masthead,
    #nav-toggle span,
    .flex.items-center.space-x-3 button {
        transition: none;
    }
    
    .animate-fade-in,
    .animate-scale-in {
        animation: none;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    #masthead {
        position: static !important;
        background: white !important;
        box-shadow: none !important;
        backdrop-filter: none !important;
    }
    
    #search_modal,
    #mobile_menu_modal {
        display: none !important;
    }
}

/* ==========================================================================
   Accessibility Enhancements
   ========================================================================== */

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators */
button:focus-visible,
a:focus-visible,
input:focus-visible {
    outline: 2px solid #d56030;
    outline-offset: 2px;
}

/* High contrast focus indicators */
@media (prefers-contrast: high) {
    button:focus-visible,
    a:focus-visible,
    input:focus-visible {
        outline: 3px solid #000;
        outline-offset: 2px;
    }
}
