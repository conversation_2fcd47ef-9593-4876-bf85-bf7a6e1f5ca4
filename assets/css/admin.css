/**
 * HalaCoupon Admin Styles - Enhanced with Rubik Font
 *
 * Modern WordPress admin styling with local Rubik font implementation
 * Features: Brand colors, improved UX, RTL/LTR support, accessibility
 *
 * @package HalaCoupon
 * @version 2.0.0 - Enhanced Admin Experience
 */

/* ==========================================================================
   Rubik Font Family - Local Implementation for Admin
   ========================================================================== */

/* Rubik Light Normal */
@font-face {
  font-family: 'Rubik';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('./fonts/Rubik-Light.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Rubik Light Italic */
@font-face {
  font-family: 'Rubik';
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url('./fonts/Rubik-LightItalic.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* ==========================================================================
   Rubik Regular - 400 (Default)
   ========================================================================== */

/* Rubik Regular Normal */
@font-face {
  font-family: 'Rubik';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('./fonts/Rubik-Regular.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Rubik Regular Italic */
@font-face {
  font-family: 'Rubik';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url('./fonts/Rubik-Italic.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* ==========================================================================
   Rubik Medium - 500
   ========================================================================== */

/* Rubik Medium Normal */
@font-face {
  font-family: 'Rubik';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('./fonts/Rubik-Medium.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Rubik Medium Italic */
@font-face {
  font-family: 'Rubik';
  font-style: italic;
  font-weight: 500;
  font-display: swap;
  src: url('./fonts/Rubik-MediumItalic.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* ==========================================================================
   Rubik SemiBold - 600
   ========================================================================== */

/* Rubik SemiBold Normal */
@font-face {
  font-family: 'Rubik';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('./fonts/Rubik-SemiBold.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Rubik SemiBold Italic */
@font-face {
  font-family: 'Rubik';
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url('./fonts/Rubik-SemiBoldItalic.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* ==========================================================================
   Rubik Bold - 700
   ========================================================================== */

/* Rubik Bold Normal */
@font-face {
  font-family: 'Rubik';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('./fonts/Rubik-Bold.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Rubik Bold Italic */
@font-face {
  font-family: 'Rubik';
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url('./fonts/Rubik-BoldItalic.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* ==========================================================================
   Rubik ExtraBold - 800
   ========================================================================== */

/* Rubik ExtraBold Normal */
@font-face {
  font-family: 'Rubik';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url('./fonts/Rubik-ExtraBold.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Rubik ExtraBold Italic */
@font-face {
  font-family: 'Rubik';
  font-style: italic;
  font-weight: 800;
  font-display: swap;
  src: url('./fonts/Rubik-ExtraBoldItalic.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* ==========================================================================
   Rubik Black - 900
   ========================================================================== */

/* Rubik Black Normal */
@font-face {
  font-family: 'Rubik';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url('./fonts/Rubik-Black.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Rubik Black Italic */
@font-face {
  font-family: 'Rubik';
  font-style: italic;
  font-weight: 900;
  font-display: swap;
  src: url('./fonts/Rubik-BlackItalic.ttf') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}




/* ==========================================================================
   Enhanced CMB2 Metabox Styling
   ========================================================================== */

/* Editor area improvements */
.term-description-wrap .wp-editor-area {
    border: 1px solid #ddd;
    border-radius: 6px;
    font-family: 'Rubik', monospace !important;
    transition: border-color 0.2s ease;
}

.term-description-wrap .wp-editor-area:focus {
    border-color: var(--halacoupon-primary);
    box-shadow: 0 0 0 2px rgba(235, 191, 67, 0.1);
}

/* CMB2 row clearfix */
.cmb2-metabox .cmb-row::after {
    clear: both;
    content: " ";
    display: block;
}

/* Enhanced left column styling */
#col-left .cmb2-metabox .cmb-td {
    clear: both;
    padding: 8px 0;
    width: 100%;
}

#col-left .cmb2-metabox .cmb-th {
    padding: 8px 0 4px 0;
    font-weight: 600;
    color: #1d2327;
}

#col-left .cmb-row {
    padding: 12px 0;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f1;
}

#col-left .cmb-row:last-child {
    border-bottom: none;
}

/* Enhanced form inputs */
#col-left .cmb2-wrap .cmb-td .cmb2-text-medium {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-family: 'Rubik', sans-serif !important;
    font-size: 14px;
    transition: all 0.2s ease;
}

#col-left .cmb2-wrap .cmb-td .cmb2-text-medium:focus {
    border-color: var(--halacoupon-primary);
    box-shadow: 0 0 0 2px rgba(235, 191, 67, 0.1);
    outline: none;
}

/* Checkbox styling improvements */
.cmb-row.cmb-type-checkbox .cmb-td label,
.cmb-row.cmb-type-checkbox .cmb-td .cmb2-metabox-description {
    display: inline;
    font-family: 'Rubik', sans-serif !important;
}

.cmb-td {
    float: left;
    min-width: 100%;
}

.cmb2-wrap .cmb-td input.cmb2-text-medium {
    width: 95%;
}

/* Enhanced descriptions */
.cmb-td .cmb2-metabox-description {
    display: block;
    color: #646970;
    font-size: 13px;
    font-style: italic;
    margin-top: 6px;
    font-family: 'Rubik', sans-serif !important;
}

.cmb-type-checkbox .cmb-td .cmb2-metabox-description,
.cmb-type-checkbox .cmb-td label {
    display: inline-block;
    margin-left: 8px;
}
/* ==========================================================================
   Enhanced Admin Table Columns & Icons
   ========================================================================== */

/* Icon column improvements */
.column-icon {
    width: 50px;
    text-align: center;
}

.c-cat-icon {
    font-size: 28px;
    color: var(--halacoupon-primary);
    transition: color 0.2s ease;
}

.c-cat-icon:hover {
    color: var(--halacoupon-secondary);
}

.c-cat-icon .i {
    position: relative !important;
    top: auto !important;
    bottom: auto !important;
    left: auto !important;
    right: auto !important;
    margin: 0 !important;
    display: inline-block;
}

/* Enhanced URL column */
td.column-url {
    font-size: 13px;
    font-family: 'Rubik', sans-serif !important;
}

td.column-url span {
    display: block;
    margin-bottom: 2px;
    font-weight: 500;
    color: #1d2327;
}

td.column-url a {
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    max-width: 200px;
    color: var(--halacoupon-primary);
    text-decoration: none;
    transition: color 0.2s ease;
}

td.column-url a:hover {
    color: var(--halacoupon-secondary);
    text-decoration: underline;
}

td.column-url div:after {
    clear: both;
    content: " ";
    display: block;
}

/* Feature column */
.column-feature {
    width: 30px;
    text-align: center;
}

.column-feature .dashicons {
    color: var(--halacoupon-primary);
    font-size: 18px;
}

/* Out column */
.column-out {
    width: 80px;
    text-align: right !important;
    font-weight: 600;
    color: var(--halacoupon-accent);
}

/* Enhanced thumbnail column */
.column-thumb {
    width: 80px;
    text-align: center;
}

.column-thumb img {
    height: auto;
    max-width: 100%;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.column-thumb img:hover {
    transform: scale(1.05);
}

/* Enhanced input styling */
.cmb2-wrap input.cmb2-text-medium {
    width: 90%;
    font-family: 'Rubik', sans-serif !important;
}


/* ==========================================================================
   Enhanced Admin Typography & Base Styles
   ========================================================================== */

/* Base admin typography with Rubik font */
body,
#wpwrap,
.wp-admin,
.wp-admin input,
.wp-admin textarea,
.wp-admin select,
.wp-admin button {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif !important;
}

/* Enhanced headings with proper font weights */
h1, h2, h3, h4, h5, h6,
.wp-admin h1, .wp-admin h2, .wp-admin h3, .wp-admin h4, .wp-admin h5, .wp-admin h6 {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600;
    line-height: 1.3;
    color: #1d2327;
}

/* Specific heading weights */
h1, .wp-admin h1 { font-weight: 700; }
h2, .wp-admin h2 { font-weight: 600; }
h3, .wp-admin h3 { font-weight: 600; }
h4, .wp-admin h4 { font-weight: 500; }
h5, .wp-admin h5 { font-weight: 500; }
h6, .wp-admin h6 { font-weight: 500; }

/* Enhanced RTL Support */
.rtl body,
.rtl #wpwrap,
.rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6 {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.rtl #wpwrap {
    direction: rtl;
}

/* Brand color integration */
:root {
    --halacoupon-primary: #EBBF43;
    --halacoupon-secondary: #FFBC02;
    --halacoupon-accent: #000000;
    --halacoupon-text: #220e07;
    --halacoupon-background: #fdf9f7;
}


/* ==========================================================================
   Enhanced Taxonomy & CMB2 Group Styling
   ========================================================================== */

.cmb-td {
    min-width: 100%;
}

/* Enhanced store taxonomy layout */
.taxonomy-coupon_store #col-left {
    width: 60%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-right: 20px;
}

.taxonomy-coupon_store #col-right {
    width: 38%;
}

/* Enhanced repeatable groups */
.cmb-repeatable-group {
    background: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
    transition: box-shadow 0.2s ease;
}

.cmb-repeatable-group:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cmb-repeatable-group .cmb-group-title {
    margin: 0 !important;
    background: var(--halacoupon-primary);
    color: white;
    padding: 12px 16px;
    font-weight: 600;
    font-family: 'Rubik', sans-serif !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Enhanced remove button */
.cmb2-metabox button.dashicons-before.dashicons-no-alt.cmb-remove-group-row {
    top: 0.6em !important;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px;
    transition: all 0.2s ease;
}

.cmb2-metabox button.dashicons-before.dashicons-no-alt.cmb-remove-group-row:hover {
    background: #c82333;
    transform: scale(1.05);
}

/* Enhanced group content */
.cmb-type-group .cmb-repeatable-grouping,
.cmb2-postbox .cmb-repeatable-grouping {
    padding: 16px !important;
    background: white;
}

/* Enhanced edit tag styling */
#edittag .cmb-td {
    padding: 12px 0 !important;
}

#edittag .cmb-th label {
    font-weight: 600;
    color: #1d2327;
    font-family: 'Rubik', sans-serif !important;
}

/* Add group button enhancement */
.cmb-add-group-row {
    background: var(--halacoupon-primary) !important;
    color: white !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 10px 16px !important;
    font-family: 'Rubik', sans-serif !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.cmb-add-group-row:hover {
    background: var(--halacoupon-secondary) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(235, 191, 67, 0.3) !important;
}

/* 🎯 ENHANCED ADMIN BAR SPACING FIX FOR MODERN HEADER */
/* Handle header spacing when WordPress admin bar is displayed */

/* Desktop admin bar (32px height) */
@media screen and (min-width: 783px) {
  .admin-bar .admin-bar-aware {
    top: 32px !important;
  }

  .admin-bar .admin-bar-content {
    padding-top: calc(4rem + 32px) !important; /* 64px header + 32px admin bar */
  }

  .admin-bar .admin-bar-content.lg\:pt-20 {
    padding-top: calc(5rem + 32px) !important; /* 80px header + 32px admin bar */
  }

  /* Fix for Tailwind responsive classes */
  @media (min-width: 1024px) {
    .admin-bar .admin-bar-content {
      padding-top: calc(5rem + 32px) !important; /* 80px header + 32px admin bar */
    }
  }
}

/* Mobile admin bar (46px height) */
@media screen and (max-width: 782px) {
  .admin-bar .admin-bar-aware {
    top: 46px !important;
  }

  .admin-bar .admin-bar-content {
    padding-top: calc(4rem + 46px) !important; /* 64px header + 46px admin bar */
  }
}

/* 🎨 MODERN ADMIN BAR INTEGRATION */
/* Make the admin bar blend better with the modern header */
.admin-bar #wpadminbar {
  backdrop-filter: blur(8px) !important;
  background: rgba(35, 40, 45, 0.95) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

/* Smooth transition when admin bar appears/disappears */
.admin-bar-aware {
  transition: top 0.3s ease-out !important;
}

.admin-bar-content {
  transition: padding-top 0.3s ease-out !important;
}

/* 🔧 ADDITIONAL FIXES FOR ADMIN BAR CONFLICTS */
/* Ensure header stays above admin bar */
.admin-bar .admin-bar-aware {
  z-index: 99999 !important;
}

/* Fix any potential layout shifts */
.admin-bar body {
  margin-top: 0 !important;
}

/* Ensure proper spacing on all screen sizes */
@media screen and (min-width: 783px) {
  .admin-bar .admin-bar-content {
    min-height: calc(100vh - 32px - 4rem) !important;
  }
}

@media screen and (max-width: 782px) {
  .admin-bar .admin-bar-content {
    min-height: calc(100vh - 46px - 4rem) !important;
  }
}

/* ==========================================================================
   Enhanced Admin Buttons & Form Elements
   ========================================================================== */

/* Primary button styling */
.button-primary,
input[type="submit"].button-primary,
.wp-core-ui .button-primary {
    background: linear-gradient(135deg, var(--halacoupon-primary), var(--halacoupon-secondary)) !important;
    border: none !important;
    color: white !important;
    font-family: 'Rubik', sans-serif !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 2px 8px rgba(235, 191, 67, 0.2) !important;
}

.button-primary:hover,
input[type="submit"].button-primary:hover,
.wp-core-ui .button-primary:hover {
    background: linear-gradient(135deg, var(--halacoupon-secondary), var(--halacoupon-primary)) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(235, 191, 67, 0.3) !important;
}

/* Secondary button styling */
.button,
.wp-core-ui .button {
    font-family: 'Rubik', sans-serif !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
}

/* Enhanced form inputs */
input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="number"],
textarea,
select {
    font-family: 'Rubik', sans-serif !important;
    border-radius: 6px !important;
    border: 1px solid #ddd !important;
    transition: all 0.2s ease !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    border-color: var(--halacoupon-primary) !important;
    box-shadow: 0 0 0 2px rgba(235, 191, 67, 0.1) !important;
    outline: none !important;
}

/* Enhanced notices */
.notice,
.notice-success,
.notice-error,
.notice-warning,
.notice-info {
    border-radius: 6px !important;
    border-left-width: 4px !important;
    font-family: 'Rubik', sans-serif !important;
}

.notice-success {
    border-left-color: #00a32a !important;
    background: #f0f9f0 !important;
}

.notice-error {
    border-left-color: #d63638 !important;
    background: #fdf0f0 !important;
}

.notice-warning {
    border-left-color: var(--halacoupon-secondary) !important;
    background: #fffbf0 !important;
}

.notice-info {
    border-left-color: var(--halacoupon-primary) !important;
    background: #fdfcf7 !important;
}

/* Enhanced metabox styling */
.postbox,
.meta-box-sortables .postbox {
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e0e0e0 !important;
}

.postbox .hndle {
    font-family: 'Rubik', sans-serif !important;
    font-weight: 600 !important;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    border-bottom: 1px solid #e0e0e0 !important;
    border-radius: 8px 8px 0 0 !important;
}

/* Enhanced table styling */
.wp-list-table {
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.wp-list-table th {
    font-family: 'Rubik', sans-serif !important;
    font-weight: 600 !important;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
}

.wp-list-table td {
    font-family: 'Rubik', sans-serif !important;
}

.wp-list-table .alternate {
    background: #fafafa !important;
}

/* ==========================================================================
   Enhanced Admin Menu & Navigation
   ========================================================================== */

/* Admin menu font improvements */
#adminmenu,
#adminmenu a,
#adminmenu div.wp-menu-name {
    font-family: 'Rubik', sans-serif !important;
}

#adminmenu .wp-menu-name {
    font-weight: 500 !important;
}

#adminmenu .wp-submenu a {
    font-weight: 400 !important;
}

/* Enhanced admin bar */
#wpadminbar {
    font-family: 'Rubik', sans-serif !important;
}

#wpadminbar .ab-item {
    font-weight: 500 !important;
}

/* ==========================================================================
   Enhanced Media Library & Upload
   ========================================================================== */

/* Media library improvements */
.media-frame-title h1,
.media-frame-router .media-router a,
.media-frame-menu .media-menu-item {
    font-family: 'Rubik', sans-serif !important;
}

.attachment-details .setting input,
.attachment-details .setting textarea,
.attachment-details .setting select {
    font-family: 'Rubik', sans-serif !important;
}

/* ==========================================================================
   Enhanced Plugin & Theme Admin Pages
   ========================================================================== */

/* Plugin/theme cards */
.plugin-card,
.theme-browser .theme {
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.2s ease !important;
}

.plugin-card:hover,
.theme-browser .theme:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-2px) !important;
}

.plugin-card h3,
.plugin-card .plugin-title,
.theme-browser .theme .theme-name {
    font-family: 'Rubik', sans-serif !important;
    font-weight: 600 !important;
}

/* ==========================================================================
   Enhanced Responsive Design
   ========================================================================== */

/* Mobile admin improvements */
@media screen and (max-width: 782px) {
    .cmb2-wrap .cmb-td input.cmb2-text-medium {
        width: 100% !important;
        margin-bottom: 8px !important;
    }

    .taxonomy-coupon_store #col-left {
        width: 100% !important;
        margin-right: 0 !important;
        margin-bottom: 20px !important;
    }

    .taxonomy-coupon_store #col-right {
        width: 100% !important;
    }

    .column-thumb {
        width: 60px !important;
    }

    .column-icon {
        width: 40px !important;
    }
}

/* ==========================================================================
   Enhanced Accessibility & Focus States
   ========================================================================== */

/* Better focus indicators */
button:focus,
input:focus,
textarea:focus,
select:focus,
.button:focus {
    outline: 2px solid var(--halacoupon-primary) !important;
    outline-offset: 2px !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .button-primary {
        background: #000 !important;
        border: 2px solid #fff !important;
    }

    .notice {
        border-width: 2px !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ==========================================================================
   Print Styles for Admin
   ========================================================================== */

@media print {
    .wp-admin {
        font-family: 'Rubik', serif !important;
    }

    .button,
    .button-primary,
    .notice {
        background: transparent !important;
        border: 1px solid #000 !important;
        color: #000 !important;
    }
}