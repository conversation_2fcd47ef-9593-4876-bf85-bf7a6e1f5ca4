/*===========================
== Made By <PERSON>  ==
== for more inquiries      ==
== Mobile : 00201001248698 ==
===========================*/

@import url(../components/css/style.min.css);
@import url(../components/css/style-rtl.min.css);


@property --num {
	syntax: "<integer>";
	initial-value: 0;
	inherits: false;
  }
  .loading_Animate_Text {
	animation: counter 5s ease-in-out;
	counter-reset: num var(--num);
	font: 800 30px;
	color: #4c4c4ca8;
  }
  
  .loading_Animate_Text::after {
	content: counter(num);
  }
  
  @keyframes counter {
	from {
	  --num: 0;
	}
	to {
	  --num: 100;
	}
}


.redirect-page {
	font-size: 1.4rem;
	text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
	gap: 25px;
    height: 100vh;
    justify-content: center;
    background-color: var(--primary-color);
	color: #fff;
	padding-inline: 1rem;
}

.confirmation {
    display: flex;
	flex-direction: column;

    align-items: center;
    gap: 12px;
}




.loading_animate {
	box-sizing: border-box;
    width: 80px;
    height: 80px;
    border-radius: 100%;
    border: 10px solid #ffffff87;
    border-top-color: #ffffff;
    -webkit-animation: spin 1s infinite linear;
    animation: spin 1s infinite linear;
}

@keyframes spin {
	100% {
	  transform: rotate(360deg);
	}
  }


  code {
	  font-size: 1.2rem;
	  padding: 15px 55px;
	  background-color: #000;
	  border: 2px dashed;
  }

  