@import "tailwindcss";

/* @theme {
   --color-primary: #EBBF43;
  --color-secondary: #FFBC02;
  --color-accent: #000000;
  --color-background: #fdf9f7;
  --color-text: #220e07;
  --color-cta: #FFBC02;
} */

/* Font declarations are now enqueued separately via WordPress for better performance */


/* Custom base styles */
@layer base {

  /* Base HTML styles */
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Rubik', system-ui, sans-serif;
    color: #111827;
    background-color: #f9fafb;
  }

  /* RTL support - Rubik works well for both LTR and RTL */
  [dir="rtl"] {
    font-family: 'Rubik', system-ui, sans-serif;
  }

  /* Focus styles for accessibility */
  *:focus {
    outline: none;
  }

  /* Skip link for accessibility */
  .skip-link {
    position: absolute;
    top: -2.5rem;
    left: 1rem;
    z-index: 50;
    background-color: #EBBF43;
    color: #000000;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: all 0.2s;
    font-weight: 600;
  }

  .skip-link:focus {
    top: 1rem;
    background-color: #FFBC02;
  }

  /* ==========================================================================
     CONTENT TYPOGRAPHY SYSTEM - WordPress Content Areas Only
     ========================================================================== */

  /* Content Typography Wrapper - Apply to content areas only */
  .entry-content,
  .page-content,
  .post-content,
  .content-area,
  .single-content,
  .store-content,
  .coupon-content,
  article .content,
  .wp-block-group,
  .comment-content,
  .content-typography {
    font-family: 'Rubik', system-ui, sans-serif;
    color: #220e07;
    line-height: 1.6;
    font-size: 16px;
    font-weight: 400;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  [dir="rtl"] .entry-content,
  [dir="rtl"] .page-content,
  [dir="rtl"] .post-content,
  [dir="rtl"] .content-area,
  [dir="rtl"] .single-content,
  [dir="rtl"] .store-content,
  [dir="rtl"] .coupon-content,
  [dir="rtl"] article .content,
  [dir="rtl"] .wp-block-group,
  [dir="rtl"] .comment-content,
  [dir="rtl"] .content-typography {
    text-align: right;
  }

  /* ==========================================================================
     CONTENT TYPOGRAPHY - All Elements (Content Areas Only)
     ========================================================================== */

  /* Use efficient selector pattern for content areas */
  .entry-content,
  .page-content,
  .post-content,
  .content-area,
  .single-content,
  .store-content,
  .coupon-content,
  article .content,
  .wp-block-group,
  .comment-content,
  .content-typography {

    /* ==========================================================================
       ENHANCED HEADINGS SYSTEM - Professional Typography with SEO & Accessibility
       ========================================================================== */

    h1, h2, h3, h4, h5, h6 {
      font-family: 'Rubik', system-ui, sans-serif;
      font-weight: 700;
      line-height: 1.2;
      color: #220e07;
      margin: 0 0 1.25rem 0;
      letter-spacing: -0.025em;
      text-wrap: balance;
      scroll-margin-top: 2rem; /* Accessibility: Better anchor link positioning */
      position: relative;
      transition: all 0.3s ease;
    }

    /* Enhanced H1 - Primary Content Heading */
    h1 {
      font-size: clamp(2rem, 5vw, 3rem); /* Responsive scaling */
      line-height: 1.1;
      font-weight: 800;
      letter-spacing: -0.05em;
      margin-bottom: 2rem;
      background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 50%, #EBBF43 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
      text-shadow: 0 2px 4px rgba(235, 191, 67, 0.1);
      position: relative;
      padding-bottom: 0.5rem;
    }



    @keyframes slideIn {
      from { width: 0; opacity: 0; }
      to { width: 60px; opacity: 1; }
    }

    /* Enhanced H2 - Section Headings */
    h2 {
      font-size: clamp(1.5rem, 4vw, 2rem);
      line-height: 1.15;
      font-weight: 700;
      margin-bottom: 1.5rem;
      color: #220e07;
      position: relative;
      padding-left: 1rem;
    }



    /* Enhanced H3 - Subsection Headings */
    h3 {
      font-size: clamp(1.25rem, 3vw, 1.75rem);
      line-height: 1.25;
      font-weight: 600;
      margin-bottom: 1.25rem;
      color: #220e07;
      position: relative;
      padding-bottom: 0.5rem;
    }


    /* Enhanced H4 - Content Headings */
    h4 {
      font-size: clamp(1.125rem, 2.5vw, 1.375rem);
      line-height: 1.3;
      font-weight: 600;
      margin-bottom: 1rem;
      color: #220e07;
      position: relative;
    }


    /* Enhanced H5 - Minor Headings */
    h5 {
      font-size: clamp(1rem, 2vw, 1.25rem);
      line-height: 1.35;
      font-weight: 600;
      margin-bottom: 0.875rem;
      color: #220e07;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      font-size: 1rem;
    }

    /* Enhanced H6 - Smallest Headings */
    h6 {
      font-size: clamp(0.875rem, 1.5vw, 1rem);
      line-height: 1.4;
      font-weight: 600;
      margin-bottom: 0.75rem;
      color: rgba(34, 14, 7, 0.8);
      text-transform: uppercase;
      letter-spacing: 0.1em;
      font-size: 0.875rem;
    }

    /* Heading Hover Effects for Better UX */
    h1:hover, h2:hover, h3:hover, h4:hover, h5:hover, h6:hover {
      transform: translateY(-1px);
    }

    /* Accessibility: Focus styles for headings with anchor links */
    h1:focus, h2:focus, h3:focus, h4:focus, h5:focus, h6:focus {
      outline: 2px solid #EBBF43;
      outline-offset: 4px;
      border-radius: 4px;
    }

    /* ==========================================================================
       ENHANCED PARAGRAPH & TEXT SYSTEM - Professional Content Typography
       ========================================================================== */

    /* Enhanced Paragraphs */
    p {
      margin: 0 0 1.5rem 0;
      line-height: 1.75;
      color: #220e07;
      font-size: 1rem;
      text-wrap: pretty;
      font-weight: 400;
      letter-spacing: 0.01em;
      word-spacing: 0.05em;
      hyphens: auto;
      text-rendering: optimizeLegibility;
    }

    p:last-child {
      margin-bottom: 0;
    }

    /* Enhanced Lead Paragraphs */
    .lead, p.lead {
      font-size: clamp(1.125rem, 2.5vw, 1.375rem);
      line-height: 1.6;
      font-weight: 400;
      color: rgba(34, 14, 7, 0.9);
      margin-bottom: 2rem;
      padding: 1rem 0;
      border-left: 4px solid rgba(235, 191, 67, 0.3);
      padding-left: 1.5rem;
      background: linear-gradient(90deg, rgba(235, 191, 67, 0.03) 0%, transparent 100%);
      border-radius: 0 0.5rem 0.5rem 0;
    }

    /* Enhanced Small Text */
    small, .small {
      font-size: 0.875rem;
      line-height: 1.5;
      color: rgba(34, 14, 7, 0.7);
      font-weight: 400;
      letter-spacing: 0.02em;
    }

    /* Enhanced Text Emphasis */
    strong, b {
      font-weight: 700;
      color: #220e07;
      letter-spacing: 0.01em;
      text-shadow: 0 1px 2px rgba(34, 14, 7, 0.1);
    }

    em, i {
      font-style: italic;
      color: rgba(34, 14, 7, 0.9);
      font-weight: 450;
    }

    /* Enhanced Mark/Highlight */
    mark {
      background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 100%);
      color: #000000;
      padding: 0.2rem 0.4rem;
      border-radius: 0.375rem;
      font-weight: 500;
      box-shadow: 0 1px 3px rgba(235, 191, 67, 0.3);
      position: relative;
      display: inline-block;
      transform: rotate(-0.5deg);
      transition: transform 0.2s ease;
    }

    mark:hover {
      transform: rotate(0deg) scale(1.02);
    }

    /* Enhanced Text Selection */
    ::selection {
      background: rgba(235, 191, 67, 0.3);
      color: #220e07;
      text-shadow: none;
    }

    ::-moz-selection {
      background: rgba(235, 191, 67, 0.3);
      color: #220e07;
      text-shadow: none;
    }

    /* Drop Caps for Enhanced Typography */
    .drop-cap::first-letter {
      float: left;
      font-size: 4rem;
      line-height: 3rem;
      margin: 0.5rem 0.5rem 0 0;
      font-weight: 700;
      color: #EBBF43;
      text-shadow: 2px 2px 4px rgba(235, 191, 67, 0.3);
    }

    /* Enhanced Text Utilities */
    .text-large {
      font-size: 1.125rem;
      line-height: 1.6;
      font-weight: 400;
    }

    .text-small {
      font-size: 0.875rem;
      line-height: 1.5;
      color: rgba(34, 14, 7, 0.7);
    }

    .text-muted {
      color: rgba(34, 14, 7, 0.6);
      font-weight: 400;
    }

    .text-highlight {
      background: linear-gradient(120deg, transparent 0%, rgba(235, 191, 67, 0.2) 50%, transparent 100%);
      padding: 0.1rem 0.3rem;
      border-radius: 0.25rem;
    }

    /* ==========================================================================
       ENHANCED CODE & TECHNICAL ELEMENTS - Professional Code Typography
       ========================================================================== */

    /* Enhanced Inline Code */
    code {
      font-family: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      font-size: 0.875em;
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.1) 0%, rgba(255, 188, 2, 0.05) 100%);
      color: #996f01;
      padding: 0.2rem 0.5rem;
      border-radius: 0.375rem;
      border: 1px solid rgba(235, 191, 67, 0.2);
      font-weight: 500;
      letter-spacing: 0.02em;
      position: relative;
      display: inline-block;
      transition: all 0.2s ease;
      box-shadow: 0 1px 3px rgba(235, 191, 67, 0.1);
    }

    code:hover {
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.15) 0%, rgba(255, 188, 2, 0.08) 100%);
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(235, 191, 67, 0.2);
    }

    /* Enhanced Code Blocks */
    pre {
      font-family: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      border: 1px solid #e9ecef;
      border-radius: 0.75rem;
      padding: 1.5rem;
      margin: 2rem 0;
      overflow-x: auto;
      line-height: 1.6;
      font-size: 0.875rem;
      position: relative;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      border-left: 4px solid #EBBF43;
    }

    pre::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #EBBF43 0%, #FFBC02 100%);
      border-radius: 0.75rem 0.75rem 0 0;
    }

    pre code {
      background: none;
      border: none;
      padding: 0;
      color: #495057;
      font-size: inherit;
      box-shadow: none;
      display: block;
    }

    pre code:hover {
      background: none;
      transform: none;
      box-shadow: none;
    }

    /* Enhanced Keyboard Input */
    kbd {
      font-family: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      background: linear-gradient(135deg, #212529 0%, #343a40 100%);
      color: #ffffff;
      padding: 0.25rem 0.5rem;
      border-radius: 0.375rem;
      font-size: 0.75rem;
      font-weight: 600;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
      border: 1px solid #495057;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      position: relative;
      display: inline-block;
    }

    kbd:hover {
      transform: translateY(-1px);
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    /* Enhanced Abbreviations */
    abbr[title] {
      text-decoration: underline dotted rgba(235, 191, 67, 0.6);
      cursor: help;
      border-bottom: none;
      position: relative;
      color: #EBBF43;
      font-weight: 500;
    }

    abbr[title]:hover {
      text-decoration: underline solid #EBBF43;
      color: #FFBC02;
    }

    /* Enhanced Subscript and Superscript */
    sub, sup {
      font-size: 0.75em;
      line-height: 0;
      position: relative;
      vertical-align: baseline;
      font-weight: 500;
      color: rgba(34, 14, 7, 0.8);
    }

    sub {
      bottom: -0.25em;
    }

    sup {
      top: -0.5em;
    }

    /* Code Language Labels */
    pre[data-language]::after {
      content: attr(data-language);
      position: absolute;
      top: 0.75rem;
      right: 1rem;
      font-size: 0.75rem;
      color: rgba(34, 14, 7, 0.6);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      font-weight: 600;
      background: rgba(235, 191, 67, 0.1);
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      border: 1px solid rgba(235, 191, 67, 0.2);
    }

    /* Copy Code Button Styling */
    .code-block-wrapper {
      position: relative;
    }

    .copy-code-btn {
      position: absolute;
      top: 0.75rem;
      right: 0.75rem;
      background: rgba(235, 191, 67, 0.9);
      color: #000000;
      border: none;
      padding: 0.375rem 0.75rem;
      border-radius: 0.375rem;
      font-size: 0.75rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      opacity: 0;
      transform: translateY(-10px);
    }

    .code-block-wrapper:hover .copy-code-btn {
      opacity: 1;
      transform: translateY(0);
    }

    .copy-code-btn:hover {
      background: #FFBC02;
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(235, 191, 67, 0.3);
    }

    /* ==========================================================================
       ENHANCED LIST SYSTEM - Professional List Typography with Animations
       ========================================================================== */

    /* Enhanced Base List Styling */
    ul, ol {
      margin: 0 0 1.5rem 0;
      padding: 0;
      line-height: 1.7;
      padding-left: 2rem;
      position: relative;
    }

    /* Enhanced List Items */
    li {
      margin-bottom: 0.75rem;
      color: #220e07;
      line-height: 1.7;
      position: relative;
      transition: all 0.2s ease;
      padding: 0.25rem 0;
    }

    li:last-child {
      margin-bottom: 0;
    }

    li:hover {
      transform: translateX(2px);
      color: rgba(34, 14, 7, 0.9);
    }

    /* Enhanced Nested Lists */
    ul ul, ol ol, ul ol, ol ul {
      margin: 0.75rem 0;
      padding-left: 1.5rem;
    }

    /* Enhanced Unordered Lists */
    ul {
      list-style: none;
    }

    ul > li {
      position: relative;
    }

    /* Enhanced Custom Bullets */
    ul > li::before {
      content: '';
      position: absolute;
      top: 0.85em;
      left: -1.5rem;
      width: 0.5rem;
      height: 0.5rem;
      background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 100%);
      border-radius: 50%;
      transform: translateY(-50%);
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(235, 191, 67, 0.3);
    }

    ul > li:hover::before {
      transform: translateY(-50%) scale(1.2);
      box-shadow: 0 3px 6px rgba(235, 191, 67, 0.4);
    }

    /* Enhanced Nested Bullets */
    ul ul > li::before {
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.7) 0%, rgba(255, 188, 2, 0.7) 100%);
      width: 0.375rem;
      height: 0.375rem;
    }

    ul ul ul > li::before {
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.5) 0%, rgba(255, 188, 2, 0.5) 100%);
      border-radius: 0;
      transform: translateY(-50%) rotate(45deg);
      width: 0.3rem;
      height: 0.3rem;
    }

    /* Enhanced Ordered Lists */
    ol {
      list-style: none;
      counter-reset: list-counter;
    }

    ol > li {
      position: relative;
      counter-increment: list-counter;
    }

    /* Enhanced Custom Numbers */
    ol > li::before {
      content: counter(list-counter);
      position: absolute;
      left: -2rem;
      top: 0.1rem;
      width: 1.5rem;
      height: 1.5rem;
      background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 100%);
      color: #000000;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8rem;
      font-weight: 700;
      line-height: 1;
      transition: all 0.3s ease;
      box-shadow: 0 2px 6px rgba(235, 191, 67, 0.3);
      border: 2px solid #ffffff;
    }

    ol > li:hover::before {
      transform: scale(1.1);
      box-shadow: 0 4px 8px rgba(235, 191, 67, 0.4);
    }

    /* Enhanced Nested Ordered Lists */
    ol ol {
      counter-reset: nested-counter;
    }

    ol ol > li {
      counter-increment: nested-counter;
    }

    ol ol > li::before {
      content: counter(nested-counter, lower-alpha);
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.8) 0%, rgba(255, 188, 2, 0.8) 100%);
      font-size: 0.7rem;
      width: 1.25rem;
      height: 1.25rem;
    }

    ol ol ol > li::before {
      content: counter(nested-counter, lower-roman);
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.6) 0%, rgba(255, 188, 2, 0.6) 100%);
      font-size: 0.65rem;
      width: 1.125rem;
      height: 1.125rem;
    }

    /* Enhanced Description Lists */
    dl {
      margin: 0 0 1.5rem 0;
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.02) 0%, transparent 100%);
      padding: 1rem;
      border-radius: 0.5rem;
      border-left: 3px solid rgba(235, 191, 67, 0.3);
    }

    dt {
      font-weight: 700;
      color: #220e07;
      margin-bottom: 0.5rem;
      font-size: 1.05rem;
      position: relative;
      padding-left: 1rem;
    }

    dt::before {
      content: '▸';
      position: absolute;
      left: 0;
      color: #EBBF43;
      font-weight: 700;
    }

    dd {
      margin: 0 0 1.25rem 0;
      padding-left: 1.5rem;
      color: rgba(34, 14, 7, 0.8);
      line-height: 1.7;
      border-left: 2px solid rgba(235, 191, 67, 0.1);
      padding-left: 1rem;
    }

    dd:last-child {
      margin-bottom: 0;
    }

    /* Enhanced Special List Styles */
    .list-unstyled {
      list-style: none;
      padding-left: 0;
    }

    .list-unstyled li::before {
      display: none;
    }

    .list-unstyled li:hover {
      transform: none;
    }

    .list-inline {
      list-style: none;
      padding-left: 0;
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .list-inline li::before {
      display: none;
    }

    .list-inline li {
      margin-bottom: 0;
      background: rgba(235, 191, 67, 0.1);
      padding: 0.5rem 1rem;
      border-radius: 2rem;
      transition: all 0.2s ease;
      border: 1px solid rgba(235, 191, 67, 0.2);
    }

    .list-inline li:hover {
      background: rgba(235, 191, 67, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(235, 191, 67, 0.2);
    }

    /* Enhanced Checklist Style */
    .list-checklist {
      list-style: none;
      padding-left: 0;
    }

    .list-checklist li {
      position: relative;
      padding-left: 2rem;
    }

    .list-checklist li::before {
      content: '✓';
      position: absolute;
      left: 0;
      top: 0.1rem;
      width: 1.25rem;
      height: 1.25rem;
      background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
      color: #ffffff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.75rem;
      font-weight: 700;
      box-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);
    }

    /* ==========================================================================
       ENHANCED LINKS SYSTEM - Professional Links with SEO & Accessibility
       ========================================================================== */

    /* Enhanced Base Link Styling */
    a {
      color: #EBBF43;
      text-decoration: none;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      font-weight: 500;
      border-radius: 0.25rem;
      padding: 0.125rem 0.25rem;
      margin: -0.125rem -0.25rem;
      display: inline-block;
      line-height: 1.4;
    }

    a:hover {
      color: #FFBC02;
      text-decoration: none;
      transform: translateY(-1px);
      text-shadow: 0 1px 3px rgba(235, 191, 67, 0.3);
    }

    /* Enhanced Focus States for Accessibility */
    a:focus {
      outline: 3px solid #EBBF43;
      outline-offset: 2px;
      border-radius: 0.375rem;
      background: rgba(235, 191, 67, 0.1);
      box-shadow: 0 0 0 4px rgba(235, 191, 67, 0.2);
    }

    a:focus:not(:focus-visible) {
      outline: none;
      box-shadow: none;
      background: transparent;
    }

    a:focus-visible {
      outline: 3px solid #EBBF43;
      outline-offset: 2px;
      background: rgba(235, 191, 67, 0.1);
    }

    /* Enhanced Animated Underline Effect */
    .link-underline, a.link-underline {
      position: relative;
      overflow: hidden;
    }

    .link-underline::after, a.link-underline::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 2px;
      background: linear-gradient(90deg, #EBBF43 0%, #FFBC02 100%);
      transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 1px;
    }

    .link-underline:hover::after, a.link-underline:hover::after {
      width: 100%;
    }

    /* Enhanced External Link Styling with SEO Benefits */
    a[href^="http"]:not([href*="l-hala-coupon.local"])::after,
    .external-link::after {
      content: '↗';
      display: inline-block;
      margin-left: 0.375rem;
      font-size: 0.8em;
      opacity: 0.7;
      transition: all 0.2s ease;
      vertical-align: super;
      font-weight: 700;
    }

    a[href^="http"]:not([href*="l-hala-coupon.local"]):hover::after,
    .external-link:hover::after {
      opacity: 1;
      transform: translateY(-1px);
    }

    /* Enhanced Button-Style Links */
    .link-button, a.link-button {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.875rem 1.75rem;
      background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 100%);
      color: #000000;
      border-radius: 0.5rem;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 12px rgba(235, 191, 67, 0.3);
      border: 2px solid transparent;
      position: relative;
      overflow: hidden;
    }

    .link-button::before, a.link-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    .link-button:hover, a.link-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(235, 191, 67, 0.4);
      color: #000000;
    }

    .link-button:hover::before, a.link-button:hover::before {
      left: 100%;
    }

    .link-button:active, a.link-button:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(235, 191, 67, 0.3);
    }

    /* Enhanced Link Variants */
    .link-muted, a.link-muted {
      color: rgba(34, 14, 7, 0.6);
      font-weight: 400;
    }

    .link-muted:hover, a.link-muted:hover {
      color: #EBBF43;
      font-weight: 500;
    }

    .link-dark, a.link-dark {
      color: #220e07;
      font-weight: 500;
    }

    .link-dark:hover, a.link-dark:hover {
      color: #EBBF43;
    }

    .link-subtle, a.link-subtle {
      color: inherit;
      text-decoration: underline;
      text-decoration-color: rgba(235, 191, 67, 0.4);
      text-underline-offset: 0.25rem;
      text-decoration-thickness: 1px;
    }

    .link-subtle:hover, a.link-subtle:hover {
      color: #EBBF43;
      text-decoration-color: #EBBF43;
      text-decoration-thickness: 2px;
    }

    /* Enhanced CTA Links */
    .link-cta, a.link-cta {
      position: relative;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      font-size: 0.875rem;
    }

    .link-cta::after, a.link-cta::after {
      content: '→';
      margin-left: 0.5rem;
      transition: transform 0.2s ease;
      display: inline-block;
    }

    .link-cta:hover::after, a.link-cta:hover::after {
      transform: translateX(0.25rem);
    }

    /* Enhanced Breadcrumb Links */
    .breadcrumb-link, a.breadcrumb-link {
      color: rgba(34, 14, 7, 0.6);
      font-size: 0.875rem;
      font-weight: 400;
      text-decoration: none;
      transition: all 0.2s ease;
    }

    .breadcrumb-link:hover, a.breadcrumb-link:hover {
      color: #EBBF43;
      text-decoration: underline;
      text-underline-offset: 0.25rem;
    }

    /* Enhanced Skip Links for Accessibility */
    .skip-link, a.skip-link {
      position: absolute;
      top: -3rem;
      left: 1rem;
      z-index: 1000;
      background: #EBBF43;
      color: #000000;
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(235, 191, 67, 0.4);
    }

    .skip-link:focus, a.skip-link:focus {
      top: 1rem;
      outline: 3px solid #000000;
      outline-offset: 2px;
    }

    /* Enhanced Link States for Better UX */
    a:visited {
      color: rgba(235, 191, 67, 0.8);
    }

    a:visited:hover {
      color: #FFBC02;
    }

    /* Enhanced Link Accessibility Attributes */
    a[aria-describedby] {
      border-bottom: 1px dotted rgba(235, 191, 67, 0.5);
    }

    a[aria-describedby]:hover {
      border-bottom-style: solid;
      border-bottom-color: #EBBF43;
    }

    /* ==========================================================================
       ENHANCED BLOCKQUOTES - Professional Quote Typography
       ========================================================================== */

    /* Enhanced Blockquote Styling */
    blockquote {
      margin: 2.5rem 0;
      padding: 2rem;
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.08) 0%, rgba(255, 188, 2, 0.03) 100%);
      border-radius: 1rem;
      position: relative;
      font-size: clamp(1.125rem, 2.5vw, 1.375rem);
      line-height: 1.6;
      color: rgba(34, 14, 7, 0.9);
      font-style: italic;
      border-left: 6px solid #EBBF43;
      padding-left: 2.5rem;
      box-shadow: 0 4px 20px rgba(235, 191, 67, 0.1);
      transition: all 0.3s ease;
    }

    blockquote:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 30px rgba(235, 191, 67, 0.15);
    }

    /* Enhanced Quote Marks */
    blockquote::before {
      content: '"';
      position: absolute;
      top: 1rem;
      left: 1rem;
      font-size: 4rem;
      line-height: 1;
      color: rgba(235, 191, 67, 0.2);
      font-family: Georgia, serif;
      font-weight: 700;
      z-index: 0;
    }

    blockquote::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, #EBBF43 0%, #FFBC02 50%, #EBBF43 100%);
      border-radius: 1rem 1rem 0 0;
    }

    /* Enhanced Blockquote Content */
    blockquote p {
      margin-bottom: 1.25rem;
      position: relative;
      z-index: 1;
      font-weight: 400;
      text-shadow: 0 1px 2px rgba(235, 191, 67, 0.1);
    }

    blockquote p:last-of-type {
      margin-bottom: 0;
    }

    blockquote p:first-child {
      margin-top: 0.5rem;
    }

    /* Enhanced Citations */
    blockquote cite,
    blockquote footer {
      display: block;
      margin-top: 1.5rem;
      font-size: 0.9rem;
      font-style: normal;
      color: rgba(34, 14, 7, 0.7);
      font-weight: 500;
      position: relative;
      padding-top: 1rem;
      border-top: 1px solid rgba(235, 191, 67, 0.2);
    }

    blockquote cite::before,
    blockquote footer::before {
      content: '— ';
      color: #EBBF43;
      font-weight: 700;
      margin-right: 0.25rem;
    }

    /* Enhanced Pull Quotes */
    .pullquote {
      float: right;
      width: 45%;
      margin: 0 0 1.5rem 2rem;
      padding: 1.5rem;
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.1) 0%, rgba(255, 188, 2, 0.05) 100%);
      border-radius: 0.75rem;
      font-size: 1.125rem;
      line-height: 1.5;
      border: 2px solid rgba(235, 191, 67, 0.2);
      position: relative;
      box-shadow: 0 4px 12px rgba(235, 191, 67, 0.1);
    }

    .pullquote::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      height: 4px;
      background: linear-gradient(90deg, #EBBF43 0%, #FFBC02 100%);
      border-radius: 0.75rem 0.75rem 0 0;
    }

	.bg-primary {
		background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 100%);
	}

    /* Enhanced Testimonial Style */
    .testimonial {
      background: linear-gradient(135deg, #ffffff 0%, rgba(235, 191, 67, 0.02) 100%);
      border: 1px solid rgba(235, 191, 67, 0.2);
      border-radius: 1rem;
      padding: 2rem;
      margin: 2rem 0;
      position: relative;
      box-shadow: 0 4px 20px rgba(235, 191, 67, 0.08);
    }

    .testimonial::before {
      content: '★★★★★';
      position: absolute;
      top: 1rem;
      right: 1.5rem;
      color: #EBBF43;
      font-size: 0.875rem;
      letter-spacing: 0.1em;
    }

    .testimonial blockquote {
      margin: 0;
      padding: 0;
      background: none;
      border: none;
      box-shadow: none;
      font-size: 1.125rem;
    }

    .testimonial blockquote::before,
    .testimonial blockquote::after {
      display: none;
    }

    /* ==========================================================================
       ENHANCED TABLES - Professional Table Typography with Accessibility
       ========================================================================== */

    /* Enhanced Table Container */
    table {
      width: 100%;
      margin: 2rem 0;
      border-collapse: collapse;
      border-spacing: 0;
      background: #ffffff;
      border-radius: 1rem;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #e5e7eb;
      position: relative;
      transition: all 0.3s ease;
    }

    table:hover {
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }

    /* Enhanced Table Caption */
    caption {
      caption-side: top;
      padding: 1rem;
      font-size: 1.125rem;
      font-weight: 600;
      color: #220e07;
      text-align: left;
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.05) 0%, rgba(255, 188, 2, 0.02) 100%);
      border-radius: 1rem 1rem 0 0;
      border-bottom: 2px solid rgba(235, 191, 67, 0.2);
    }

    /* Enhanced Table Header */
    thead {
      background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 50%, #EBBF43 100%);
      position: relative;
    }

    thead::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.2) 50%, rgba(0, 0, 0, 0.1) 100%);
    }

    /* Enhanced Table Headers */
    th {
      padding: 1.25rem 1.5rem;
      text-align: left;
      font-weight: 700;
      color: #000000;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      position: relative;
      transition: all 0.2s ease;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    th:hover {
      background: rgba(0, 0, 0, 0.05);
    }

    /* Sortable Table Headers */
    th.sortable {
      cursor: pointer;
      user-select: none;
    }

    th.sortable::after {
      content: '⇅';
      margin-left: 0.5rem;
      opacity: 0.5;
      font-size: 0.75em;
      transition: opacity 0.2s ease;
    }

    th.sortable:hover::after {
      opacity: 1;
    }

    th.sort-asc::after {
      content: '↑';
      opacity: 1;
    }

    th.sort-desc::after {
      content: '↓';
      opacity: 1;
    }

    /* Enhanced Table Body */
    tbody tr {
      border-bottom: 1px solid #f3f4f6;
      transition: all 0.3s ease;
      position: relative;
    }

    tbody tr:hover {
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.05) 0%, rgba(255, 188, 2, 0.02) 100%);
      transform: scale(1.01);
      box-shadow: 0 2px 8px rgba(235, 191, 67, 0.1);
    }

    tbody tr:last-child {
      border-bottom: none;
    }

    tbody tr:nth-child(even) {
      background: rgba(248, 249, 250, 0.5);
    }

    tbody tr:nth-child(even):hover {
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.05) 0%, rgba(255, 188, 2, 0.02) 100%);
    }

    /* Enhanced Table Cells */
    td {
      padding: 1rem 1.5rem;
      color: #220e07;
      font-size: 0.9rem;
      line-height: 1.6;
      vertical-align: top;
      transition: all 0.2s ease;
      position: relative;
    }

    /* Enhanced Table Footer */
    tfoot {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-top: 2px solid rgba(235, 191, 67, 0.2);
    }

    tfoot td, tfoot th {
      padding: 1rem 1.5rem;
      font-weight: 600;
      color: rgba(34, 14, 7, 0.8);
      font-size: 0.875rem;
      border-top: 1px solid #dee2e6;
    }

    /* Enhanced Responsive Tables */
    .table-responsive {
      overflow-x: auto;
      margin: 2rem 0;
      border-radius: 1rem;
      border: 1px solid #e5e7eb;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .table-responsive table {
      margin: 0;
      border: none;
      border-radius: 0;
      box-shadow: none;
      min-width: 600px;
    }

    /* Enhanced Table Variants */
    .table-striped tbody tr:nth-child(odd) {
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.02) 0%, rgba(255, 188, 2, 0.01) 100%);
    }

    .table-bordered {
      border: 2px solid rgba(235, 191, 67, 0.2);
    }

    .table-bordered th,
    .table-bordered td {
      border: 1px solid rgba(235, 191, 67, 0.1);
    }

    .table-borderless th,
    .table-borderless td,
    .table-borderless thead th,
    .table-borderless tbody + tbody {
      border: none;
    }

    /* Enhanced Table Sizes */
    .table-sm th,
    .table-sm td {
      padding: 0.75rem;
      font-size: 0.8125rem;
    }

    .table-lg th,
    .table-lg td {
      padding: 1.5rem;
      font-size: 1rem;
    }

    /* Enhanced Table Cell Alignment */
    .text-center {
      text-align: center !important;
    }

    .text-right {
      text-align: right !important;
    }

    .text-left {
      text-align: left !important;
    }

    /* Enhanced Table Status Indicators */
    .table-status-success {
      background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(22, 163, 74, 0.05) 100%);
      border-left: 4px solid #22c55e;
    }

    .table-status-warning {
      background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
      border-left: 4px solid #f59e0b;
    }

    .table-status-error {
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
      border-left: 4px solid #ef4444;
    }

    /* ==========================================================================
       ENHANCED WORDPRESS COMPONENTS - Professional WordPress Elements
       ========================================================================== */

    /* Enhanced Image Alignment */
    .alignleft {
      float: left;
      margin: 0 2rem 1.5rem 0;
      border-radius: 0.75rem;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .alignleft:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .alignright {
      float: right;
      margin: 0 0 1.5rem 2rem;
      border-radius: 0.75rem;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .alignright:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .aligncenter {
      display: block;
      margin: 2rem auto;
      text-align: center;
      border-radius: 0.75rem;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .aligncenter:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    /* Enhanced WordPress Image Captions */
    .wp-caption {
      max-width: 100%;
      background: #ffffff;
      border-radius: 1rem;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      margin: 2rem 0;
      border: 1px solid rgba(235, 191, 67, 0.1);
      transition: all 0.3s ease;
      position: relative;
    }

    .wp-caption:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
    }

    .wp-caption::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #EBBF43 0%, #FFBC02 100%);
      border-radius: 1rem 1rem 0 0;
    }

    .wp-caption img {
      width: 100%;
      height: auto;
      display: block;
      transition: all 0.3s ease;
    }

    .wp-caption:hover img {
      transform: scale(1.02);
    }

    .wp-caption-text {
      padding: 1rem 1.5rem;
      font-size: 0.9rem;
      color: rgba(34, 14, 7, 0.7);
      line-height: 1.6;
      font-style: italic;
      text-align: center;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      position: relative;
      font-weight: 400;
    }

    /* Enhanced WordPress Galleries */
    .gallery {
      display: grid;
      gap: 1.5rem;
      margin: 2rem 0;
      padding: 1rem;
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.02) 0%, transparent 100%);
      border-radius: 1rem;
      border: 1px solid rgba(235, 191, 67, 0.1);
    }

    .gallery-columns-1 { grid-template-columns: 1fr; }
    .gallery-columns-2 { grid-template-columns: repeat(2, 1fr); }
    .gallery-columns-3 { grid-template-columns: repeat(3, 1fr); }
    .gallery-columns-4 { grid-template-columns: repeat(4, 1fr); }
    .gallery-columns-5 { grid-template-columns: repeat(5, 1fr); }
    .gallery-columns-6 { grid-template-columns: repeat(6, 1fr); }

    .gallery-item {
      background: #ffffff;
      border-radius: 0.75rem;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid rgba(235, 191, 67, 0.1);
      position: relative;
    }

    .gallery-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #EBBF43 0%, #FFBC02 100%);
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    .gallery-item:hover {
      transform: translateY(-4px) scale(1.02);
      box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
    }

    .gallery-item:hover::before {
      transform: scaleX(1);
    }

    .gallery-item img {
      width: 100%;
      height: auto;
      display: block;
      transition: all 0.3s ease;
    }

    .gallery-item:hover img {
      transform: scale(1.05);
    }

    .gallery-caption {
      padding: 0.75rem;
      font-size: 0.8rem;
      color: rgba(34, 14, 7, 0.7);
      text-align: center;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      font-weight: 400;
      line-height: 1.4;
    }

    /* Enhanced WordPress Video Embeds */
    .wp-video,
    .wp-audio-shortcode {
      margin: 2rem 0;
      border-radius: 1rem;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(235, 191, 67, 0.1);
    }

    .wp-video video {
      width: 100%;
      height: auto;
      border-radius: 1rem;
      transition: all 0.3s ease;
    }

    .wp-video:hover video {
      transform: scale(1.01);
    }

    /* Enhanced WordPress More Link */
    .more-link {
      display: inline-flex;
      align-items: center;
      gap: 0.75rem;
      color: #EBBF43;
      font-weight: 600;
      text-decoration: none;
      margin-top: 1.5rem;
      padding: 0.75rem 1.5rem;
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.1) 0%, rgba(255, 188, 2, 0.05) 100%);
      border-radius: 2rem;
      border: 1px solid rgba(235, 191, 67, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .more-link::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(235, 191, 67, 0.1), transparent);
      transition: left 0.6s ease;
    }

    .more-link:hover {
      color: #FFBC02;
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(235, 191, 67, 0.2);
      background: linear-gradient(135deg, rgba(235, 191, 67, 0.15) 0%, rgba(255, 188, 2, 0.08) 100%);
    }

    .more-link:hover::before {
      left: 100%;
    }

    .more-link::after {
      content: '→';
      transition: transform 0.3s ease;
      font-weight: 700;
    }

    .more-link:hover::after {
      transform: translateX(0.25rem);
    }

  } /* End of content typography wrapper */

  /* ==========================================================================
     RTL SUPPORT FOR CONTENT TYPOGRAPHY
     ========================================================================== */

  [dir="rtl"] .entry-content,
  [dir="rtl"] .page-content,
  [dir="rtl"] .post-content,
  [dir="rtl"] .content-area,
  [dir="rtl"] .single-content,
  [dir="rtl"] .store-content,
  [dir="rtl"] .coupon-content,
  [dir="rtl"] article .content,
  [dir="rtl"] .wp-block-group,
  [dir="rtl"] .comment-content,
  [dir="rtl"] .content-typography {

    h1, h2, h3, h4, h5, h6 {
      font-family: 'Rubik', system-ui, sans-serif;
      text-align: right;
    }

    /* RTL List Support */
    ul, ol {
      padding-left: 0;
      padding-right: 1.5rem;
    }

    ul ul, ol ol, ul ol, ol ul {
      padding-left: 0;
      padding-right: 1.25rem;
    }

    ul > li::before {
      left: auto;
      right: -1rem;
    }

    ol > li::before {
      left: auto;
      right: -1.5rem;
    }

    dd {
      padding-left: 0;
      padding-right: 1rem;
    }

    .list-unstyled {
      padding-right: 0;
    }

    /* RTL Blockquote Support */
    blockquote {
      border-left: none;
      border-right: 4px solid #EBBF43;
      padding-left: 1.5rem;
      padding-right: 2rem;
    }

    blockquote::before {
      left: auto;
      right: 0.5rem;
      content: '"';
    }

    /* RTL Table Support */
    th {
      text-align: right;
    }

    /* RTL WordPress Components */
    .alignleft {
      float: right;
      margin: 0 0 1rem 1.5rem;
    }

    .alignright {
      float: left;
      margin: 0 1.5rem 1rem 0;
    }

  } /* End of RTL content typography */

  /* ==========================================================================
     STORE CONTENT BLOCKS - Enhanced Structured Content System
     ========================================================================== */

  /* Store Content Blocks Container */
  .store-content-blocks-container {
    margin: 2rem 0;
    position: relative;
  }

  /* Store Content Header and Metadata */
  .store-content-header {
    margin-bottom: 1rem;
  }

  .store-content-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 0.75rem 1rem;
    background: rgba(235, 191, 67, 0.05);
    border: 1px solid rgba(235, 191, 67, 0.15);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    color: rgba(34, 14, 7, 0.8);
  }

  .reading-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
  }

  .reading-time-icon {
    color: #EBBF43;
    flex-shrink: 0;
  }

  /* Store Content Table of Contents - Collapsible Navigation */
  .store-toc-container {
    background: linear-gradient(135deg, rgba(235, 191, 67, 0.08) 0%, rgba(255, 188, 2, 0.03) 100%);
    border: 1px solid rgba(235, 191, 67, 0.25);
    border-radius: 1rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  }

  .store-toc-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #EBBF43 0%, #FFBC02 50%, #EBBF43 100%);
    border-radius: 1rem 1rem 0 0;
  }

  /* Table of Contents Header */
  .store-toc-header {
    position: relative;
    z-index: 2;
  }

  .store-toc-toggle {
    width: 100%;
    background: transparent;
    border: none;
    padding: 1.25rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    color: #220e07;
    text-align: left;
  }

  .store-toc-toggle:hover {
    background: rgba(235, 191, 67, 0.05);
    color: #EBBF43;
  }

  .store-toc-toggle:focus {
    outline: 2px solid #EBBF43;
    outline-offset: -2px;
    background: rgba(235, 191, 67, 0.08);
  }

  .store-toc-toggle[aria-expanded="true"] {
    background: rgba(235, 191, 67, 0.05);
    border-bottom: 1px solid rgba(235, 191, 67, 0.2);
  }

  /* Toggle Icon */
  .store-toc-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    color: #EBBF43;
  }

  .store-toc-toggle[aria-expanded="false"] .toc-icon-expanded,
  .store-toc-toggle[aria-expanded="true"] .toc-icon-collapsed {
    display: none;
  }

  /* Toggle Title and Count */
  .store-toc-title {
    flex: 1;
    font-weight: 600;
    color: inherit;
  }

  .store-toc-count {
    font-size: 0.875rem;
    color: rgba(34, 14, 7, 0.7);
    font-weight: 500;
  }

  /* Navigation List - Collapsed by Default */
  .store-toc-nav {
    max-height: 0;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
  }

  .store-toc-toggle[aria-expanded="true"] + * .store-toc-nav {
    max-height: none;
    padding: 1rem 0;
  }

  .store-content-nav-list {
    list-style: none;
    padding: 0 1.5rem;
    margin: 0;
    counter-reset: toc-counter;
  }

  .store-content-nav-list[aria-hidden="false"] {
    display: block;
  }

  .store-content-nav-item {
    margin: 0;
    counter-increment: toc-counter;
    border-bottom: 1px solid rgba(235, 191, 67, 0.1);
  }

  .store-content-nav-item:last-child {
    border-bottom: none;
  }

  .store-content-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    color: #220e07;
    text-decoration: none;
    font-weight: 500;
    position: relative;
    border-radius: 0.5rem;
    margin: 0.25rem 0;
  }

  .store-content-nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 100%);
    border-radius: 0.5rem 0 0 0.5rem;
  }

  .store-content-nav-link:hover,
  .store-content-nav-link:focus {
    background: rgba(235, 191, 67, 0.08);
    color: #EBBF43;
  }

  .store-content-nav-link:hover::before,
  .store-content-nav-link:focus::before {
    width: 4px;
  }

  .store-content-nav-link:focus {
    outline: 2px solid #EBBF43;
    outline-offset: 2px;
  }

  .store-content-nav-link.active {
    background: rgba(235, 191, 67, 0.12);
    color: #EBBF43;
    font-weight: 600;
  }

  .store-content-nav-link.active::before {
    width: 4px;
  }

  /* Section Numbers */
  .nav-section-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 100%);
    color: #000000;
    border-radius: 50%;
    font-size: 0.75rem;
    font-weight: 700;
    flex-shrink: 0;
  }

  .nav-section-title {
    flex: 1;
    line-height: 1.4;
  }

  /* SEO Anchor Points */
  .seo-anchor,
  .visually-hidden {
    position: absolute;
    top: -100px; /* Offset for fixed headers */
    visibility: hidden;
    pointer-events: none;
  }

  /* Skip Link */
  .store-toc-skip {
    padding: 0 1.5rem 1rem;
  }

  .skip-link {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: rgba(235, 191, 67, 0.1);
    color: #EBBF43;
    text-decoration: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .skip-link:hover,
  .skip-link:focus {
    background: rgba(235, 191, 67, 0.2);
  }

  /* Store Content Blocks Wrapper */
  .store-content-blocks-wrapper {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  /* Individual Content Block */
  .store-content-block {
    background: linear-gradient(135deg, #ffffff 0%, rgba(235, 191, 67, 0.01) 100%);
    border: 1px solid rgba(235, 191, 67, 0.15);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    position: relative;
    scroll-margin-top: 2rem; /* For anchor link positioning */
  }

  .store-content-block::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #EBBF43 0%, #FFBC02 50%, #EBBF43 100%);
    border-radius: 1rem 1rem 0 0;
  }

  /* Content Block Header */
  .store-content-header {
    padding: 2rem 2rem 1rem 2rem;
    background: linear-gradient(135deg, #FFEB3B 0%, #FFC107 100%);
    border-bottom: 1px solid rgb(0, 0, 0);
    position: relative;
  }

  .store-content-heading {
    margin: 0;
    font-size: clamp(1.375rem, 3vw, 1.75rem);
    font-weight: 700;
    color: #220e07;
    line-height: 1.3;
    position: relative;
    padding-left: 1rem;
  }

  .store-content-heading::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.5rem;
    width: 4px;
    height: 60%;
    background: linear-gradient(180deg, #ffffff 0%, #ffeeee 100%);
    border-radius: 2px;
  }

  /* Content Block Content */
  .store-content-block .store-content {
    padding: 1.5rem 2rem 2rem 2rem;
  }

  /* Enhanced Typography for Content Blocks */
  .store-content-block .store-content h3 {
    font-size: clamp(1.125rem, 2.5vw, 1.375rem);
    color: #220e07;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 0.5rem;
  }

  .store-content-block .store-content h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: rgba(235, 191, 67, 0.5);
    border-radius: 1px;
  }

  .store-content-block .store-content h4 {
    font-size: clamp(1rem, 2vw, 1.25rem);
    color: #220e07;
    margin-top: 1.25rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
  }

  .store-content-block .store-content h4::before {
    content: '▸';
    color: #EBBF43;
    font-weight: 700;
    margin-right: 0.5rem;
    font-size: 0.875em;
  }

  .store-content-block .store-content p {
    margin-bottom: 1.25rem;
    line-height: 1.7;
    color: rgba(34, 14, 7, 0.9);
  }

  .store-content-block .store-content ul,
  .store-content-block .store-content ol {
    margin-bottom: 1.5rem;
  }

  .store-content-block .store-content ul > li::before {
    background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 100%);
    box-shadow: 0 2px 4px rgba(235, 191, 67, 0.2);
  }

  .store-content-block .store-content ol > li::before {
    background: linear-gradient(135deg, #EBBF43 0%, #FFBC02 100%);
    color: #000000;
    box-shadow: 0 2px 4px rgba(235, 191, 67, 0.2);
  }

  /* Responsive Design for Content Blocks */
  @media (max-width: 768px) {
    .store-toc-container {
      margin-bottom: 1.5rem;
      border-radius: 0.75rem;
    }

    .store-toc-toggle {
      padding: 1rem 1.25rem;
      font-size: 0.95rem;
    }

    .store-toc-title {
      font-size: 0.95rem;
    }

    .store-toc-count {
      font-size: 0.8rem;
    }

    .store-content-nav-list {
      padding: 0 1.25rem;
    }

    .store-content-nav-link {
      padding: 0.75rem 0.875rem;
      font-size: 0.9rem;
      gap: 0.625rem;
    }

    .nav-section-number {
      width: 20px;
      height: 20px;
      font-size: 0.7rem;
    }

    .nav-section-title {
      line-height: 1.3;
    }

    .store-content-blocks-wrapper {
      gap: 1.5rem;
    }

    .store-content-block {
      border-radius: 0.75rem;
    }

    .store-content-header {
      padding: 1.5rem 1.5rem 0.75rem 1.5rem;
    }

    .store-content-block .store-content {
      padding: 1rem 1.5rem 1.5rem 1.5rem;
    }

    .store-content-heading {
      font-size: 1.25rem;
      padding-left: 0.75rem;
    }

    .store-content-heading::before {
      width: 3px;
      top: 0.375rem;
    }

    .store-content-meta {
      padding: 0.625rem 0.875rem;
      font-size: 0.8rem;
    }

    .reading-time {
      gap: 0.375rem;
    }

    .reading-time-icon {
      width: 14px;
      height: 14px;
    }

    /* Auto-collapse TOC after navigation on mobile */
    .store-toc-container.auto-collapse .store-toc-nav {
      transition: max-height 0.3s ease-out;
    }
  }

  /* Extra small screens */
  @media (max-width: 480px) {
    .store-toc-toggle {
      padding: 0.875rem 1rem;
      font-size: 0.9rem;
      gap: 0.5rem;
    }

    .store-toc-icon {
      width: 18px;
      height: 18px;
    }

    .store-content-nav-link {
      padding: 0.625rem 0.75rem;
      font-size: 0.85rem;
    }

    .nav-section-number {
      width: 18px;
      height: 18px;
      font-size: 0.65rem;
    }

    .store-content-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }

  /* Print Styles for Content Blocks */
  @media print {
    .store-content-navigation {
      display: none;
    }

    .store-content-block {
      break-inside: avoid;
      box-shadow: none;
      border: 1px solid #ddd;
      margin-bottom: 1rem;
    }

    .store-content-block::before {
      display: none;
    }

    .store-content-header {
      background: none;
      border-bottom: 1px solid #ddd;
    }
  }

  /* Enhanced Accessibility and SEO Features */
  .store-content-block:focus-within {
    outline: 2px solid #EBBF43;
    outline-offset: 2px;
  }

  .store-content-nav-link[aria-current="page"] {
    background: rgba(235, 191, 67, 0.2);
    border-color: #EBBF43;
    color: #EBBF43;
    font-weight: 600;
  }

  /* Smooth Scrolling for Anchor Links */
  html {
    scroll-behavior: smooth;
  }

  /* Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    html {
      scroll-behavior: auto;
    }
  }

  /* Focus Management for Content Blocks */
  .store-content-block[tabindex="-1"]:focus {
    outline: 2px solid #EBBF43;
    outline-offset: 4px;
  }

  /* Enhanced focus states for better accessibility */
  .store-toc-toggle:focus-visible {
    outline: 3px solid #EBBF43;
    outline-offset: 2px;
    box-shadow: 0 0 0 6px rgba(235, 191, 67, 0.2);
  }

  .store-content-nav-link:focus-visible {
    outline: 2px solid #EBBF43;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(235, 191, 67, 0.2);
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .store-toc-container {
      border: 2px solid;
      background: Canvas;
      color: CanvasText;
    }

    .store-toc-toggle {
      border: 1px solid;
      background: ButtonFace;
      color: ButtonText;
    }

    .store-content-nav-link {
      border: 1px solid;
      background: Canvas;
      color: LinkText;
    }

    .store-content-nav-link:hover,
    .store-content-nav-link:focus {
      background: Highlight;
      color: HighlightText;
    }
  }

  /* Print styles for table of contents */
  @media print {
    .store-toc-container {
      background: white;
      border: 1px solid #000;
      box-shadow: none;
      page-break-inside: avoid;
    }

    .store-toc-toggle {
      display: none;
    }

    .store-toc-nav {
      max-height: none !important;
      padding: 1rem 0 !important;
    }

    .store-content-nav-list {
      display: block !important;
    }

    .store-content-nav-link {
      color: #000 !important;
      text-decoration: none;
      border: none;
      background: none;
    }

    .store-content-nav-link::after {
      content: " (" attr(href) ")";
      font-size: 0.8em;
      color: #666;
    }
  }

  /* Screen reader announcements */
  #toc-announcements {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
  }



  /* Ensure proper heading hierarchy for SEO */
  .store-content-blocks-container h1 {
    font-size: 0;
    line-height: 0;
    visibility: hidden;
    position: absolute;
  }

  /* Skip link styling */
  .skip-link:focus {
    position: static;
    width: auto;
    height: auto;
    padding: 0.5rem 1rem;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* RTL Support for Store Content Blocks and Table of Contents */
  [dir="rtl"] .store-content-heading {
    padding-left: 0;
    padding-right: 1rem;
  }

  [dir="rtl"] .store-content-heading::before {
    left: auto;
    right: 0;
  }

  [dir="rtl"] .store-content-block .store-content h3::after {
    left: auto;
    right: 0;
  }

  [dir="rtl"] .store-content-block .store-content h4::before {
    margin-right: 0;
    margin-left: 0.5rem;
    content: '◂';
  }

  /* RTL Table of Contents */
  [dir="rtl"] .store-toc-toggle {
    text-align: right;
  }

  [dir="rtl"] .store-content-nav-link {
    flex-direction: row-reverse;
  }

  [dir="rtl"] .store-content-nav-link::before {
    left: auto;
    right: 0;
    border-radius: 0 0.5rem 0.5rem 0;
  }

  [dir="rtl"] .store-content-nav-link:hover,
  [dir="rtl"] .store-content-nav-link:focus {
    transform: translateX(-4px);
  }

  [dir="rtl"] .nav-section-title {
    text-align: right;
  }

  [dir="rtl"] .store-content-meta {
    flex-direction: row-reverse;
  }

  [dir="rtl"] .reading-time {
    flex-direction: row-reverse;
  }

  /* RTL Mobile Adjustments */
  @media (max-width: 768px) {
    [dir="rtl"] .store-content-heading {
      padding-right: 0.75rem;
      padding-left: 0;
    }

    [dir="rtl"] .store-content-heading::before {
      right: 0;
      left: auto;
    }
  }

  /* ==========================================================================
     RESPONSIVE TYPOGRAPHY FOR CONTENT AREAS
     ========================================================================== */

  @media (max-width: 768px) {
    .entry-content h1, .page-content h1, .post-content h1, .content-area h1, .single-content h1, .store-content h1, .coupon-content h1, article .content h1, .wp-block-group h1, .comment-content h1, .content-typography h1 {
      font-size: 2rem;
    }
    .entry-content h2, .page-content h2, .post-content h2, .content-area h2, .single-content h2, .store-content h2, .coupon-content h2, article .content h2, .wp-block-group h2, .comment-content h2, .content-typography h2 {
      font-size: 1.75rem;
    }
    .entry-content h3, .page-content h3, .post-content h3, .content-area h3, .single-content h3, .store-content h3, .coupon-content h3, article .content h3, .wp-block-group h3, .comment-content h3, .content-typography h3 {
      font-size: 1.375rem;
    }
    .entry-content h4, .page-content h4, .post-content h4, .content-area h4, .single-content h4, .store-content h4, .coupon-content h4, article .content h4, .wp-block-group h4, .comment-content h4, .content-typography h4 {
      font-size: 1.125rem;
    }
    .entry-content h5, .page-content h5, .post-content h5, .content-area h5, .single-content h5, .store-content h5, .coupon-content h5, article .content h5, .wp-block-group h5, .comment-content h5, .content-typography h5 {
      font-size: 1rem;
    }
    .entry-content h6, .page-content h6, .post-content h6, .content-area h6, .single-content h6, .store-content h6, .coupon-content h6, article .content h6, .wp-block-group h6, .comment-content h6, .content-typography h6 {
      font-size: 0.875rem;
    }
  }





  /* Code and preformatted text */
  code {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 0.875em;
    background: rgba(235, 191, 67, 0.1);
    color: #996f01;
    padding: 0.125rem 0.375rem;
    border-radius: 0.375rem;
    border: 1px solid rgba(235, 191, 67, 0.2);
    font-weight: 500;
  }

  pre {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1.5rem 0;
    overflow-x: auto;
    line-height: 1.5;
    font-size: 0.875rem;
  }

  pre code {
    background: none;
    border: none;
    padding: 0;
    color: #495057;
    font-size: inherit;
  }

  /* Keyboard input styling */
  kbd {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    background: #212529;
    color: #ffffff;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Abbreviations */
  abbr[title] {
    text-decoration: underline dotted;
    cursor: help;
    border-bottom: none;
  }

  /* Subscript and superscript */
  sub, sup {
    font-size: 0.75em;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }

  sub {
    bottom: -0.25em;
  }

  sup {
    top: -0.5em;
  }















  /* ==========================================================================
     ACCESSIBILITY & PRINT STYLES
     ========================================================================== */

  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    h1 {
      background: none;
      -webkit-text-fill-color: unset;
      color: #000000;
    }

    a {
      text-decoration: underline;
    }

    button,
    input[type="submit"] {
      border: 2px solid #000000;
    }
  }

  /* Print styles */
  @media print {
    * {
      background: transparent !important;
      color: black !important;
      box-shadow: none !important;
      text-shadow: none !important;
    }

    h1, h2, h3, h4, h5, h6 {
      background: none !important;
      -webkit-text-fill-color: unset !important;
      color: black !important;
    }

    a {
      text-decoration: underline;
    }

    a[href^="http"]:after {
      content: " (" attr(href) ")";
    }

    blockquote {
      border: 1px solid #999;
      page-break-inside: avoid;
    }

    thead {
      display: table-header-group;
    }

    tr,
    img {
      page-break-inside: avoid;
    }

    img {
      max-width: 100% !important;
    }

    p,
    h2,
    h3 {
      orphans: 3;
      widows: 3;
    }

    h2,
    h3 {
      page-break-after: avoid;
    }
  }
}

/* Custom component styles */
@layer components {
  /* Button components */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s;
    outline: none;
    border: none;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .btn:focus {
    box-shadow: 0 0 0 2px #f97316, 0 0 0 4px rgba(249, 115, 22, 0.2);
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Primary buttons */
  .btn-primary {
    background-color: #ea580c;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: #c2410c;
    transform: translateY(-1px);
  }

  /* Secondary buttons */
  .btn-secondary {
    background-color: #0d9488;
    color: white;
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: #0f766e;
    transform: translateY(-1px);
  }

  /* Tertiary buttons */
  .btn-tertiary {
    background-color: #6366f1;
    color: white;
  }

  .btn-tertiary:hover:not(:disabled) {
    background-color: #4f46e5;
    transform: translateY(-1px);
  }

  /* Outline buttons */
  .btn-outline {
    border: 2px solid #ea580c;
    color: #ea580c;
    background-color: transparent;
  }

  .btn-outline:hover:not(:disabled) {
    background-color: #ea580c;
    color: white;
    transform: translateY(-1px);
  }

  .btn-outline-secondary {
    border: 2px solid #0d9488;
    color: #0d9488;
    background-color: transparent;
  }

  .btn-outline-secondary:hover:not(:disabled) {
    background-color: #0d9488;
    color: white;
    transform: translateY(-1px);
  }

  /* Ghost buttons */
  .btn-ghost {
    color: #4b5563;
    background-color: transparent;
  }

  .btn-ghost:hover:not(:disabled) {
    background-color: #f3f4f6;
    color: #374151;
  }

  /* Button sizes */
  .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .btn-xl {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  /* Icon buttons */
  .btn-icon {
    padding: 0.5rem;
    width: 2.5rem;
    height: 2.5rem;
  }

  .btn-icon-sm {
    padding: 0.375rem;
    width: 2rem;
    height: 2rem;
  }

  .btn-icon-lg {
    padding: 0.75rem;
    width: 3rem;
    height: 3rem;
  }

  /* Interactive button states */
  .btn-loading {
    position: relative;
    color: transparent;
  }

  .btn-loading::after {
    content: '';
    position: absolute;
    width: 1rem;
    height: 1rem;
    top: 50%;
    left: 50%;
    margin-left: -0.5rem;
    margin-top: -0.5rem;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  /* Like button */
  .like-button {
    position: relative;
    transition: all 0.2s ease;
  }

  .like-button:hover {
    transform: scale(1.1);
  }

  .like-button.active {
    color: #ef4444;
  }

  .like-button.active:hover {
    color: #dc2626;
  }

  /* Share button */
  .share-button {
    position: relative;
    transition: all 0.2s ease;
  }

  .share-button:hover {
    transform: scale(1.05);
  }

  /* Copy button */
  .copy-button {
    position: relative;
    overflow: hidden;
  }

  .copy-button.copied {
    background-color: #10b981;
    color: white;
  }

  .copy-button.copied::before {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.25rem;
  }

  /* Ripple effect */
  .btn-ripple {
    position: relative;
    overflow: hidden;
  }

  .btn-ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .btn-ripple:active::before {
    width: 300px;
    height: 300px;
  }

  /* Pagination components */
  .pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    margin: 2rem 0;
  }

  .pagination-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    height: 2.5rem;
    padding: 0.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s;
    border: 1px solid #e5e7eb;
    background-color: white;
    color: #374151;
  }

  .pagination-item:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
    transform: translateY(-1px);
  }

  .pagination-item.active {
    background-color: #ea580c;
    border-color: #ea580c;
    color: white;
  }

  .pagination-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  .pagination-prev,
  .pagination-next {
    padding: 0.5rem 0.75rem;
  }

  .pagination-dots {
    padding: 0.5rem 0.25rem;
    border: none;
    background: none;
    color: #9ca3af;
  }

  /* Rating stars */
  .rating-stars {
    display: flex;
    align-items: center;
    gap: 0.125rem;
  }

  .rating-star {
    width: 1rem;
    height: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    color: #d1d5db;
  }

  .rating-star.filled {
    color: #fbbf24;
  }

  .rating-star.half-filled {
    background: linear-gradient(90deg, #fbbf24 50%, #d1d5db 50%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .rating-star:hover {
    transform: scale(1.1);
  }

  .rating-interactive .rating-star:hover,
  .rating-interactive .rating-star:hover ~ .rating-star {
    color: #f59e0b;
  }

  .rating-lg .rating-star {
    width: 1.25rem;
    height: 1.25rem;
  }

  .rating-sm .rating-star {
    width: 0.875rem;
    height: 0.875rem;
  }


  /* Store components */
  .store-card {
    padding: 1rem;
  }

  .store-logo {
    width: 4rem;
    height: 4rem;
    border-radius: 0.5rem;
    object-fit: cover;
    border: 1px solid #e5e7eb;
  }

  /* CSS Filter System */
  [c-type] {
    transition: all 0.3s ease;
    opacity: 1;
    transform: scale(1);
  }

  [c-type].filter-hidden {
    opacity: 0;
    transform: scale(0.95);
    pointer-events: none;
  }

  [c-type].filter-visible {
    opacity: 1;
    transform: scale(1);
    pointer-events: auto;
  }

  .filter-btn {
    transition: all 0.2s ease;
  }

  .filter-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }

  .filter-btn.active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  /* Coupon Description Content Styling with RTL/LTR Support */
  .coupon-description-content {
    line-height: 1.6;
  }

  .coupon-description-content p {
    margin-bottom: 0.75rem;
  }

  .coupon-description-content p:last-child {
    margin-bottom: 0;
  }

  .coupon-description-content ul,
  .coupon-description-content ol {
    margin: 0.5rem 0;
  }

  /* LTR list styling */
  .coupon-description-content ul,
  .coupon-description-content ol {
    padding-left: 1.25rem;
  }

  /* RTL list styling */
  [dir="rtl"] .coupon-description-content ul,
  [dir="rtl"] .coupon-description-content ol {
    padding-left: 0;
    padding-right: 1.25rem;
  }

  .coupon-description-content li {
    margin-bottom: 0.25rem;
  }

  .coupon-description-content strong,
  .coupon-description-content b {
    font-weight: 600;
    color: #374151;
  }

  .coupon-description-content em,
  .coupon-description-content i {
    font-style: italic;
  }

  .coupon-description-content a {
    color: #f97316;
    text-decoration: underline;
    text-decoration-color: #fed7aa;
    text-underline-offset: 2px;
    transition: all 0.2s ease;
  }

  .coupon-description-content a:hover {
    color: #ea580c;
    text-decoration-color: #f97316;
  }

  .coupon-description-content h1,
  .coupon-description-content h2,
  .coupon-description-content h3,
  .coupon-description-content h4,
  .coupon-description-content h5,
  .coupon-description-content h6 {
    font-weight: 600;
    color: #111827;
    margin: 0.75rem 0 0.5rem 0;
  }

  .coupon-description-content h1 { font-size: 1.125rem; }
  .coupon-description-content h2 { font-size: 1.1rem; }
  .coupon-description-content h3 { font-size: 1.05rem; }
  .coupon-description-content h4,
  .coupon-description-content h5,
  .coupon-description-content h6 { font-size: 1rem; }

  .coupon-description-content blockquote {
    margin: 0.75rem 0;
    font-style: italic;
    color: #6b7280;
  }

  /* LTR blockquote styling */
  .coupon-description-content blockquote {
    border-left: 3px solid #f97316;
    padding-left: 1rem;
  }

  /* RTL blockquote styling */
  [dir="rtl"] .coupon-description-content blockquote {
    border-left: none;
    border-right: 3px solid #f97316;
    padding-left: 0;
    padding-right: 1rem;
  }

  .coupon-description-content code {
    background: #f3f4f6;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 0.875em;
    color: #374151;
  }

  /* Collapsible Description Toggle - Now handled by coupon-card.css */
  /* Removed conflicting styles to prevent JavaScript conflicts */

  /* Modal System - Clean implementation without conflicts */

  /* Coupon highlight animation for fallback */
  .highlight-coupon {
    animation: highlightPulse 2s ease-in-out;
    border: 2px solid #3b82f6 !important;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3) !important;
  }

  @keyframes highlightPulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }
    50% {
      transform: scale(1.02);
      box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
    }
  }

  /* Form components */
  .form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: border-color 0.2s;
  }

  .form-input:focus {
    border-color: #ea580c;
    box-shadow: 0 0 0 2px rgba(234, 88, 12, 0.2);
  }

  .form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
  }

  /* Navigation components - Moved to modern-glass-header.css */
}

/* Custom utility classes */
@layer utilities {
  /* Font utilities */
  .font-rubik {
    font-family: 'Rubik', system-ui, sans-serif;
  }

  .font-sans {
    font-family: 'Rubik', system-ui, sans-serif;
  }

  .font-mono {
    font-family: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  }

  /* Language-specific font switching - Rubik works well for both LTR and RTL */
  .lang-ar, [lang="ar"], [dir="rtl"] {
    font-family: 'Rubik', system-ui, sans-serif;
  }

  .lang-en, [lang="en"], [dir="ltr"] {
    font-family: 'Rubik', system-ui, sans-serif;
  }

  /* RTL utilities */
  [dir="rtl"] .rtl\:mr-auto {
    margin-right: auto;
  }

  [dir="rtl"] .rtl\:ml-auto {
    margin-left: auto;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  

  /* Custom animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Text utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Screen reader only */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Category animations */
  @keyframes ripple {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
  }

  .category-card.hover-active {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }


  /* 🎯 CTA Colors - Manual Definition for Tailwind - Golden/Yellow scheme */
  .bg-cta-500 {
    background-color: #FFBC02;
  }

  .bg-cta-600 {
    background-color: #e6a902;
  }

  .border-cta-500 {
    border-color: #FFBC02;
  }

  .text-cta-900 {
    color: #996f01;
  }

  .hover\:bg-cta-600:hover {
    background-color: #e6a902;
  }

  .focus\:ring-cta-500:focus {
    --tw-ring-color: #FFBC02;
    box-shadow: 0 0 0 3px rgba(255, 188, 2, 0.5);
  }

  /* Modal styles - Moved to modern-glass-header.css for better organization */

  /* Popup System */
  .popup {
    position: fixed;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
    padding: 1rem;
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    max-width: 300px;
  }

  .popup.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .popup::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
  }

  .popup::after {
    content: '';
    position: absolute;
    top: -9px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-bottom: 9px solid #e5e7eb;
  }

  /* Share popup specific styles */
  .share-popup {
    min-width: 250px;
  }

  .share-popup .share-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
  }

  .share-popup .share-url-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    background: #f9fafb;
  }

  /* Tooltip styles */
  .tooltip {
    position: fixed;
    background: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 10000;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .tooltip.show {
    opacity: 1;
  }

  .tooltip::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 4px solid transparent;
  }

  .tooltip.top::before {
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-top-color: #1f2937;
  }

  .tooltip.bottom::before {
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: #1f2937;
  }

  .tooltip.left::before {
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    border-left-color: #1f2937;
  }

  .tooltip.right::before {
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    border-right-color: #1f2937;
  }


  .c-logo   { grid-area: c-logo; }
  .c-title  { grid-area: c-title; }
  .c-action { grid-area: c-action; }
  

 
}



.coupon-categories-carousel .splide__track {
	overflow: visible;
}



.coupon-code-input {
text-align: left!important;
}


@media screen and (max-width: 768px) {
   .c-content {
  display: grid; 
  grid-template-columns: 1fr 1fr 1fr; 
  grid-template-rows: 1fr 1fr; 
  gap: 0px 0px; 
  grid-template-areas: 
    "c-logo c-title c-title"
    "c-action c-action c-action"; 
}
	
}