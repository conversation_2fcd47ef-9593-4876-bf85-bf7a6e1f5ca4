/**
 * Store Social Profiles & Page Links Styling
 * Modern design with glass morphism effects and brand colors
 * Supports RTL/LTR languages and accessibility
 */

/* ==========================================================================
   Store Social Profiles
   ========================================================================== */

.store-social-profiles {
    margin-bottom: 2rem;
}

.store-social-title {
    font-weight: 600;
    color: #220e07;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}



.store-social-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.store-social-item {
    margin: 0;
}

.store-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #220e07;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.store-social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #EBBF43, #FFBC02);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.store-social-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(235, 191, 67, 0.3);
    border-color: rgba(235, 191, 67, 0.4);
}

.store-social-link:hover::before {
    opacity: 0.1;
}

.store-social-link:hover .store-social-icon {
    color: #EBBF43;
    transform: scale(1.1);
}

.store-social-link:hover .store-social-icon svg {
    transform: scale(1.1);
}

.store-social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.store-social-icon svg {
    width: 20px;
    height: 20px;
    transition: all 0.3s ease;
}

/* Social Platform Specific Colors */
.social-facebook .store-social-link:hover .store-social-icon {
    color: #1877f2;
}

.social-twitter .store-social-link:hover .store-social-icon {
    color: #000000;
}

.social-instagram .store-social-link:hover .store-social-icon {
    color: #E4405F;
}

.social-pinterest .store-social-link:hover .store-social-icon {
    color: #BD081C;
}

/* Social Profiles with Labels */
.store-social-profiles.show-labels .store-social-link {
    width: auto;
    padding: 0.75rem 1rem;
    gap: 0.5rem;
}

.store-social-label {
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
}

/* Size Variations */
.social-size-small .store-social-link {
    width: 40px;
    height: 40px;
}

.social-size-small .store-social-icon svg {
    width: 16px;
    height: 16px;
}

.social-size-large .store-social-link {
    width: 56px;
    height: 56px;
}

.social-size-large .store-social-icon svg {
    width: 24px;
    height: 24px;
}

/* Style Variations */
.social-style-solid .store-social-link {
    background: linear-gradient(135deg, #EBBF43, #FFBC02);
    color: #000000;
    border: none;
}

.social-style-solid .store-social-link:hover {
    background: linear-gradient(135deg, #FFBC02, #EBBF43);
}

.social-style-outline .store-social-link {
    background: transparent;
    border: 2px solid #EBBF43;
    color: #EBBF43;
}

.social-style-outline .store-social-link:hover {
    background: #EBBF43;
    color: #000000;
}

/* ==========================================================================
   Store Page Links
   ========================================================================== */

.store-page-links {
    margin-bottom: 2rem;
}

.store-links-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #220e07;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.store-links-title::before {
    content: '';
    width: 4px;
    height: 1.25rem;
    background: linear-gradient(135deg, #EBBF43, #FFBC02);
    border-radius: 2px;
}

.store-links-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.store-link-item {
    margin: 0;
}

.store-page-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #220e07;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.store-page-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #EBBF43, #FFBC02);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.store-page-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(235, 191, 67, 0.2);
    border-color: rgba(235, 191, 67, 0.4);
}

.store-page-link:hover::before {
    opacity: 0.05;
}

.store-link-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.store-link-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #EBBF43, #FFBC02);
    border-radius: 10px;
    color: #000000;
    transition: all 0.3s ease;
}

.store-link-icon svg {
    width: 20px;
    height: 20px;
    transition: all 0.3s ease;
}

.store-page-link:hover .store-link-icon {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(235, 191, 67, 0.3);
}

.store-page-link:hover .store-link-icon svg {
    transform: scale(1.05);
}

.store-link-text {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.store-link-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: #220e07;
}

.store-link-description {
    font-size: 0.75rem;
    color: #64748b;
    line-height: 1.4;
}

.store-link-external {
    color: #64748b;
    opacity: 0.7;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.store-link-external svg {
    width: 16px;
    height: 16px;
    transition: all 0.3s ease;
}

.store-page-link:hover .store-link-external {
    opacity: 1;
    color: #EBBF43;
    transform: translateX(2px);
}

/* Link Type Specific Icons */
.link-contact .store-link-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.link-delivery .store-link-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.link-returns .store-link-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.link-faq .store-link-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

/* Style Variations */
.links-style-minimal .store-page-link {
    background: transparent;
    border: 1px solid #e2e8f0;
    padding: 0.75rem;
}

.links-style-minimal .store-link-icon {
    background: #f1f5f9;
    color: #64748b;
}

.links-style-card .store-page-link {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.links-style-card .store-page-link:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .store-social-list {
        gap: 0.5rem;
    }

    .store-social-link {
        width: 44px;
        height: 44px;
    }

    .store-social-icon svg {
        width: 18px;
        height: 18px;
    }

    .store-page-link {
        padding: 0.875rem;
    }

    .store-link-icon {
        width: 36px;
        height: 36px;
    }

    .store-link-icon svg {
        width: 18px;
        height: 18px;
    }

    .store-link-name {
        font-size: 0.8125rem;
    }

    .store-link-description {
        font-size: 0.6875rem;
    }
}

@media (max-width: 480px) {
    .store-social-profiles,
    .store-page-links {
        margin-bottom: 1.5rem;
    }
    
    .store-social-title,
    .store-links-title {
        font-size: 1rem;
        margin-bottom: 0.75rem;
    }
    
    .store-links-list {
        gap: 0.5rem;
    }
}

/* ==========================================================================
   RTL (Right-to-Left) Support
   ========================================================================== */


[dir="rtl"] .store-links-title::before,
.rtl .store-links-title::before {
    order: 2;
}



[dir="rtl"] .store-link-text,
.rtl .store-link-text {
    text-align: right;
}

[dir="rtl"] .store-page-link:hover .store-link-external,
.rtl .store-page-link:hover .store-link-external {
    transform: translateX(-2px);
}

/* RTL Typography */
[dir="rtl"] .store-social-profiles,
[dir="rtl"] .store-page-links,
.rtl .store-social-profiles,
.rtl .store-page-links {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
}

[dir="rtl"] .store-link-name,
[dir="rtl"] .store-link-description,
.rtl .store-link-name,
.rtl .store-link-description {
    line-height: 1.6;
}

/* RTL Accessibility */
[dir="rtl"] .store-social-link:focus,
[dir="rtl"] .store-page-link:focus,
.rtl .store-social-link:focus,
.rtl .store-page-link:focus {
    outline: 2px solid #EBBF43;
    outline-offset: 2px;
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .store-social-profiles,
    .store-page-links {
        break-inside: avoid;
        margin-bottom: 1rem;
    }

    .store-social-link,
    .store-page-link {
        background: transparent !important;
        border: 1px solid #ccc !important;
        box-shadow: none !important;
        transform: none !important;
    }

    .store-social-link::before,
    .store-page-link::before {
        display: none !important;
    }

    .store-link-external {
        display: none !important;
    }

    .store-page-link::after {
        content: " (" attr(href) ")";
        font-size: 0.75rem;
        color: #666;
    }
}

/* ==========================================================================
   High Contrast Mode Support
   ========================================================================== */

@media (prefers-contrast: high) {
    .store-social-link,
    .store-page-link {
        border: 2px solid #000000;
        background: #ffffff;
        color: #000000;
    }

    .store-link-icon {
        background: #000000;
        color: #ffffff;
    }

    .store-social-link:hover,
    .store-page-link:hover {
        background: #000000;
        color: #ffffff;
    }
}

/* ==========================================================================
   Reduced Motion Support
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    .store-social-link,
    .store-page-link,
    .store-social-icon,
    .store-link-icon,
    .store-link-external {
        transition: none;
    }

    .store-social-link:hover,
    .store-page-link:hover {
        transform: none;
    }

    .store-social-link:hover .store-social-icon,
    .store-page-link:hover .store-link-icon {
        transform: none;
    }

    .animate-float,
    .animate-float-slow,
    .animate-float-fast {
        animation: none;
    }
}

/* ==========================================================================
   Floating Animations
   ========================================================================== */

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.8;
    }
}

@keyframes floatSlow {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.4;
    }
    50% {
        transform: translateY(-15px) rotate(90deg);
        opacity: 0.7;
    }
}

@keyframes floatFast {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.5;
    }
    50% {
        transform: translateY(-10px) rotate(270deg);
        opacity: 0.8;
    }
}

/* Apply Float Animations */
.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-float-slow {
    animation: floatSlow 8s ease-in-out infinite;
}

.animate-float-fast {
    animation: floatFast 4s ease-in-out infinite;
}
