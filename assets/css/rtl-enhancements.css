/*
 * RTL Enhancements for Modern Page Templates
 * Comprehensive RTL support for all enhanced pages
 * Updated for Golden/Yellow Color Scheme
 *
 * @package HalaCoupon
 * @version 3.0
 */

/* ==========================================================================
   RTL Base Styles
   ========================================================================== */

[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] body {
    direction: rtl;
    text-align: right;
}

/* ==========================================================================
   Search Results Page RTL
   ========================================================================== */

/* Search Form RTL */
[dir="rtl"] .search-form input[type="search"] {
    padding-left: 3.5rem;
    padding-right: 1.5rem;
    text-align: right;
}

[dir="rtl"] .search-form button[type="submit"] {
    left: 0.5rem;
    right: auto;
}

/* Search Results Grid RTL */
[dir="rtl"] .search-results-grid {
    direction: rtl;
}

[dir="rtl"] .search-results-grid .glass-card {
    text-align: right;
}

/* Badges and Overlays RTL */
[dir="rtl"] .absolute.top-3.left-3 {
    left: auto;
    right: 0.75rem;
}

[dir="rtl"] .absolute.top-3.right-3 {
    right: auto;
    left: 0.75rem;
}

/* ==========================================================================
   Single Post Template RTL
   ========================================================================== */

/* Hero Section RTL */
[dir="rtl"] .single-hero {
    text-align: right;
}

/* Breadcrumb RTL */
[dir="rtl"] .breadcrumb ol {
    direction: rtl;
}

[dir="rtl"] .breadcrumb-separator {
    transform: scaleX(-1);
}

/* Social Sharing RTL */
[dir="rtl"] .social-sharing {
    direction: rtl;
}

[dir="rtl"] .social-sharing .flex {
    flex-direction: row-reverse;
}

/* Progress Bar RTL */
[dir="rtl"] .reading-progress {
    direction: ltr; /* Keep progress bar LTR for consistency */
}

/* Table of Contents RTL */
[dir="rtl"] .table-of-contents {
    text-align: right;
}

[dir="rtl"] .table-of-contents .ml-4 {
    margin-left: 0;
    margin-right: 1rem;
}

[dir="rtl"] .table-of-contents .ml-8 {
    margin-left: 0;
    margin-right: 2rem;
}

/* Author Bio RTL */
[dir="rtl"] .author-bio .flex {
    direction: rtl;
}

/* Post Navigation RTL */
[dir="rtl"] .post-navigation .flex {
    direction: rtl;
}

[dir="rtl"] .post-navigation .text-right {
    text-align: left;
}

/* ==========================================================================
   Page Template RTL
   ========================================================================== */

/* Page Hero RTL */
[dir="rtl"] .page-hero {
    text-align: right;
}

/* Page Content RTL */
[dir="rtl"] .page-content {
    direction: rtl;
    text-align: right;
}

/* Related Pages RTL */
[dir="rtl"] .related-pages .grid {
    direction: rtl;
}

/* ==========================================================================
   Blog Loop RTL
   ========================================================================== */

/* Blog Grid RTL */
[dir="rtl"] .blog-grid {
    direction: rtl;
}

/* Blog Card RTL */
[dir="rtl"] .blog-card {
    text-align: right;
}

/* Blog Card Meta RTL */
[dir="rtl"] .blog-card .flex {
    direction: rtl;
}

/* Blog Card Footer RTL */
[dir="rtl"] .blog-card .justify-between {
    flex-direction: row-reverse;
}

/* ==========================================================================
   Comments RTL
   ========================================================================== */

/* Comment Container RTL */
[dir="rtl"] .comment {
    direction: rtl;
    text-align: right;
}

/* Comment Avatar RTL */
[dir="rtl"] .comment .flex {
    direction: rtl;
}

/* Comment Actions RTL */
[dir="rtl"] .comment-actions {
    direction: rtl;
}

/* ==========================================================================
   Icons and Arrows RTL
   ========================================================================== */

/* Arrow Icons RTL */
[dir="rtl"] .arrow-right {
    transform: scaleX(-1);
}

[dir="rtl"] .arrow-left {
    transform: scaleX(-1);
}

/* Chevron Icons RTL */
[dir="rtl"] .chevron-right {
    transform: rotate(180deg);
}

[dir="rtl"] .chevron-left {
    transform: rotate(180deg);
}

/* ==========================================================================
   Form Elements RTL
   ========================================================================== */

/* Input Fields RTL */
[dir="rtl"] input[type="text"],
[dir="rtl"] input[type="email"],
[dir="rtl"] input[type="search"],
[dir="rtl"] textarea,
[dir="rtl"] select {
    text-align: right;
    direction: rtl;
}

/* Placeholder RTL */
[dir="rtl"] input::placeholder,
[dir="rtl"] textarea::placeholder {
    text-align: right;
}

/* Select Dropdown RTL */
[dir="rtl"] select {
    background-position: left 0.75rem center;
    padding-left: 2.5rem;
    padding-right: 0.75rem;
}

/* ==========================================================================
   Animations RTL
   ========================================================================== */

/* Hover Animations RTL */
[dir="rtl"] .group-hover\\/cta\\:translate-x-1 {
    transform: translateX(-0.25rem);
}

[dir="rtl"] .hover\\:translate-x-1:hover {
    transform: translateX(-0.25rem);
}

/* Scale Animations (keep same for RTL) */
[dir="rtl"] .hover\\:scale-105:hover,
[dir="rtl"] .group:hover .group-hover\\:scale-110 {
    /* Keep scale animations the same */
}




/* Padding RTL */
[dir="rtl"] .OfferPeel {
    background: linear-gradient(228deg, #b88800, #FFC107 49%, #2b1f0d00 50%, #9f505000) no-repeat 0 4px;
    border-radius: 0 0 5px 5px;
    transform: rotate(70deg) translateY(2px) scaleX(.9);
    transition: transform .2s, background-position .2s;
    z-index: 1;
}
/* ==========================================================================
   Responsive RTL
   ========================================================================== */

/* Mobile RTL */
@media (max-width: 767px) {
    [dir="rtl"] .mobile-text-right {
        text-align: right;
    }
    
    [dir="rtl"] .mobile-flex-row-reverse {
        flex-direction: row-reverse;
    }
}

/* Tablet RTL */
@media (min-width: 768px) and (max-width: 1023px) {
    [dir="rtl"] .tablet-text-right {
        text-align: right;
    }
}

/* Desktop RTL */
@media (min-width: 1024px) {
    [dir="rtl"] .desktop-text-right {
        text-align: right;
    }
}

/* ==========================================================================
   Print RTL
   ========================================================================== */

@media print {
    [dir="rtl"] * {
        direction: rtl;
        text-align: right;
    }

    [dir="rtl"] .breadcrumb-separator {
        transform: scaleX(-1);
    }

    /* Print-friendly golden colors */
    [dir="rtl"] .bg-primary {
        background-color: #EBBF43 !important;
        color: #000000 !important;
    }

    [dir="rtl"] .bg-secondary {
        background-color: #FFBC02 !important;
        color: #000000 !important;
    }

    [dir="rtl"] .bg-accent {
        background-color: #000000 !important;
        color: #ffffff !important;
    }
}
