/**
 * Splide.js Custom Styles - Modern Design
 * 
 * Creative, modern styling for Splide sliders using brand colors
 * Features: Glass morphism, gradients, shadows, animations
 * 
 * @package HalaCoupon
 * @version 1.0.0
 */

/* ==========================================================================
   Modern Slider Container
   ========================================================================== */

.splide {
  position: relative;
  overflow: hidden;
}

.splide__track {
  position: relative;
  overflow: hidden;
  border-radius: 1.5rem;
}

.splide__list {
  display: flex;
  margin: 0;
  padding: 0;
  list-style: none;
}

.splide__slide {
  flex-shrink: 0;
  position: relative;
}

/* ==========================================================================
   Modern Arrow Styling
   ========================================================================== */

.splide__arrows--modern {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  width: 100%;
  pointer-events: none;
}

.splide__arrow--modern {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 3.5rem;
  height: 3.5rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border: 2px solid rgba(235, 191, 67, 0.3);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: auto;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(235, 191, 67, 0.15);
  color: #EBBF43;
  opacity: 0.9;
}

.splide__arrow--modern:hover {
  background: rgba(235, 191, 67, 0.95);
  color: white;
  border-color: rgba(235, 191, 67, 0.6);
  transform: translateY(-50%) scale(1.08);
  box-shadow: 0 12px 40px rgba(235, 191, 67, 0.3), 0 4px 16px rgba(0, 0, 0, 0.15);
  opacity: 1;
}

.splide__arrow--modern:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(235, 191, 67, 0.4), 0 12px 40px rgba(235, 191, 67, 0.3);
}

.splide__arrow--modern:active {
  transform: translateY(-50%) scale(0.95);
  transition-duration: 0.1s;
}

.splide__arrow--modern-prev {
  left: 1.25rem;
}

.splide__arrow--modern-next {
  right: 1.25rem;
}

.splide__arrow--modern svg {
  width: 1.375rem;
  height: 1.375rem;
  fill: none;
  stroke: currentColor;
  stroke-width: 2.5;
  stroke-linecap: round;
  stroke-linejoin: round;
  transition: all 0.3s ease;
}

.splide__arrow--modern:hover svg {
  transform: scale(1.1);
}

.splide__arrow--modern:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.6);
  color: rgba(235, 191, 67, 0.5);
}

/* Hide arrows on mobile */
@media (max-width: 768px) {
  .splide__arrows--modern {
    display: none;
  }
}

/* ==========================================================================
   Modern Pagination Styling
   ========================================================================== */

.splide__pagination--modern {
  display: flex;
  gap: 0.5rem;
  z-index: 10;
  padding: 0.75rem 1.5rem;
}

.splide__pagination__page--modern {
  width: 0.75rem;
  height: 0.75rem;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.splide__pagination__page--modern:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}

.splide__pagination__page--modern.is-active {
  background: linear-gradient(135deg, #d56030 0%, #f18a56 100%);
  transform: scale(1.3);
  box-shadow: 0 2px 10px rgba(213, 96, 48, 0.4);
}

.splide__pagination__page--modern:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(213, 96, 48, 0.3);
}

/* ==========================================================================
   Home Slider Specific Styles
   ========================================================================== */

.home-slider .splide__slide {
  border-radius: 1rem;
  overflow: hidden;
}

.home-slider .splide__track {
  border-radius: 1.5rem;
}

/* ==========================================================================
   Category Slider Styles
   ========================================================================== */

.category-slider {
  padding: 1rem 0;
}

.category-slider .splide__slide {
  padding: 0 0.265rem;
}

.category-slider .splide__arrow--modern {
  width: 2.5rem;
  height: 2.5rem;
}

.category-slider .splide__arrow--modern svg {
  width: 1rem;
  height: 1rem;
}

/* ==========================================================================
   Store Slider Styles
   ========================================================================== */

.store-slider {
  padding: 1rem 0;
}

.store-slider .splide__slide {
  padding: 16px 0.75rem;
}

.store-slider .splide__arrow--modern {
  background: rgba(162, 233, 147, 0.9);
  border-color: rgba(162, 233, 147, 0.3);
}

.store-slider .splide__arrow--modern:hover {
  background: rgba(102, 224, 147, 0.9);
  border-color: rgba(102, 224, 147, 0.5);
  box-shadow: 0 8px 30px rgba(102, 224, 147, 0.3);
}

.store-slider .splide__arrow--modern svg {
  stroke: #47a535;
}

.store-slider .splide__arrow--modern:hover svg {
  stroke: white;
}

/* ==========================================================================
   Offer Slider Styles
   ========================================================================== */

.offer-slider {
  border-radius: 1rem;
  overflow: hidden;
}

.offer-slider .splide__pagination--modern {
  display: none;
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.splide.is-loading {
  opacity: 0.7;
}

.splide.is-loading .splide__track {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* ==========================================================================
   Accessibility Improvements
   ========================================================================== */

.splide__sr {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Focus indicators */
.splide__slide:focus {
  outline: 2px solid #d56030;
  outline-offset: 2px;
}

/* ==========================================================================
   RTL Support
   ========================================================================== */

[dir="rtl"] .splide__arrow--modern-prev {
  right: 1.25rem;
  left: auto;
}

[dir="rtl"] .splide__arrow--modern-next {
  left: 1.25rem;
  right: auto;
}


[dir="rtl"] .home-slider .splide__arrows--rtl .splide__arrow--next svg {
	transform: scaleX(1);

}



/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 640px) {
  .splide__pagination--modern {
    bottom: 0.5rem;
    padding: 0.5rem 1rem;
  }
  
  .splide__pagination__page--modern {
    width: 0.625rem;
    height: 0.625rem;
  }
}

/* ==========================================================================
   Animation Enhancements
   ========================================================================== */

.splide__slide {
  transition: transform 0.3s ease;
}

.splide__slide.is-active {
  transform: scale(1);
}

.splide__slide.is-prev,
.splide__slide.is-next {
  transform: scale(0.95);
  opacity: 0.8;
}

/* Smooth transitions */
.splide__track {
  transition: height 0.3s ease;
}

/* ==========================================================================
   Modern Glass Effects
   ========================================================================== */

.splide-glass-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  pointer-events: none;
}

/* ==========================================================================
   Performance Optimizations
   ========================================================================== */

.splide__slide {
  will-change: transform;
}

.splide__arrow--modern {
  will-change: transform, background-color;
}

.splide__pagination__page--modern {
  will-change: transform, background-color;
}


