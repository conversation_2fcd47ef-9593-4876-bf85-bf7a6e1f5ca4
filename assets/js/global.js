/**
 * Global JavaScript for HalaCoupon Theme
 * 
 * This file provides global utilities and ensures ST object is available
 * for other scripts that depend on it.
 * 
 * @package HalaCoupon
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Ensure ST object exists with fallback values
    if (typeof ST === 'undefined') {
        window.ST = {
            ajax_url: '/wp-admin/admin-ajax.php',
            _wpnonce: '',
            _coupon_nonce: '',
            loading_message: 'Loading...',
            error_message: 'Error occurred',
            search_error: 'Search error occurred',
            try_again: 'Please try again',
            retry_search: 'Try Again'
        };
        console.warn('⚠️ ST object was not properly localized, using fallback values');
    }

    // Global utility functions
    window.halacouponUtils = {

        /**
         * Get the current store URL path from localized data
         */
        getStoreUrlPath: function() {
            return (typeof ST !== 'undefined' && ST.store_url_path) ? ST.store_url_path : '/discount-codes/';
        },

        /**
         * Check if current page is a store page
         */
        isStorePage: function() {
            const currentUrl = window.location.href;
            const storeUrlPath = this.getStoreUrlPath();
            return currentUrl.includes(storeUrlPath) || document.body.classList.contains('taxonomy-coupon_store');
        },

        /**
         * Copy text to clipboard with fallback
         */
        copyToClipboard: function(text, successCallback, errorCallback) {
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(function() {
                    if (typeof successCallback === 'function') {
                        successCallback();
                    }
                }).catch(function(err) {
                    console.error('Clipboard API failed:', err);
                    if (typeof errorCallback === 'function') {
                        errorCallback();
                    }
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                try {
                    const successful = document.execCommand('copy');
                    if (successful && typeof successCallback === 'function') {
                        successCallback();
                    } else if (!successful && typeof errorCallback === 'function') {
                        errorCallback();
                    }
                } catch (err) {
                    console.error('Fallback copy failed:', err);
                    if (typeof errorCallback === 'function') {
                        errorCallback();
                    }
                }
                
                document.body.removeChild(textArea);
            }
        },

        /**
         * Show notification toast
         */
        showNotification: function(message, type = 'info', duration = 3000) {
            const notification = document.createElement('div');
            notification.className = `halacoupon-notification halacoupon-notification--${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 99999;
                padding: 12px 24px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transform: translateX(100%);
                transition: transform 0.3s ease-out;
                max-width: 300px;
                word-wrap: break-word;
            `;
            
            // Set colors based on type
            switch(type) {
                case 'success':
                    notification.style.background = '#10b981';
                    notification.style.color = 'white';
                    break;
                case 'error':
                    notification.style.background = '#ef4444';
                    notification.style.color = 'white';
                    break;
                case 'warning':
                    notification.style.background = '#f59e0b';
                    notification.style.color = 'white';
                    break;
                default:
                    notification.style.background = '#3b82f6';
                    notification.style.color = 'white';
            }
            
            notification.textContent = message;
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);
            
            // Remove after duration
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, duration);
        },

        /**
         * Debounce function
         */
        debounce: function(func, wait, immediate) {
            let timeout;
            return function executedFunction() {
                const context = this;
                const args = arguments;
                const later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        },

        /**
         * Check if element is in viewport
         */
        isInViewport: function(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }
    };

    // Global copy text function for backward compatibility
    window.copyTextToClipboard = function(text, successCallback, errorCallback) {
        return window.halacouponUtils.copyToClipboard(text, successCallback, errorCallback);
    };

    // Initialize global functionality
    $(document).ready(function() {
        console.log('🌍 HalaCoupon Global JS initialized');
        
        // Add global error handler for AJAX requests
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            if (xhr.status !== 0 && xhr.statusText !== 'abort') {
                console.error('AJAX Error:', {
                    url: settings.url,
                    status: xhr.status,
                    error: thrownError,
                    response: xhr.responseText
                });
            }
        });
    });



	var is_support_copy_command = function(){
    if ( typeof document.queryCommandSupported !== "undefined" ) {
        return document.queryCommandSupported("copy");
    }
    return false;
};
	



    var  $document = $( document );

	var html = $('html');

    if ( ! is_support_copy_command() ){
        $("body").addClass( 'no-copy-cmd' );
    }


    
    function halacoupon_get_all_getvar( url ){
        var vars = {};
        var parts = url.replace(/[?&]+([^=&]+)=([^&]*)/gi, function(m,key,value) {
            vars[key] = value;
        });
        return vars;
    }

    function halacoupon_remove_param(key, sourceURL) {
        var rtn = sourceURL.split("?")[0],
            param,
            params_arr = [],
            queryString = (sourceURL.indexOf("?") !== -1) ? sourceURL.split("?")[1] : "";
        if (queryString !== "") {
            params_arr = queryString.split("&");
            for (var i = params_arr.length - 1; i >= 0; i -= 1) {
                param = params_arr[i].split("=")[0];
                if (param === key) {
                    params_arr.splice(i, 1);
                }
            }

            rtn = rtn + "?" + params_arr.join("&");

            var check_url = rtn.split("?");
            if( ! check_url[1] || ''  == check_url[1] ){
                rtn = check_url[0];
            }
        }
        return rtn;
    }





    $document.on( 'ajax_filter_loaded', function ( e, respond ) {
        var wrapper = $( '.st-coupons');
        wrapper.html( respond.data );
        listingCouponItem( wrapper );
    } );





// Set a cookie with path for better accessibility
function setCookie(cname, cvalue, exdays) {
    var d = new Date();
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
    var expires = "expires=" + d.toUTCString();
    document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/";
}

// Delete a cookie
function deleteCookie(cname) {
    document.cookie = cname + "=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
}

// Get a cookie value
function getCookie(cname) {
    var name = cname + "=";
    var decodedCookie = decodeURIComponent(document.cookie);
    var cookies = decodedCookie.split(';');
    for (var i = 0; i < cookies.length; i++) {
        var c = cookies[i].trim();
        if (c.indexOf(name) === 0) {
            return c.substring(name.length, c.length);
        }
    }
    return "";
}


})(jQuery);
