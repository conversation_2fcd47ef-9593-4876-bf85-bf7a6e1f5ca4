jQuery(document).ready(function ($) {
    // Handle the media uploader for menu icons
    $('.upload-icon-button').on('click', function (e) {
        e.preventDefault();

        var button = $(this);
        var itemId = button.data('item-id');
        var inputField = $('#edit-menu-item-icon-' + itemId);
        var previewContainer = $(this).closest('p').siblings('.menu-icon-preview'); // Preview container

        // Create a media uploader frame
        var mediaUploader = wp.media({
            title: 'Choose Icon',
            button: {
                text: 'Use This Icon'
            },
            multiple: false // Only upload a single file
        });

        // When an image is selected, run a callback
        mediaUploader.on('select', function () {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            inputField.val(attachment.url); // Set the image URL in the input field

            // Display the selected image or SVG
            var fileType = attachment.url.split('.').pop().toLowerCase();
            if (fileType === 'svg') {
                $.get(attachment.url, function (data) {
                    var svg = new XMLSerializer().serializeToString(data.documentElement);
                    previewContainer.html('<div class="svg-icon">' + svg + '</div>'); // Show inline SVG preview
                });
            } else {
                previewContainer.html('<img src="' + attachment.url + '" style="max-width: 50px; height: 50px;" />'); // Show image preview
            }
        });

        // Open the media uploader
        mediaUploader.open();
    });
});