/**
 * 🎯 CSS-BASED COUPON FILTERS
 * Pure CSS filtering using c-type attribute - No AJAX required
 */

jQuery(document).ready(function($) {

    // Initialize filter system
    initializeCSSFilters();

    function initializeCSSFilters() {
        // Count coupons by type and update counters
        updateCouponCounts();

        // Set initial button states (All Deals active by default)
        setInitialButtonStates();

        // Add click handlers to filter buttons
        $('.filter-btn').on('click', function(e) {
            e.preventDefault();

            const $button = $(this);
            const filterType = $button.data('filter');


            // Update active button state with new styling
            updateFilterButtonStates($button, filterType);

            // Apply CSS filter
            applyCSSFilter(filterType);
        });

    }

    function setInitialButtonStates() {
        // Set the "All Deals" button as active by default
        const $allButton = $('.filter-btn[data-filter="all"]');
        if ($allButton.length) {
            updateFilterButtonStates($allButton, 'all');
        }
    }

    function updateFilterButtonStates($activeButton, filterType) {
        // Reset all buttons to their default states
        $('.filter-btn').each(function() {
            const $btn = $(this);
            const btnType = $btn.data('filter');

            // Remove active class from all buttons
            $btn.removeClass('active');

            // Reset each button to its default styling based on type
            if (btnType === 'all') {
                // All Deals button - default: white text with border
                $btn.removeClass('bg-primary bg-gradient-cta')
                    .addClass('text-white border');
            } else if (btnType === 'code') {
                // Coupons button - default: white background with primary border
                $btn.removeClass('bg-primary text-white')
                    .addClass('bg-white border-primary-200 text-primary');
                // Update count badge to primary style
                $btn.find('#count-code').removeClass('bg-white/20 text-white')
                    .addClass('text-primary');
            } else if (btnType === 'sale') {
                // Offers button - default: white background with secondary border
                $btn.removeClass('bg-secondary text-white')
                    .addClass('bg-white border-secondary-200 text-secondary-700');
                // Update count badge to secondary style
                $btn.find('#count-sale').removeClass('bg-white/20 text-white')
                    .addClass('bg-secondary-100 text-secondary-700');
            }
        });

        // Apply active styling to the clicked button
        $activeButton.addClass('active');

        if (filterType === 'all') {
            // All Deals active: gradient background
            $activeButton.removeClass('text-white border')
                .addClass('bg-primary text-white');
            // Update count badge to white style
            $activeButton.find('#count-all').removeClass('bg-white/20')
                .addClass('bg-white/20 text-white');
        } else if (filterType === 'code') {
            // Coupons active: primary gradient background
            $activeButton.removeClass('bg-white border-primary-200 text-primary')
                .addClass('bg-primary text-white');
            // Update count badge to white style
            $activeButton.find('#count-code').removeClass('text-primary')
                .addClass('bg-white/20 text-white');
        } else if (filterType === 'sale') {
            // Offers active: secondary gradient background
            $activeButton.removeClass('bg-white border-secondary-200 text-secondary-700')
                .addClass('bg-secondary text-white');
            // Update count badge to white style
            $activeButton.find('#count-sale').removeClass('bg-secondary-100 text-secondary-700')
                .addClass('bg-white/20 text-white');
        }

    }

    function updateCouponCounts() {
        const $coupons = $('[c-type]');
        let counts = {
            all: 0,
            code: 0,
            sale: 0
        };

        $coupons.each(function() {
            const type = $(this).attr('c-type');
            counts.all++;
            
            if (type === 'code') {
                counts.code++;
            } else if (type === 'sale') {
                counts.sale++;
            }
        });

        // Update counter displays (including hover states)
        $('#count-all').text(counts.all);
        $('#count-code').text(counts.code);
        $('#count-code-hover').text(counts.code);
        $('#count-sale').text(counts.sale);
        $('#count-sale-hover').text(counts.sale);

    }

    function applyCSSFilter(filterType) {
        const $coupons = $('[c-type]');
        
        if (filterType === 'all') {
            // Show all coupons
            $coupons.show().removeClass('filter-hidden');
        } else {
            // Hide all first
            $coupons.hide().addClass('filter-hidden');
            
            // Show only matching type
            $coupons.filter(`[c-type="${filterType}"]`).show().removeClass('filter-hidden');
            
        }

        // Add smooth animation
        $coupons.each(function(index) {
            const $coupon = $(this);
            if (!$coupon.hasClass('filter-hidden')) {
                setTimeout(() => {
                    $coupon.addClass('filter-visible');
                }, index * 50);
            } else {
                $coupon.removeClass('filter-visible');
            }
        });

        // Update URL hash for bookmarking
        if (filterType !== 'all') {
            window.history.replaceState(null, null, `#filter-${filterType}`);
        } else {
            window.history.replaceState(null, null, window.location.pathname);
        }
    }

    // Check URL hash on page load for filter state
    function checkInitialFilter() {
        const hash = window.location.hash;
        if (hash.startsWith('#filter-')) {
            const filterType = hash.replace('#filter-', '');
            const $button = $(`.filter-btn[data-filter="${filterType}"]`);
            
            if ($button.length) {
                $button.trigger('click');
            }
        }
    }

    // Apply initial filter after a short delay to ensure DOM is ready
    setTimeout(checkInitialFilter, 500);


});
