/**
 * Clean Coupon Card JavaScript
 * Minimal, focused functionality
 */

document.addEventListener('DOMContentLoaded', function() {

    // Description toggle - Fixed to avoid conflicts
    document.querySelectorAll('.description-toggle').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Handle both data-coupon-id and dataset.couponId
            const couponId = this.dataset.couponId || this.getAttribute('data-coupon-id');
            const content = document.getElementById(`description-${couponId}`);
            const iconDown = this.querySelector('.description-icon-down');
            const iconUp = this.querySelector('.description-icon-up');
            const isExpanded = this.getAttribute('aria-expanded') === 'true';

            console.log('🎯 Main.js: Toggling description for coupon', couponId, 'Currently expanded:', isExpanded);

            if (!content) {
                console.warn('🚨 Main.js: Description content not found for coupon', couponId);
                return;
            }

            if (isExpanded) {
                // Collapse
                content.classList.add('hidden');
                content.classList.remove('show');
                this.setAttribute('aria-expanded', 'false');
                content.setAttribute('aria-hidden', 'true');

                // Switch icons
                if (iconDown) iconDown.classList.remove('hidden');
                if (iconUp) iconUp.classList.add('hidden');

                console.log('✅ Main.js: Collapsed description for coupon', couponId);
            } else {
                // Expand
                content.classList.remove('hidden');
                content.classList.add('show');
                this.setAttribute('aria-expanded', 'true');
                content.setAttribute('aria-hidden', 'false');

                // Switch icons
                if (iconDown) iconDown.classList.add('hidden');
                if (iconUp) iconUp.classList.remove('hidden');

                console.log('✅ Main.js: Expanded description for coupon', couponId);
            }
        });
    });


    // Scratch effect for codes
    document.querySelectorAll('.scratch-coupon-btn').forEach(button => {
        button.addEventListener('click', function() {
            const code = this.dataset.code;
            const codeText = this.querySelector('.code-text');

            if (codeText && code) {
                codeText.textContent = code;
                codeText.style.animation = 'reveal-code 0.6s ease-out';
            }
        });
    });

    // Like buttons
    document.querySelectorAll('.like-btn').forEach(button => {
        button.addEventListener('click', function() {
            const svg = this.querySelector('svg');
            const isLiked = this.classList.contains('text-red-500');

            if (isLiked) {
                this.classList.remove('text-red-500', 'bg-red-50');
                this.classList.add('text-gray-400');
                if (svg) svg.setAttribute('fill', 'none');
            } else {
                this.classList.remove('text-gray-400');
                this.classList.add('text-red-500', 'bg-red-50');
                if (svg) svg.setAttribute('fill', 'currentColor');
                this.style.animation = 'heart-pulse 0.6s ease-in-out';
                setTimeout(() => this.style.animation = '', 600);
            }
        });
    });


    // Share buttons with enhanced fallback
    document.querySelectorAll('.share-btn').forEach(button => {
        button.addEventListener('click', function() {
            const couponId = this.dataset.couponId;
            const couponTitle = this.dataset.couponTitle || 'Check out this coupon!';
            const storeName = this.dataset.storeName || '';

            // Create share URL
            const baseUrl = window.location.origin + window.location.pathname;
            const shareUrl = baseUrl + (baseUrl.includes('?') ? '&' : '?') + 'coupon=' + couponId + '#coupon-' + couponId;

            // Enhanced share title
            const shareTitle = storeName ? `${couponTitle} - ${storeName}` : couponTitle;

            // Try native sharing first (mobile devices)
            if (navigator.share && navigator.canShare && navigator.canShare({ title: shareTitle, url: shareUrl })) {
                navigator.share({
                    title: shareTitle,
                    text: 'Save money with this exclusive coupon!',
                    url: shareUrl
                }).catch(err => {
                    console.log('Share failed:', err);
                    // Fallback to clipboard
                    copyToClipboardFallback.call(this, shareUrl);
                });
            } else {
                // Fallback to clipboard copy
                copyToClipboardFallback.call(this, shareUrl);
            }
        });
    });

    // Enhanced clipboard fallback function
    function copyToClipboardFallback(text) {
        const button = this;
        const originalContent = button.innerHTML;

        // Try modern clipboard API first
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                showShareSuccess.call(button, originalContent);
            }).catch(() => {
                // Fallback to legacy method
                legacyCopyToClipboard.call(button, text, originalContent);
            });
        } else {
            // Use legacy method directly
            legacyCopyToClipboard.call(button, text, originalContent);
        }
    }

    // Legacy clipboard copy method
    function legacyCopyToClipboard(text, originalContent) {
        const button = this;
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showShareSuccess.call(button, originalContent);
            } else {
                showShareError.call(button, originalContent);
            }
        } catch (err) {
            console.log('Copy failed:', err);
            showShareError.call(button, originalContent);
        } finally {
            document.body.removeChild(textArea);
        }
    }

    // Show success feedback
    function showShareSuccess(originalContent) {
        const button = this;
        button.innerHTML = '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg><span>Copied!</span>';
        button.classList.add('text-green-600', 'bg-green-100');
        button.classList.remove('text-primary-700');

        setTimeout(() => {
            button.innerHTML = originalContent;
            button.classList.remove('text-green-600', 'bg-green-100');
            button.classList.add('text-primary-700');
        }, 2000);
    }

    // Show error feedback
    function showShareError(originalContent) {
        const button = this;
        button.innerHTML = '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg><span>Failed</span>';
        button.classList.add('text-red-600', 'bg-red-100');
        button.classList.remove('text-primary-700');

        setTimeout(() => {
            button.innerHTML = originalContent;
            button.classList.remove('text-red-600', 'bg-red-100');
            button.classList.add('text-primary-700');
        }, 2000);
    }

});
