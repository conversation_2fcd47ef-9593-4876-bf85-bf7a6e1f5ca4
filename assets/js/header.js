/**
 * Modern Glass Header - Creative Interactions & Backdrop Effects
 *
 * @package HalaCoupon
 * @version 4.0.0 - Modern Glass Rebuild
 * @description Professional header with glass morphism, smooth animations, and enhanced UX
 */

jQuery(document).ready(function($) {
    'use strict';

    // Header element cache
    const $header = $('#masthead');
    const $body = $('body');
    const $window = $(window);

    // Performance optimization - throttle scroll events
    let scrollTimeout;
    let isScrolling = false;

    /**
     * Enhanced Glass Header Scroll Effects
     * Transforms header from black to enhanced glass backdrop on scroll
     */
    function handleHeaderScroll() {
        if (isScrolling) return;

        isScrolling = true;
        requestAnimationFrame(() => {
            const scrollTop = $window.scrollTop();

            if (scrollTop > 20) {
                // Scrolled state - Enhanced glass backdrop effect
                $header.removeClass('header-default').addClass('header-scrolled');

                // Add enhanced visual effects
                $header.css({
                    'background': 'rgba(0, 0, 0, 0.6)',
                    'backdrop-filter': 'blur(20px)',
                    '-webkit-backdrop-filter': 'blur(20px)',
                    'border-bottom': '1px solid rgba(235, 191, 67, 0.3)',
                    'box-shadow': '0 4px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 30px -5px rgba(235, 191, 67, 0.15)'
                });
            } else {
                // Top state - Default black background
                $header.removeClass('header-scrolled').addClass('header-default');

                // Reset to default styles
                $header.css({
                    'background': 'rgba(0, 0, 0, 1)',
                    'backdrop-filter': 'none',
                    '-webkit-backdrop-filter': 'none',
                    'border-bottom': '1px solid rgba(235, 191, 67, 0.2)',
                    'box-shadow': '0 1px 3px rgba(235, 191, 67, 0.1)'
                });
            }

            isScrolling = false;
        });
    }

    // Optimized scroll listener with throttling
    $window.on('scroll', function() {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(handleHeaderScroll, 10);
    });

    /**
     * Enhanced Search Modal with White Theme and Improved AJAX Integration
     */
    const SearchModal = {
        $modal: $('#search_modal'),
        $input: $('#search_input'),
        $button: $('#search_p_button'),
        $closeBtn: $('.close_search_modal'),
        $results: $('#search_results'),
        $loading: $('#search_loading'),
        $tagCloud: $('#search_tag_cloud'),
        $defaultContent: $('#default_content'),
        searchTimeout: null,
        currentRequest: null,

        open() {
            this.$modal.removeClass('hidden').addClass('flex');
            $body.addClass('overflow-hidden');

            // Add slide-down animation
            const $content = this.$modal.find('.modal-content');
            $content.removeClass('animate-slide-up').addClass('animate-slide-down');

            // Enhanced focus with animation delay
            setTimeout(() => {
                this.$input.focus();
                this.$input.get(0)?.select();
            }, 200);

            // Update ARIA attributes
            this.$button.attr('aria-expanded', 'true');

            // Ensure default content is visible initially
            this.$defaultContent.removeClass('hidden');
            this.$results.addClass('hidden');

            // Trap focus within modal
            this.trapFocus();
        },

        close() {
            // Add slide-up animation before hiding
            const $content = this.$modal.find('.modal-content');
            $content.removeClass('animate-slide-down').addClass('animate-slide-up');

            // Hide modal after animation completes
            setTimeout(() => {
                this.$modal.addClass('hidden').removeClass('flex');
                $body.removeClass('overflow-hidden');
                this.$button.attr('aria-expanded', 'false');
                this.$button.focus(); // Return focus to trigger

                // Clear search and results
                this.clearSearch();

                // Reset animation classes
                $content.removeClass('animate-slide-up');
            }, 300);
        },



        clearSearch() {
            if (this.currentRequest) {
                this.currentRequest.abort();
                this.currentRequest = null;
            }
            clearTimeout(this.searchTimeout);
            this.$input.val('');
            this.$results.empty().addClass('hidden');
            this.$defaultContent.removeClass('hidden');
            this.hideLoading();
        },

        showLoading() {
            this.$loading.removeClass('hidden');

            // Replace search input icon with loading spinner
            const $searchIcon = this.$input.siblings().first().find('svg');
            $searchIcon.addClass('hidden');
            this.$input.siblings().first().append(`
                <div class="search-input-loading w-5 h-5 relative">
                    <div class="absolute inset-0 border-2 border-gray-300 rounded-full"></div>
                    <div class="absolute inset-0 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                </div>
            `);

            // Replace submit button icon with loading spinner
            const $submitIcon = this.$input.siblings().last().find('svg');
            $submitIcon.addClass('hidden');
            this.$input.siblings().last().find('.w-10').append(`
                <div class="submit-button-loading w-4 h-4 relative">
                    <div class="absolute inset-0 border-2 border-black/20 rounded-full"></div>
                    <div class="absolute inset-0 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                </div>
            `);
        },

        hideLoading() {
            this.$loading.addClass('hidden');

            // Restore search input icon
            const $searchIcon = this.$input.siblings().first().find('svg');
            $searchIcon.removeClass('hidden');
            this.$input.siblings().first().find('.search-input-loading').remove();

            // Restore submit button icon
            const $submitIcon = this.$input.siblings().last().find('svg');
            $submitIcon.removeClass('hidden');
            this.$input.siblings().last().find('.submit-button-loading').remove();
        },

        performSearch(query) {
            // Abort previous request
            if (this.currentRequest) {
                this.currentRequest.abort();
            }

            this.showLoading();

            // Hide default content and show results container
            this.$defaultContent.addClass('hidden');
            this.$results.removeClass('hidden');

            // Check if ST object is available, use fallback if not
            const ajaxUrl = (typeof ST !== 'undefined' && ST.ajax_url) ? ST.ajax_url : '/wp-admin/admin-ajax.php';

            this.currentRequest = $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'halacoupon_coupon_ajax_search',
                    ajax_sc: query
                },
                dataType: 'json',
                success: (response) => {
                    this.hideLoading();
                    console.log('Search response:', response); // Debug logging
                    this.displayResults(response.results || []);
                },
                error: (xhr, status, error) => {
                    if (xhr.statusText !== 'abort') {
                        console.error('Search AJAX error:', {
                            status: status,
                            error: error,
                            response: xhr.responseText,
                            url: ajaxUrl,
                            query: query
                        });
                        this.hideLoading();
                        this.displayError(xhr, status);
                    }
                },
                complete: () => {
                    this.currentRequest = null;
                }
            });
        },

        displayResults(results) {
            if (results.length === 0) {
                // Show helpful no results message with default content
                this.$results.html(`
                    <div class="text-center py-6 mb-6">
                        <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-soft">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <p class="text-base text-gray-700 font-semibold mb-2">${ST.no_results || 'No results found'}</p>
                        <p class="text-sm text-gray-500 mb-4">${ST.try_categories || 'Try browsing our quick categories below!'}</p>
                        <div class="w-12 h-0.5 bg-gradient-to-r from-primary to-secondary rounded-full mx-auto"></div>
                    </div>
                `).removeClass('hidden');

                // Show default content below the message
                this.$defaultContent.removeClass('hidden');
                return;
            }

            let html = `
                <div class="mb-4">
                    <div class="flex items-center gap-2 mb-3">
                        <div class="w-5 h-5 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center shadow-glow">
                            <svg class="w-2.5 h-2.5 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <h4 class="text-sm font-bold text-gray-900">Search Results (${results.length})</h4>
                    </div>
                </div>
            `;

            results.forEach(result => {
                const typeClass = result.type === 'store' ? 'bg-primary/10 text-primary border-primary/30' : 'bg-secondary/10 text-secondary border-secondary/30';
                const typeLabel = result.type === 'store' ? (ST.store || 'Store') : (ST.coupon || 'Coupon');
                const isRTL = $('html').attr('dir') === 'rtl' || $('body').hasClass('rtl');
                const flexDirection = isRTL ? 'flex-row' : '';
                const textAlign = isRTL ? 'text-right' : 'text-left';
                const arrowDirection = isRTL ? 'M15 19l-7-7 7-7' : 'M9 5l7 7-7 7';

                html += `
                    <a href="${result.url}" class="search-result-item block p-4 rounded-xl group bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-primary/30 transition-all duration-300 hover:shadow-soft mb-3" dir="${isRTL ? 'rtl' : 'ltr'}">
                        <div class="flex items-center gap-3 ${flexDirection}">
                            <div class="flex-shrink-0">
                                ${result.type === 'store' && result.image ?
                                    `<img src="${result.image}" alt="${result.title}" class="w-12 h-12 rounded-lg object-contain border border-gray-200 bg-white">` :
                                    `<div class="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center text-black font-bold text-sm text-center shadow-soft">${result.image || result.title.charAt(0)}</div>`
                                }
                            </div>
                            <div class="flex-1 min-w-0 ${textAlign}">
                                <div class="flex items-center gap-2 mb-1">
                                    <h3 class="text-sm font-bold text-gray-900 group-hover:text-primary transition-colors duration-300 truncate">${result.title}</h3>
                                    <span class="result-type-badge ${typeClass} px-2 py-1 rounded-lg text-xs font-bold border">${typeLabel}</span>
                                </div>
                                <p class="text-xs text-gray-600 group-hover:text-gray-700 transition-colors duration-300">${(ST.click_to_view || 'Click to view')}</p>
                            </div>
                            <div class="flex-shrink-0">
                                <svg class="w-5 h-5 text-gray-400 group-hover:text-primary transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${arrowDirection}"></path>
                                </svg>
                            </div>
                        </div>
                    </a>
                `;
            });

            this.$results.html(html);

            // Announce results for screen readers
            $(document).trigger('searchResultsUpdated', [results.length]);
        },

        displayError(xhr, status) {
            // Check if ST object is available, use fallback if not
            let errorMessage = (typeof ST !== 'undefined' && ST.search_error) ? ST.search_error : 'Search error occurred';
            let errorDetails = (typeof ST !== 'undefined' && ST.try_again) ? ST.try_again : 'Please try again';

            // Provide specific error messages based on status
            if (xhr && xhr.status) {
                switch (xhr.status) {
                    case 400:
                        errorMessage = 'Invalid search request';
                        errorDetails = 'Please check your search terms and try again';
                        break;
                    case 403:
                        errorMessage = 'Search access denied';
                        errorDetails = 'You may need to refresh the page and try again';
                        break;
                    case 404:
                        errorMessage = 'Search service not found';
                        errorDetails = 'The search feature may be temporarily unavailable';
                        break;
                    case 500:
                        errorMessage = 'Server error occurred';
                        errorDetails = 'Please try again in a few moments';
                        break;
                    default:
                        if (status === 'timeout') {
                            errorMessage = 'Search request timed out';
                            errorDetails = 'Please check your connection and try again';
                        }
                }
            }

            this.$results.html(`
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-red-100 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-soft">
                        <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="text-base text-red-600 font-semibold mb-2">${errorMessage}</p>
                    <p class="text-sm text-gray-500 mb-4">${errorDetails}</p>
                    <button class="retry-search-btn px-4 py-2 bg-primary hover:bg-secondary text-black font-bold rounded-lg transition-all duration-300 hover:scale-105 shadow-soft hover:shadow-glow">
                        ${(typeof ST !== 'undefined' && ST.retry_search) ? ST.retry_search : 'Try Again'}
                    </button>
                </div>
            `).removeClass('hidden');

            // Show default content below error message
            this.$defaultContent.removeClass('hidden');

            // Add retry functionality
            this.$results.find('.retry-search-btn').on('click', () => {
                const currentQuery = this.$input.val().trim();
                if (currentQuery.length >= 2) {
                    this.performSearch(currentQuery);
                }
            });
        },

        trapFocus() {
            const focusableElements = this.$modal.find('button, input, a, [tabindex]:not([tabindex="-1"])');
            const firstElement = focusableElements.first();
            const lastElement = focusableElements.last();

            this.$modal.on('keydown.modal', (e) => {
                if (e.key === 'Tab') {
                    if (e.shiftKey && document.activeElement === firstElement[0]) {
                        e.preventDefault();
                        lastElement.focus();
                    } else if (!e.shiftKey && document.activeElement === lastElement[0]) {
                        e.preventDefault();
                        firstElement.focus();
                    }
                }
            });
        },

        init() {
            // Open modal
            this.$button.on('click', (e) => {
                e.preventDefault();
                this.open();
            });

            // Close modal - multiple methods
            this.$closeBtn.on('click', () => this.close());

            // Close on backdrop click
            this.$modal.on('click', (e) => {
                if (e.target === this.$modal[0]) {
                    this.close();
                }
            });

            // Prevent modal content clicks from closing
            this.$modal.find('> div').on('click', (e) => {
                e.stopPropagation();
            });

            // Search input handling with debouncing
            this.$input.on('input', (e) => {
                const query = e.target.value.trim();

                clearTimeout(this.searchTimeout);

                if (query.length === 0) {
                    this.clearSearch();
                    return;
                }

                if (query.length >= 2) {
                    this.searchTimeout = setTimeout(() => {
                        this.performSearch(query);
                    }, 300);
                }
            });

            // Tag cloud click handling
            $(document).on('click', '.search-tag', (e) => {
                e.preventDefault();
                const searchTerm = $(e.target).data('search');
                this.$input.val(searchTerm);
                this.performSearch(searchTerm);
            });
        }
    };

    /**
     * Enhanced Mobile Menu with Brand Colors and Smooth Animations
     */
    const MobileMenu = {
        $toggle: $('#nav-toggle'),
        $modal: $('#mobile_menu_modal'),
        $closeBtn: $('[data-modal-close="mobile_menu_modal"]'),

        open() {
            this.$modal.removeClass('hidden').addClass('flex');
            $body.addClass('overflow-hidden');
            this.$toggle.attr('aria-expanded', 'true');

            // Enhanced opening animation
            this.$modal.css('opacity', '0').animate({ opacity: 1 }, 300);

            // Animate hamburger to X with brand colors
            this.animateToggle(true);

            // Add enhanced visual feedback
            this.$toggle.addClass('menu-open').css({
                'background': 'rgba(235, 191, 67, 0.3)',
                'border-color': 'rgba(235, 191, 67, 0.5)',
                'box-shadow': '0 0 20px rgba(235, 191, 67, 0.3)'
            });
        },

        close() {
            // Enhanced closing animation
            this.$modal.animate({ opacity: 0 }, 300, () => {
                this.$modal.addClass('hidden').removeClass('flex');
            });

            $body.removeClass('overflow-hidden');
            this.$toggle.attr('aria-expanded', 'false');

            // Animate X back to hamburger
            this.animateToggle(false);

            // Reset toggle button styles
            this.$toggle.removeClass('menu-open').css({
                'background': 'rgba(235, 191, 67, 0.2)',
                'border-color': 'rgba(235, 191, 67, 0.3)',
                'box-shadow': '0 0 20px rgba(235, 191, 67, 0.15)'
            });
        },

        animateToggle(isOpen) {
            const spans = this.$toggle.find('span');

            if (isOpen) {
                // Enhanced X animation with brand colors
                spans.eq(0).css({
                    'transform': 'rotate(45deg) translate(6px, 6px)',
                    'background-color': '#EBBF43'
                });
                spans.eq(1).css('opacity', '0');
                spans.eq(2).css({
                    'transform': 'rotate(-45deg) translate(6px, -6px)',
                    'background-color': '#EBBF43'
                });
            } else {
                // Reset to hamburger with white color
                spans.css({
                    'transform': 'none',
                    'opacity': '1',
                    'background-color': '#ffffff'
                });
            }
        },

        init() {
            // Toggle mobile menu
            this.$toggle.on('click', (e) => {
                e.preventDefault();

                if (this.$modal.hasClass('hidden')) {
                    this.open();
                } else {
                    this.close();
                }
            });

            // Close mobile menu
            this.$closeBtn.on('click', () => this.close());

            // Close on backdrop click
            this.$modal.on('click', (e) => {
                if (e.target === this.$modal[0]) {
                    this.close();
                }
            });

            // Close when clicking menu links
            this.$modal.find('a').on('click', () => {
                setTimeout(() => this.close(), 150);
            });
        }
    };

    /**
     * Likes Counter - REMOVED
     * Likes functionality has been completely removed from the theme.
     * Only voting system is kept for coupon feedback.
     */

    /**
     * Global Keyboard Navigation
     */
    function initKeyboardNavigation() {
        $(document).on('keydown', (e) => {
            // Escape key - close all modals
            if (e.key === 'Escape') {
                SearchModal.close();
                MobileMenu.close();
            }

            // Ctrl/Cmd + K - Open search (common UX pattern)
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                SearchModal.open();
            }
        });
    }

    /**
     * Accessibility Enhancements
     */
    function initAccessibility() {
        // Add skip link functionality
        $('.skip-link').on('click', function(e) {
            e.preventDefault();
            const target = $($(this).attr('href'));
            if (target.length) {
                target.focus();
                target.get(0).scrollIntoView({ behavior: 'smooth' });
            }
        });

        // Announce dynamic content changes
        const announcer = $('<div>', {
            'aria-live': 'polite',
            'aria-atomic': 'true',
            'class': 'sr-only'
        }).appendTo('body');

        // Announce search results
        $(document).on('searchResultsUpdated', function(_, count) {
            announcer.text(`${count} search results found`);
        });
    }

    /**
     * Performance Monitoring
     */
    function initPerformanceMonitoring() {
        // Monitor header render performance
        if (window.performance && window.performance.mark) {
            window.performance.mark('header-init-start');

            setTimeout(() => {
                window.performance.mark('header-init-end');
                window.performance.measure('header-init', 'header-init-start', 'header-init-end');
            }, 0);
        }
    }

    /**
     * Initialize All Components
     */
    function init() {
        // Core components
        SearchModal.init();
        MobileMenu.init();

        // Enhanced features
        initKeyboardNavigation();
        initAccessibility();
        initPerformanceMonitoring();

        // Initial header state
        handleHeaderScroll();

        console.log('🎨 Modern Glass Header initialized successfully');
    }

    // Initialize when DOM is ready
    init();

    // Expose API for external use
    window.HalaCouponHeader = {
        SearchModal,
        MobileMenu
    };

});
