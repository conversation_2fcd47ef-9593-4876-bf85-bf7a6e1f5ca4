/**
 * Store functionality for HalaCoupon theme
 * Handles store filters, interactions, and UI updates
 */

jQuery(document).ready(function($) {
    
    // Filter button click handler
    $('.filter-nav').on('click', function(e) {
        e.preventDefault();
        
        var $button = $(this);
        var $allButtons = $('.filter-nav');
        
        // Update button states
        $allButtons.removeClass('current active');
        $allButtons.removeClass('bg-primary-600 text-white').addClass('bg-gray-100 text-gray-700');
        $allButtons.find('.bg-white\\/20').removeClass('bg-white/20').addClass('bg-gray-200');
        
        // Activate clicked button
        $button.addClass('current active');
        $button.removeClass('bg-gray-100 text-gray-700').addClass('bg-primary-600 text-white');
        $button.find('.bg-gray-200').removeClass('bg-gray-200').addClass('bg-white/20');
        
        // Load filtered coupons
        loadCoupons();
    });

    // Filter checkbox change handler
    $('.filter-checkbox, .filter-checkbox-country').on('change', function() {
        loadCoupons();
    });

    // Reset filter handlers
    $('.reset-filter').on('click', function() {
        var filterGroup = $(this).data('filter-group');
        $('.' + filterGroup).prop('checked', false);
        loadCoupons();
    });

    // Load coupons function
    function loadCoupons() {
        var coupon_type = $('.filter-nav.current').data('filter') || 'all';
        var selectedTerms = [];
        var termId = $('.filter-nav').data('term-id');
        var taxonomy = $('.filter-nav').data('taxonomy');

        // Show loading state
        showLoadingState();

        // Gather all selected filters
        $('.filter-checkbox:checked, .filter-checkbox-country:checked').each(function() {
            selectedTerms.push({
                id: $(this).val(),
                taxonomy: $(this).data('taxonomy')
            });
        });

        // AJAX request to load filtered coupons
        $.ajax({
            url: typeof ST !== 'undefined' ? ST.ajax_url : '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'halacoupon_store_coupons_filter_combined',
                coupon_type: coupon_type,
                term_id: termId,
                taxonomy: taxonomy,
                selected_terms: selectedTerms,
                nonce: typeof ST !== 'undefined' ? ST.nonce : ''
            },
            success: function(response) {
                hideLoadingState();
                $('#coupon-listings-store').html(response);
                
                // Smooth scroll to results
                var offsetHeight = $('#masthead').outerHeight() || 80;
                $('html, body').animate({
                    scrollTop: $('#coupon-listings-store').offset().top - offsetHeight
                }, 600);
                
                // Reinitialize coupon interactions
                initializeCouponInteractions();
            },
            error: function() {
                hideLoadingState();
                $('#coupon-listings-store').html(
                    '<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">' +
                    '<div class="max-w-md mx-auto">' +
                    '<svg class="w-16 h-16 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                    '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>' +
                    '</svg>' +
                    '<h3 class="text-lg font-semibold text-gray-900 mb-2">Something went wrong</h3>' +
                    '<p class="text-gray-600">Please try again later.</p>' +
                    '</div>' +
                    '</div>'
                );
            }
        });
    }

    // Show loading state
    function showLoadingState() {
        $('#coupon-listings-store').html(
            '<div class="space-y-6">' +
            Array(3).fill(0).map(() => 
                '<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse">' +
                '<div class="flex items-start space-x-4">' +
                '<div class="w-16 h-16 bg-gray-200 rounded-lg"></div>' +
                '<div class="flex-1 space-y-3">' +
                '<div class="h-4 bg-gray-200 rounded w-3/4"></div>' +
                '<div class="h-3 bg-gray-200 rounded w-1/2"></div>' +
                '<div class="h-8 bg-gray-200 rounded w-32"></div>' +
                '</div>' +
                '</div>' +
                '</div>'
            ).join('') +
            '</div>'
        );
    }

    // Hide loading state
    function hideLoadingState() {
        // Loading state is replaced by actual content
    }

    // Initialize coupon interactions for newly loaded content
    function initializeCouponInteractions() {
        // Like buttons removed - keeping only voting system

        // OLD CLICK HANDLER DISABLED - Using new modal system only
        // $('.coupon-code-btn').off('click').on('click', function(e) {
        //     e.preventDefault();
        //
        //     var $button = $(this);
        //     var couponId = $button.data('coupon-id');
        //     var couponCode = $button.data('code');
        //     var affUrl = $button.data('aff-url');
        //     var type = $button.data('type');
        //
        //     if (type !== 'sale') {
        //         showCouponModal(couponId);
        //     }
        //
        //     trackCouponUsage(couponId, type);
        //
        //     if (couponCode && type !== 'sale') {
        //         copyToClipboard(couponCode);
        //         showCopyFeedback($button);
        //     }
        //
        //     if (affUrl) {
        //         setTimeout(function() {
        //             window.open(affUrl, '_blank');
        //         }, type === 'sale' ? 0 : 1500);
        //     }
        // });
    }

    // Like coupon function
    function likeCoupon(couponId, $button) {
        $.ajax({
            url: typeof ST !== 'undefined' ? ST.ajax_url : '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'like_coupon',
                coupon_id: couponId,
                nonce: typeof ST !== 'undefined' ? ST.nonce : ''
            },
            success: function(response) {
                if (response.success) {
                    $button.addClass('active');
                    $button.removeClass('text-gray-400 hover:text-red-500').addClass('text-red-500 hover:text-red-600');
                    $button.find('svg').replaceWith('<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>');
                    $button.attr('aria-label', 'Unlike coupon');
                    
                    // Trigger likes updated event
                    $(document).trigger('likesUpdated');
                    showToast('Coupon added to favorites!', 'success');
                }
            }
        });
    }

    // Unlike coupon function
    function unlikeCoupon(couponId, $button) {
        $.ajax({
            url: typeof ST !== 'undefined' ? ST.ajax_url : '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'unlike_coupon',
                coupon_id: couponId,
                nonce: typeof ST !== 'undefined' ? ST.nonce : ''
            },
            success: function(response) {
                if (response.success) {
                    $button.removeClass('active');
                    $button.removeClass('text-red-500 hover:text-red-600').addClass('text-gray-400 hover:text-red-500');
                    $button.find('svg').replaceWith('<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>');
                    $button.attr('aria-label', 'Like coupon');
                    
                    // Trigger likes updated event
                    $(document).trigger('likesUpdated');
                    showToast('Coupon removed from favorites!', 'success');
                }
            }
        });
    }

    // Show toast notification
    function showToast(message, type) {
        var bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
        var toast = $('<div class="fixed top-4 right-4 z-50 px-6 py-3 text-white rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 ' + bgColor + '">' + message + '</div>');
        
        $('body').append(toast);
        
        setTimeout(function() {
            toast.removeClass('translate-x-full');
        }, 100);
        
        setTimeout(function() {
            toast.addClass('translate-x-full');
            setTimeout(function() {
                toast.remove();
            }, 300);
        }, 3000);
    }

    // Track coupon usage
    function trackCouponUsage(couponId, type) {
        $.ajax({
            url: typeof ST !== 'undefined' ? ST.ajax_url : '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'track_coupon_usage',
                coupon_id: couponId,
                coupon_type: type,
                nonce: typeof ST !== 'undefined' ? ST.nonce : ''
            }
        });
    }

    // Show copy feedback
    function showCopyFeedback($button) {
        var originalText = $button.find('.get-code').text();
        $button.find('.get-code').text('Copied!');
        $button.addClass('bg-green-600 hover:bg-green-700');
        
        setTimeout(function() {
            $button.find('.get-code').text(originalText);
            $button.removeClass('bg-green-600 hover:bg-green-700');
        }, 2000);
    }

    // OLD MODAL FUNCTION DISABLED - Using fresh modal system only
    // function showCouponModal(couponId) {
    //     var $modal = $('.coupon-code-modal[data-modal-id="' + couponId + '"]');
    //     if ($modal.length) {
    //         $modal.removeClass('hidden').addClass('flex');
    //         $('body').addClass('overflow-hidden');
    //     }
    // }

    // Initialize on page load
    initializeCouponInteractions();

});
