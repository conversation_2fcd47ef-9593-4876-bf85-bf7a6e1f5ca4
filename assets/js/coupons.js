/**
 * Coupon functionality for HalaCoupon theme
 * Handles coupon interactions, modals, and copying
 */

jQuery(document).ready(function($) {
    

    // Copy to clipboard function
    window.copyToClipboard = function(text, redirectUrl) {
        if (typeof text === 'string') {
            // Direct text copy
            navigator.clipboard.writeText(text).then(function() {
                showToast('Code copied to clipboard!', 'success');
                if (redirectUrl) {
                    setTimeout(function() {
                        window.open(redirectUrl, '_blank');
                    }, 1000);
                }
            }).catch(function() {
                // Fallback for older browsers
                var textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('Code copied to clipboard!', 'success');
                if (redirectUrl) {
                    setTimeout(function() {
                        window.open(redirectUrl, '_blank');
                    }, 1000);
                }
            });
        } else {
            // Element ID copy
            var element = document.getElementById(text);
            if (element) {
                element.select();
                element.setSelectionRange(0, 99999);
                navigator.clipboard.writeText(element.value).then(function() {
                    showToast('Copied to clipboard!', 'success');
                }).catch(function() {
                    document.execCommand('copy');
                    showToast('Copied to clipboard!', 'success');
                });
            }
        }
    };

    // Show copy feedback
    function showCopyFeedback($button) {
        var originalText = $button.find('.get-code').text();
        $button.find('.get-code').text('Copied!');
        $button.addClass('bg-green-600 hover:bg-green-700');
        
        setTimeout(function() {
            $button.find('.get-code').text(originalText);
            $button.removeClass('bg-green-600 hover:bg-green-700');
        }, 2000);
    }

    // Toast notification function
    function showToast(message, type) {
        var bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
        var toast = $('<div class="fixed top-4 right-4 z-50 px-6 py-3 text-white rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 ' + bgColor + '">' + message + '</div>');
        
        $('body').append(toast);
        
        setTimeout(function() {
            toast.removeClass('translate-x-full');
        }, 100);
        
        setTimeout(function() {
            toast.addClass('translate-x-full');
            setTimeout(function() {
                toast.remove();
            }, 300);
        }, 3000);
    }

    // Track coupon usage
    function trackCouponUsage(couponId, type) {
        $.ajax({
            url: typeof ST !== 'undefined' ? ST.ajax_url : '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'track_coupon_usage',
                coupon_id: couponId,
                coupon_type: type,
                nonce: typeof ST !== 'undefined' ? ST.nonce : ''
            },
            success: function(response) {
                // Update usage count if element exists
                var $usageCount = $('.today-used .count');
                if ($usageCount.length && response.success && response.data.new_count) {
                    $usageCount.text(response.data.new_count);
                }
            }
        });
    }



    // Share button functionality
    $('.coupon-share-button').on('click', function(e) {
        e.preventDefault();
        var variation = $(this).data('variation');
        var $popup = $('.' + variation);
        if ($popup.length) {
            $popup.removeClass('hidden').addClass('flex');
        }
    });

    // Close share popup
    $('.coupon-share-popup .close, .coupon-share-popup').on('click', function(e) {
        if (e.target === this || $(this).hasClass('close')) {
            $('.coupon-share-popup').addClass('hidden').removeClass('flex');
        }
    });

    // Like button functionality removed - keeping only voting system

}); // End jQuery document ready
