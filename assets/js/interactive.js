/**
 * Interactive Elements for HalaCoupon theme
 * Handles buttons, pagination, ratings, and other interactive components
 */

jQuery(document).ready(function($) {
    
    // Initialize all interactive elements
    initializeInteractiveElements();
    
    function initializeInteractiveElements() {
        // Initialize buttons
        initializeButtons();
        
        
        // Initialize share buttons
        initializeShareButtons();
        
        // Initialize copy buttons
        initializeCopyButtons();
        
        // Initialize rating stars
        initializeRatingStars();
        
        // Initialize pagination
        initializePagination();
        
        // Initialize tooltips
        initializeTooltips();
        
        // Initialize ripple effects
        initializeRippleEffects();
    }
    
    // Initialize buttons
    function initializeButtons() {
        // Add loading state to buttons
        $('.btn[data-loading]').on('click', function() {
            var $btn = $(this);
            if (!$btn.hasClass('btn-loading')) {
                $btn.addClass('btn-loading').prop('disabled', true);
                
                // Remove loading state after specified time or 3 seconds
                var loadingTime = $btn.data('loading') || 3000;
                setTimeout(function() {
                    $btn.removeClass('btn-loading').prop('disabled', false);
                }, loadingTime);
            }
        });
        
        // Handle button groups
        $('.btn-group .btn').on('click', function() {
            var $btn = $(this);
            var $group = $btn.closest('.btn-group');
            
            if (!$group.hasClass('btn-group-multiple')) {
                $group.find('.btn').removeClass('active');
                $btn.addClass('active');
            } else {
                $btn.toggleClass('active');
            }
        });
    }
    

    
    // Initialize share buttons
    function initializeShareButtons() {
        $('.share-button').on('click', function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var url = $button.data('url') || window.location.href;
            var title = $button.data('title') || document.title;
            var text = $button.data('text') || '';
            
            // Check if Web Share API is supported
            if (navigator.share) {
                navigator.share({
                    title: title,
                    text: text,
                    url: url
                }).then(function() {
                    showToast('Shared successfully!', 'success');
                }).catch(function(error) {
                    console.log('Error sharing:', error);
                    fallbackShare(url, title);
                });
            } else {
                fallbackShare(url, title);
            }
        });
        
        // Social media share buttons
        $('.share-facebook').on('click', function(e) {
            e.preventDefault();
            var url = $(this).data('url') || window.location.href;
            shareOnPlatform('facebook', url);
        });
        
        $('.share-twitter').on('click', function(e) {
            e.preventDefault();
            var url = $(this).data('url') || window.location.href;
            var text = $(this).data('text') || '';
            shareOnPlatform('twitter', url, text);
        });
        
        $('.share-whatsapp').on('click', function(e) {
            e.preventDefault();
            var url = $(this).data('url') || window.location.href;
            var text = $(this).data('text') || '';
            shareOnPlatform('whatsapp', url, text);
        });
    }
    
    // Initialize copy buttons
    function initializeCopyButtons() {
        $('.copy-button').on('click', function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var textToCopy = $button.data('copy') || $button.text();
            
            copyToClipboard(textToCopy).then(function() {
                // Show success state
                $button.addClass('copied');
                var originalText = $button.html();
                $button.html('<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg> Copied!');
                
                setTimeout(function() {
                    $button.removeClass('copied').html(originalText);
                }, 2000);
                
                showToast('Copied to clipboard!', 'success');
            }).catch(function() {
                showToast('Failed to copy. Please try again.', 'error');
            });
        });
    }
    
    // Initialize rating stars
    function initializeRatingStars() {
        $('.rating-interactive .rating-star').on('click', function() {
            var $star = $(this);
            var $rating = $star.closest('.rating-stars');
            var rating = $star.data('rating');
            
            // Update visual state
            $rating.find('.rating-star').each(function(index) {
                if (index < rating) {
                    $(this).addClass('filled').removeClass('half-filled');
                } else {
                    $(this).removeClass('filled half-filled');
                }
            });
            
            // Trigger rating change event
            $rating.trigger('ratingChanged', [rating]);
        });
        
        $('.rating-interactive .rating-star').on('mouseenter', function() {
            var $star = $(this);
            var $rating = $star.closest('.rating-stars');
            var rating = $star.data('rating');
            
            // Show preview
            $rating.find('.rating-star').each(function(index) {
                if (index < rating) {
                    $(this).addClass('preview-filled');
                } else {
                    $(this).removeClass('preview-filled');
                }
            });
        });
        
        $('.rating-interactive').on('mouseleave', function() {
            $(this).find('.rating-star').removeClass('preview-filled');
        });
    }
    
    // Initialize pagination
    function initializePagination() {
        $('.pagination-item').on('click', function(e) {
            if ($(this).hasClass('disabled') || $(this).hasClass('active')) {
                e.preventDefault();
                return false;
            }
            
            // Add loading state
            $(this).addClass('btn-loading');
        });
    }
    
    // Initialize tooltips
    function initializeTooltips() {
        $('[data-tooltip]').each(function() {
            var $element = $(this);
            var tooltipText = $element.data('tooltip');
            var position = $element.data('tooltip-position') || 'top';
            
            $element.on('mouseenter', function() {
                showTooltip($element, tooltipText, position);
            }).on('mouseleave', function() {
                hideTooltip();
            });
        });
    }
    
    // Initialize ripple effects
    function initializeRippleEffects() {
        $('.btn-ripple').on('click', function(e) {
            var $button = $(this);
            var rect = this.getBoundingClientRect();
            var x = e.clientX - rect.left;
            var y = e.clientY - rect.top;
            
            var $ripple = $('<span class="ripple-effect"></span>');
            $ripple.css({
                position: 'absolute',
                left: x - 25,
                top: y - 25,
                width: 50,
                height: 50,
                borderRadius: '50%',
                background: 'rgba(255, 255, 255, 0.3)',
                transform: 'scale(0)',
                animation: 'ripple 0.6s linear',
                pointerEvents: 'none'
            });
            
            $button.append($ripple);
            
            setTimeout(function() {
                $ripple.remove();
            }, 600);
        });
    }
    
    // Utility functions
    function copyToClipboard(text) {
        if (navigator.clipboard && window.isSecureContext) {
            return navigator.clipboard.writeText(text);
        } else {
            // Fallback for older browsers
            return new Promise(function(resolve, reject) {
                var textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                try {
                    document.execCommand('copy');
                    textArea.remove();
                    resolve();
                } catch (error) {
                    textArea.remove();
                    reject(error);
                }
            });
        }
    }
    
    function fallbackShare(url, title) {
        // Copy URL to clipboard as fallback
        copyToClipboard(url).then(function() {
            showToast('Link copied to clipboard!', 'success');
        }).catch(function() {
            showToast('Unable to share. Please copy the URL manually.', 'error');
        });
    }
    
    function shareOnPlatform(platform, url, text) {
        var shareUrl = '';
        
        switch (platform) {
            case 'facebook':
                shareUrl = 'https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(url);
                break;
            case 'twitter':
                shareUrl = 'https://twitter.com/intent/tweet?url=' + encodeURIComponent(url) + '&text=' + encodeURIComponent(text);
                break;
            case 'whatsapp':
                shareUrl = 'https://wa.me/?text=' + encodeURIComponent(text + ' ' + url);
                break;
        }
        
        if (shareUrl) {
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }
    }
    
    function showTooltip($element, text, position) {
        var $tooltip = $('<div class="tooltip">' + text + '</div>');
        $tooltip.css({
            position: 'absolute',
            background: '#1f2937',
            color: 'white',
            padding: '0.5rem',
            borderRadius: '0.375rem',
            fontSize: '0.75rem',
            whiteSpace: 'nowrap',
            zIndex: 9999,
            pointerEvents: 'none'
        });
        
        $('body').append($tooltip);
        
        var elementRect = $element[0].getBoundingClientRect();
        var tooltipRect = $tooltip[0].getBoundingClientRect();
        
        var left, top;
        
        switch (position) {
            case 'top':
                left = elementRect.left + (elementRect.width - tooltipRect.width) / 2;
                top = elementRect.top - tooltipRect.height - 8;
                break;
            case 'bottom':
                left = elementRect.left + (elementRect.width - tooltipRect.width) / 2;
                top = elementRect.bottom + 8;
                break;
            case 'left':
                left = elementRect.left - tooltipRect.width - 8;
                top = elementRect.top + (elementRect.height - tooltipRect.height) / 2;
                break;
            case 'right':
                left = elementRect.right + 8;
                top = elementRect.top + (elementRect.height - tooltipRect.height) / 2;
                break;
        }
        
        $tooltip.css({ left: left, top: top });
    }
    
    function hideTooltip() {
        $('.tooltip').remove();
    }
    
    function showToast(message, type) {
        var bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
        var toast = $('<div class="fixed top-4 right-4 z-50 px-6 py-3 text-white rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 ' + bgColor + '">' + message + '</div>');
        
        $('body').append(toast);
        
        setTimeout(function() {
            toast.removeClass('translate-x-full');
        }, 100);
        
        setTimeout(function() {
            toast.addClass('translate-x-full');
            setTimeout(function() {
                toast.remove();
            }, 300);
        }, 3000);
    }
    
    // Expose functions globally
    window.showToast = showToast;
    window.copyToClipboard = copyToClipboard;

});
