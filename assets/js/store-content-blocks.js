/**
 * Store Content Blocks - Simple Navigation
 *
 * Provides collapsible table of contents and smooth scrolling navigation.
 *
 * @package HalaCoupon
 * @version 1.0.0
 * <AUTHOR>
 */

(function($) {
    'use strict';

    /**
     * Simple Store Content Blocks Navigation
     */
    class StoreContentBlocks {

        constructor() {
            this.tocToggle = null;
            this.tocNav = null;
            this.tocList = null;
            this.isExpanded = false;
            this.init();
        }

        /**
         * Initialize the content blocks system
         */
        init() {
            this.setupTableOfContents();
            this.bindEvents();
        }

        /**
         * Setup table of contents functionality
         */
        setupTableOfContents() {
            this.tocToggle = $('.store-toc-toggle');
            this.tocNav = $('.store-toc-nav');
            this.tocList = $('.store-content-nav-list');

            if (this.tocToggle.length && this.tocNav.length) {
                // Initialize collapsed state
                this.isExpanded = false;
                this.tocToggle.attr('aria-expanded', 'false');
                this.tocList.attr('aria-hidden', 'true');

                // Set initial collapsed state
                this.tocNav.css('max-height', '0');
            }
        }

        /**
         * Bind event handlers
         */
        bindEvents() {
            // Table of contents toggle
            $(document).on('click', '.store-toc-toggle', this.handleTocToggle.bind(this));

            // Smooth scrolling for navigation links
            $(document).on('click', '.store-content-nav-link', this.handleNavClick.bind(this));

            // Skip link functionality
            $(document).on('click', '.skip-link', this.handleSkipLink.bind(this));
        }

        /**
         * Handle table of contents toggle
         */
        handleTocToggle(e) {
            e.preventDefault();

            const $toggle = $(e.currentTarget);
            const $nav = $toggle.closest('.store-toc-container').find('.store-toc-nav');
            const $list = $toggle.closest('.store-toc-container').find('.store-content-nav-list');

            this.isExpanded = !this.isExpanded;

            // Update ARIA attributes
            $toggle.attr('aria-expanded', this.isExpanded);
            $list.attr('aria-hidden', !this.isExpanded);

            // Simple toggle without animations
            if (this.isExpanded) {
                $nav.css('max-height', 'none');
            } else {
                $nav.css('max-height', '0');
            }
        }

        /**
         * Handle navigation link clicks with SEO-optimized smooth scrolling
         */
        handleNavClick(e) {
            e.preventDefault();

            const $link = $(e.currentTarget);
            const targetId = $link.attr('href');
            const safeTargetId = $link.attr('data-target-id');
            const seoSlug = $link.attr('data-seo-slug');

            // Try multiple strategies to find the target element
            let $target = this.findTargetElement(targetId, safeTargetId, seoSlug);

            if ($target.length) {
                // Update URL with SEO-friendly hash (for better SEO and sharing)
                if (seoSlug && history.replaceState) {
                    const newUrl = window.location.pathname + window.location.search + '#' + seoSlug;
                    history.replaceState(null, '', newUrl);
                }

                // Calculate scroll position with proper offset
                const headerHeight = $('header').outerHeight() || 0;
                const adminBarHeight = $('#wpadminbar').outerHeight() || 0;
                const scrollOffset = headerHeight + adminBarHeight + 20;

                // Simple smooth scroll to target
                $('html, body').animate({
                    scrollTop: $target.offset().top - scrollOffset
                }, 600);

                // Auto-collapse TOC on mobile after navigation
                if (window.innerWidth <= 768 && this.isExpanded) {
                    this.tocToggle.trigger('click');
                }

                // Track SEO navigation for analytics
                if (typeof gtag !== 'undefined' && seoSlug) {
                    gtag('event', 'seo_navigation', {
                        'event_category': 'Store Content SEO',
                        'event_label': seoSlug,
                        'custom_parameter_1': $link.attr('data-encoded') === 'true' ? 'encoded' : 'standard'
                    });
                }
            } else {
                console.warn('Store Content Blocks: Target element not found for:', targetId, 'Safe ID:', safeTargetId, 'SEO Slug:', seoSlug);
            }
        }

        /**
         * Find target element using multiple strategies for URL-encoded content
         */
        findTargetElement(targetId, safeTargetId, seoSlug) {
            let $target = $();

            // Strategy 1: Try safe target ID first (most reliable for CSS selectors)
            if (safeTargetId) {
                try {
                    $target = $('#' + safeTargetId);
                    if ($target.length) return $target;
                } catch (error) {
                    // Continue to next strategy
                }
            }

            // Strategy 2: Try original target ID
            if (targetId) {
                try {
                    $target = $(targetId);
                    if ($target.length) return $target;
                } catch (error) {
                    // Continue to next strategy
                }
            }

            // Strategy 3: Use attribute selectors for URL-encoded content
            const selectors = [seoSlug, targetId?.replace('#', ''), safeTargetId].filter(Boolean);

            for (const selector of selectors) {
                // Try direct attribute matching
                $target = $('[id="' + selector + '"]');
                if ($target.length) return $target;

                // Try SEO anchor attribute
                $target = $('[data-seo-anchor="' + selector + '"]');
                if ($target.length) return $target;

                // Try URL-decoded version
                try {
                    const decoded = decodeURIComponent(selector);
                    $target = $('[id="' + decoded + '"]');
                    if ($target.length) return $target;
                } catch (error) {
                    // Continue
                }
            }

            return $();
        }

        /**
         * Escape CSS selectors for jQuery
         */
        escapeSelector(selector) {
            if (!selector) return '';

            // Remove the # if present
            const id = selector.replace('#', '');

            // Escape special characters for CSS selectors
            const escaped = id.replace(/[!"#$%&'()*+,.\/:;<=>?@[\\\]^`{|}~]/g, '\\$&');

            return '#' + escaped;
        }

        /**
         * Handle skip link functionality
         */
        handleSkipLink(e) {
            e.preventDefault();

            const $link = $(e.currentTarget);
            const targetId = $link.attr('href');

            // Use proper selector escaping for complex IDs
            let $target;
            try {
                $target = $(targetId);
            } catch (error) {
                const escapedId = this.escapeSelector(targetId);
                $target = $(escapedId);
            }

            // Fallback: find by ID attribute directly
            if (!$target.length && targetId) {
                const cleanId = targetId.replace('#', '');
                $target = $('[id="' + cleanId + '"]');
            }

            if ($target.length) {
                // Simple smooth scroll to target
                $('html, body').animate({
                    scrollTop: $target.offset().top
                }, 400);
            }
        }
    }



    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        // Only initialize if content blocks exist
        if ($('.store-content-blocks-container').length) {
            window.storeContentBlocks = new StoreContentBlocks();
        }
    });

})(jQuery);
