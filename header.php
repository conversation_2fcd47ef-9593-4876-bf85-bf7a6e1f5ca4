<?php
/**
 * Modern Glass Header Template - Creative Design with Backdrop Effects
 *
 * @package HalaCoupon
 * @version 4.0.0 - Modern Glass Rebuild
 * @description Professional header with glass morphism effects, smooth transitions, and brand integration
 */

global $ag_option;
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>

<head>
	<meta charset="<?php bloginfo('charset'); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="profile" href="http://gmpg.org/xfn/11">
	<link rel="pingback" href="<?php bloginfo('pingback_url'); ?>">
	<?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>

	<header id="masthead" class="fixed top-0 left-0 right-0 z-50 header-default" role="banner" aria-label="<?php esc_attr_e('Main Navigation', 'halacoupon'); ?>">

		<div class="absolute inset-0 overflow-hidden pointer-events-none">
			<div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/10 to-transparent rounded-full transform translate-x-16 -translate-y-16 transition-all duration-700"></div>
			<div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-secondary/10 to-transparent rounded-full transform -translate-x-12 translate-y-12 transition-all duration-700"></div>
		</div>

		<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="flex items-center justify-between h-16 lg:h-20" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">

				<button
					id="nav-toggle"
					class="lg:hidden group relative flex items-center justify-center w-12 h-12 rounded-2xl bg-primary/20 backdrop-blur-sm border border-primary/30 hover:bg-primary/30 hover:border-primary/50 transition-all duration-300 hover:scale-105 shadow-glow hover:shadow-glow-secondary"
					aria-label="<?php esc_attr_e('Toggle mobile menu', 'halacoupon'); ?>"
					aria-expanded="false"
					aria-controls="mobile_menu_modal"
				>
					<div class="relative w-5 h-5">
						<span class="absolute top-0 left-0 w-full h-0.5 bg-white group-hover:bg-primary rounded-full transition-all duration-300 transform origin-center"></span>
						<span class="absolute top-2 left-0 w-full h-0.5 bg-white group-hover:bg-primary rounded-full transition-all duration-300"></span>
						<span class="absolute top-4 left-0 w-full h-0.5 bg-white group-hover:bg-primary rounded-full transition-all duration-300 transform origin-center"></span>
					</div>
				</button>

				<!-- Logo Section with Enhanced Design -->
				<div class="flex-1 lg:flex-initial">
					<?php if ( halacoupon_get_option('site_logo', false, 'url') != '' ) : ?>
						<a
							href="<?php echo esc_url( home_url( '/' ) ); ?>"
							class="group relative flex items-center transition-all duration-300 hover:scale-105"
							title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>"
							rel="home"
							aria-label="<?php echo esc_attr( sprintf( __('Go to %s homepage', 'halacoupon'), get_bloginfo( 'name' ) ) ); ?>"
						>
							<img
								src="<?php echo esc_url( halacoupon_get_option('site_logo', false, 'url') ); ?>"
								alt="<?php echo esc_attr( get_bloginfo( 'name' ) ); ?>"
								class="h-8 lg:h-12 w-auto transition-all duration-300 group-hover:scale-105"
								loading="eager"
								decoding="async"
							/>
						</a>
					<?php else : ?>
						<div class="text-center lg:text-left">
							<?php if ( is_home() || is_front_page() ) : ?>
								<h1 class="text-xl lg:text-3xl font-bold">
									<a
										href="<?php echo esc_url( home_url( '/' ) ); ?>"
										class="group relative inline-block py-2 rounded-2xl logo-gradient-text hover:scale-105"
										title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>"
										rel="home"
									>
										<?php bloginfo( 'name' ); ?>
										<span class="absolute inset-0 bg-white/20 backdrop-blur-sm rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></span>
									</a>
								</h1>
							<?php else : ?>
								<h2 class="text-xl lg:text-3xl font-bold">
									<a
										href="<?php echo esc_url( home_url( '/' ) ); ?>"
										class="group relative inline-block py-2 rounded-2xl logo-gradient-text hover:scale-105"
										title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>"
										rel="home"
									>
										<?php bloginfo( 'name' ); ?>
										<span class="absolute inset-0 bg-white/20 backdrop-blur-sm rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></span>
									</a>
								</h2>
							<?php endif; ?>
						</div>
					<?php endif; ?>
				</div>

			<!-- Modern Desktop Navigation with Glass Effect -->
			<nav id="site-header-nav" class="hidden lg:flex flex-1 justify-center mx-8" role="navigation" aria-label="<?php esc_attr_e('Primary Navigation', 'halacoupon'); ?>" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
				<div class="relative px-6 py-3">
					<?php
					wp_nav_menu( array(
						'theme_location' => 'primary',
						'container' => false,
						'menu_class' => 'flex items-center ' . (is_rtl() ? 'space-x-reverse space-x-2' : 'space-x-2'),
						'fallback_cb' => false,
						'walker' => new class extends Walker_Nav_Menu {
							function start_lvl( &$output, $depth = 0, $args = null ) {
								$rtl_class = is_rtl() ? 'right-0' : 'left-0';
								$output .= '<ul class="absolute top-full ' . $rtl_class . ' mt-3 bg-white/95 backdrop-blur-md shadow-float-lg rounded-2xl py-3 min-w-56 z-50 border border-white/30 opacity-0 invisible group-hover:opacity-100 group-hover:visible transform translate-y-2 group-hover:translate-y-0 transition-all duration-300">';
							}
							function end_lvl( &$output, $depth = 0, $args = null ) {
								$output .= '</ul>';
							}
							function start_el( &$output, $item, $depth = 0, $args = null, $id = 0 ) {
								if ( $depth === 0 ) {
									$output .= '<li class="relative group">';
									$output .= '<a href="' . esc_url( $item->url ) . '" class="relative px-4 py-2 text-sm font-semibold text-white hover:text-primary rounded-2xl transition-all duration-300 hover:bg-primary/20 hover:shadow-glow hover:scale-105 flex items-center gap-2">';
								} else {
									$output .= '<li>';
									$output .= '<a href="' . esc_url( $item->url ) . '" class="block px-4 py-3 text-sm font-medium text-gray-700 hover:bg-primary/10 hover:text-primary rounded-xl transition-all duration-300 hover:translate-x-1">';
								}
								$output .= esc_html( $item->title );
								$output .= '</a>';
							}
							function end_el( &$output, $item, $depth = 0, $args = null ) {
								$output .= '</li>';
							}
						}
					) );
					?>
				</div>
			</nav>

			<!-- Modern Header Actions with Glass Effects -->
			<div class="flex items-center space-x-3">

				<!-- Search Button with Brand Design -->
				<button
					id="search_p_button"
					type="button"
					class="group relative flex items-center justify-center w-12 h-12 rounded-2xl bg-secondary/20 backdrop-blur-sm border border-secondary/30 hover:bg-secondary/30 hover:border-secondary/50 transition-all duration-300 hover:scale-105 shadow-glow-secondary hover:shadow-float"
					aria-label="<?php esc_attr_e('Open search modal', 'halacoupon'); ?>"
					aria-expanded="false"
					aria-controls="search_modal"
				>
					<svg class="w-5 h-5 text-white group-hover:text-secondary transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
					</svg>
					<span class="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-primary to-secondary rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
				</button>

				<!-- Likes functionality removed - keeping only voting system -->

				<!-- Country Dropdown with Glass Effect -->
				<div class="relative">
					<?php echo output_country_dropdown(); ?>
				</div>
			</div>
		</div>
	</div>
</header>

<!-- Search Modal Template -->
<?php get_template_part( 'inc/search-modal' ); ?>

<!-- Mobile Navigation Template -->
<?php get_template_part( 'inc/mobile-nav' ); ?>

<!-- Content area with proper spacing -->
<div id="content" class="site-content pt-20">
	<?php echo halacoupon_breadcrumbs(); ?>
