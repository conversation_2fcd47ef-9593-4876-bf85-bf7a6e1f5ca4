<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */


get_header();

// Get page data
$page_template = get_page_template_slug();
$has_featured_image = has_post_thumbnail();
?>

<!-- Enhanced Page Schema -->
<div itemscope itemtype="https://schema.org/WebPage">

    <!-- Dynamic Hero Section -->
    <section class="relative <?php echo $has_featured_image ? 'h-96 md:h-[500px]' : 'py-16 md:py-24'; ?> overflow-hidden">

        <?php if ($has_featured_image) : ?>
        <!-- Featured Image Background -->
        <div class="absolute inset-0">
            <?php
            the_post_thumbnail('full', [
                'class' => 'w-full h-full object-cover',
                'itemprop' => 'image'
            ]);
            ?>
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
        </div>
        <?php else : ?>
        <!-- Gradient Background -->
        <div class="absolute inset-0 bg-gradient-primary"></div>
        <?php endif; ?>

        <!-- Floating Elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl animate-float"></div>
            <div class="absolute bottom-10 right-10 w-24 h-24 bg-primary/20 rounded-full blur-lg animate-float" style="animation-delay: 2s;"></div>
            <div class="absolute top-1/3 right-1/4 w-16 h-16 bg-accent/15 rounded-full blur-md animate-float" style="animation-delay: 4s;"></div>
        </div>

        <!-- Hero Content -->
        <div class="relative h-full flex items-center">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl <?php echo $has_featured_image ? 'text-white' : 'text-text'; ?>">

                    <!-- Breadcrumb -->
                    <nav class="mb-6" aria-label="Breadcrumb">
                        <ol class="flex items-center gap-2 text-sm <?php echo $has_featured_image ? 'text-white/80' : 'text-text/70'; ?>" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
                            <li><a href="<?php echo esc_url(home_url('/')); ?>" class="hover:<?php echo $has_featured_image ? 'text-white' : 'text-primary'; ?> transition-colors duration-200"><?php esc_html_e('Home', 'halacoupon'); ?></a></li>
                            <li><span class="mx-2"><?php echo is_rtl() ? '←' : '→'; ?></span></li>
                            <li class="<?php echo $has_featured_image ? 'text-white' : 'text-text'; ?> font-medium"><?php the_title(); ?></li>
                        </ol>
                    </nav>

                    <!-- Page Title -->
                    <h1 class="text-4xl md:text-6xl font-bold leading-tight mb-6" itemprop="headline">
                        <?php the_title(); ?>
                    </h1>

                    <!-- Page Excerpt/Description -->
                    <?php if (has_excerpt()) : ?>
                    <p class="text-xl md:text-2xl <?php echo $has_featured_image ? 'text-white/90' : 'text-text/80'; ?> leading-relaxed max-w-3xl" itemprop="description">
                        <?php the_excerpt(); ?>
                    </p>
                    <?php endif; ?>

                    <!-- Page Meta -->
                    <div class="flex items-center gap-4 mt-6 text-sm <?php echo $has_featured_image ? 'text-white/80' : 'text-text/70'; ?>">
                        <time datetime="<?php echo esc_attr(get_the_modified_date('c')); ?>" itemprop="dateModified">
                            <?php printf(__('Last updated: %s', 'halacoupon'), get_the_modified_date()); ?>
                        </time>
                    </div>

                </div>
            </div>
        </div>

    </section>

    <!-- Main Content Area -->
    <main class="relative py-12 md:py-20 bg-background min-h-screen">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">

                <!-- Content Card -->
                <article class="glass-card rounded-3xl border border-white/30 overflow-hidden shadow-2xl" id="post-<?php the_ID(); ?>" <?php post_class('page-content'); ?>>

                    <!-- Content Body -->
                    <div class="px-6 md:px-12 py-8 md:py-12">

                        <!-- Main Content -->
                        <div class="page-content prose prose-lg max-w-none prose-headings:text-text prose-p:text-text/80 prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-strong:text-text prose-code:text-primary prose-code:bg-primary/10 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-blockquote:border-l-primary prose-blockquote:bg-primary/5 prose-blockquote:pl-6 prose-blockquote:py-4 prose-blockquote:rounded-r-lg"
                             itemprop="mainContentOfPage">
                            <?php
                            the_post();
                            the_content();
                            ?>
                        </div>

                        <!-- Page Links -->
                        <div class="mt-8">
                            <?php halacoupon_wp_link_pages(); ?>
                        </div>

                    </div>

                </article>

                <!-- Comments Section -->
                <?php if (comments_open() || get_comments_number()) : ?>
                <section class="glass-card rounded-2xl border border-white/30 overflow-hidden mt-8">
                    <div class="p-6 md:p-8">
                        <h2 class="text-2xl font-bold text-text mb-6 flex items-center gap-3">
                            <svg class="w-6 h-6 text-primary" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                            </svg>
                            <?php esc_html_e('Comments', 'halacoupon'); ?>
                            <span class="text-lg text-text/60">(<?php echo get_comments_number(); ?>)</span>
                        </h2>
                        <?php comments_template(); ?>
                    </div>
                </section>
                <?php endif; ?>

                <!-- Related Pages (if applicable) -->
                <?php
                // Get child pages if this is a parent page
                $child_pages = get_children([
                    'post_parent' => get_the_ID(),
                    'post_type' => 'page',
                    'post_status' => 'publish',
                    'numberposts' => 6
                ]);

                if ($child_pages) :
                ?>
                <section class="mt-12">
                    <h2 class="text-2xl font-bold text-text mb-8 text-center"><?php esc_html_e('Related Pages', 'halacoupon'); ?></h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php foreach ($child_pages as $child_page) : ?>
                        <article class="group glass-card rounded-xl border border-white/30 hover:border-primary/40 hover:scale-105 transition-all duration-300 overflow-hidden">

                            <?php if (has_post_thumbnail($child_page->ID)) : ?>
                            <div class="aspect-video overflow-hidden">
                                <?php echo get_the_post_thumbnail($child_page->ID, 'medium', [
                                    'class' => 'w-full h-full object-cover group-hover:scale-110 transition-transform duration-500'
                                ]); ?>
                            </div>
                            <?php endif; ?>

                            <div class="p-4">
                                <h3 class="font-semibold text-text group-hover:text-primary transition-colors duration-200 mb-2 line-clamp-2">
                                    <a href="<?php echo esc_url(get_permalink($child_page->ID)); ?>">
                                        <?php echo esc_html($child_page->post_title); ?>
                                    </a>
                                </h3>

                                <?php if ($child_page->post_excerpt) : ?>
                                <p class="text-sm text-text/70 line-clamp-2 mb-3">
                                    <?php echo esc_html($child_page->post_excerpt); ?>
                                </p>
                                <?php endif; ?>

                                <a href="<?php echo esc_url(get_permalink($child_page->ID)); ?>"
                                   class="inline-flex items-center gap-1 text-sm text-primary font-medium hover:text-primary-600 transition-colors duration-200">
                                    <?php esc_html_e('Learn More', 'halacoupon'); ?>
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </article>
                        <?php endforeach; ?>
                    </div>
                </section>
                <?php endif; ?>

            </div>
        </div>
    </main>

</div>

<style>
/* Glass morphism effects */
.glass-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
}

.glass-card:hover {
    backdrop-filter: blur(15px);
    background: rgba(255, 255, 255, 0.9);
}

/* Line clamp utility */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Prose enhancements */
.prose img {
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.prose blockquote {
    position: relative;
}

.prose blockquote::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: -10px;
    font-size: 4rem;
    color: #EBBF43;
    opacity: 0.3;
    font-family: 'Rubik', system-ui, sans-serif;
}
</style>

<?php get_footer(); ?>
