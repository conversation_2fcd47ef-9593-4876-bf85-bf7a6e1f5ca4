{"name": "halacoupon", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build-css": "tailwindcss -i ./assets/css/src/main.css -o ./assets/css/dist/main.css --watch", "build-css-prod": "tailwindcss -i ./assets/css/src/main.css -o ./assets/css/dist/main.css --minify", "watch": "npm run build-css", "build": "npm run build-css-prod", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "tailwindcss-animate": "^1.0.7"}}