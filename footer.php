<?php
/**
 * Footer Template - Modern Creative Design
 *
 * Enhanced with Tailwind CSS creative design system
 * Features: Glass morphism, gradients, animations, modern newsletter
 *
 * @package HalaCoupon
 * @version 2.0.0 - Modernized
 */

global $ag_option;

$footer_text = halacoupon_get_option( 'footer_text_identity' );
$footer_text_help = halacoupon_get_option( 'footer_text_help' );

?>
</div> <!-- END .site-content -->

<footer id="main_Footer" class="relative py-12 sm:py-16 md:py-20 lg:py-24 overflow-hidden"
	style="background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #1f2937 100%);" role="contentinfo">
	<!-- Background with Dark Gradient -->
	<div class="absolute inset-0 pointer-events-none z-0"
		style="background: linear-gradient(135deg, rgba(31, 41, 55, 0.95) 0%, rgba(17, 24, 39, 0.98) 50%, rgba(31, 41, 55, 0.95) 100%);">
	</div>
	<div class="absolute inset-0 pointer-events-none z-0"
		style="background: linear-gradient(45deg, rgba(213, 96, 48, 0.08) 0%, transparent 50%, rgba(102, 224, 147, 0.08) 100%);">
	</div>

	<!-- Floating Elements (hidden on xs, reduced on sm) -->
	<div class="hidden sm:block absolute top-6 left-6 w-20 h-20 md:w-32 md:h-32 rounded-full animate-float blur-xl z-0"
		style="background: rgba(213, 96, 48, 0.25);"></div>
	<div class="hidden sm:block absolute bottom-6 right-6 w-28 h-28 md:w-48 md:h-48 rounded-full animate-float blur-2xl z-0"
		style="background: rgba(102, 224, 147, 0.15); animation-delay: 2s;"></div>
	<div class="hidden sm:block absolute top-1/2 left-1/4 w-16 h-16 md:w-24 md:h-24 rounded-full animate-pulse blur-2xl z-0"
		style="background: rgba(162, 233, 147, 0.25);"></div>

	<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16 xl:py-20 z-10">
		<div class="grid grid-cols-1 lg:grid-cols-12 gap-6 sm:gap-8 lg:gap-12 xl:gap-16">
			<div class="lg:col-span-5 space-y-4 sm:space-y-6 lg:space-y-8 animate-fade-in-up <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>"
				dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
				<?php if ( halacoupon_get_option('site_logo', false, 'url') != '' ) : ?>
				<div class="group flex justify-start">
					<a href="<?php echo esc_url( home_url( '/' ) ); ?>"
						title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>" rel="home"
						class="inline-block relative">
						<div class="relative p-2 sm:p-3 transition-all duration-300 group-hover:scale-105">
							<img src="<?php echo halacoupon_get_option('site_logo', false, 'url'); ?>"
								alt="<?php echo get_bloginfo( 'name' ) ?>"
								class="h-8 sm:h-10 lg:h-12 w-auto transition-all duration-300" />
						</div>
						<!-- Glow effect -->
						<div
							class="absolute inset-0 bg-gradient-primary rounded-xl lg:rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-lg">
						</div>
					</a>
				</div>
				<?php else : ?>
				<div class="group flex <?php echo is_rtl() ? 'justify-end' : 'justify-start'; ?>">
					<h2
						class="text-xl sm:text-2xl lg:text-3xl font-bold <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
						<a href="<?php echo esc_url( home_url( '/' ) ); ?>"
							class="text-white hover:text-primary transition-all duration-500"
							title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>"
							rel="home"><?php bloginfo( 'name' ); ?></a>
					</h2>
				</div>
				<?php endif; ?>

				<!-- Footer Description with PROPER RTL/LTR Text Alignment -->
				<div class="relative max-w-md lg:max-w-none <?php echo is_rtl() ? 'mr-0' : 'ml-0'; ?>">
					<p
						class="text-gray-300 text-sm sm:text-base lg:text-lg leading-relaxed <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
						<?php echo esc_html( $footer_text ); ?>
					</p>
				</div>

				<!-- Social Profiles with PROPER RTL/LTR Layout -->
				<div class="space-y-3 sm:space-y-4">
					<h3
						class="text-base sm:text-lg font-semibold text-white flex items-center gap-2 <?php echo is_rtl() ? 'justify-end flex-row-reverse' : 'justify-start'; ?>">
						<svg class="w-4 h-4 sm:w-5 sm:h-5 text-secondary" fill="none" stroke="currentColor"
							viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
							</path>
						</svg>
						<?php esc_html_e('Follow Us', 'halacoupon'); ?>
					</h3>
					<div
						class="flex flex-wrap <?php echo is_rtl() ? 'justify-end flex-row-reverse space-x-reverse space-x-2' : 'justify-start space-x-2'; ?> sm:<?php echo is_rtl() ? 'space-x-reverse space-x-3' : 'space-x-3'; ?>">
						<?php halacoupon_display_social_profiles(); ?>
					</div>
				</div>
			</div>

			<!-- Footer Menus Section with PROPER RTL/LTR Support -->
			<div class="lg:col-span-7 animate-fade-in-up mt-8 lg:mt-0" style="animation-delay: 0.2s;"
				dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
				<div
					class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
					<?php
					$locations = get_nav_menu_locations();
					$footer_menus = [
						'footer_menu_1' => esc_html__( 'Company', 'halacoupon' ),
						'footer_menu_2' => esc_html__( 'Support', 'halacoupon' ),
						'footer_menu_3' => esc_html__( 'Resources', 'halacoupon' ),
					];

					foreach ( $footer_menus as $menu_location => $default_title ) :
						if ( isset( $locations[ $menu_location ] ) ) :
							$menu_object = wp_get_nav_menu_object( $locations[ $menu_location ] );
							$menu_name = $menu_object ? $menu_object->name : $default_title;
							?>
					<div class="space-y-3 sm:space-y-4 lg:space-y-6 group">
						<h3
							class="text-sm sm:text-base lg:text-lg font-bold text-white flex items-center gap-1.5 lg:gap-2 group-hover:text-secondary transition-colors duration-300 <?php echo is_rtl() ? 'flex-row-reverse text-right justify-end' : 'text-left justify-start'; ?>">
							<div
								class="w-1.5 h-1.5 lg:w-2 lg:h-2 bg-gradient-secondary rounded-full group-hover:bg-accent transition-colors duration-300">
							</div>
							<?php echo esc_html( $menu_name ); ?>
						</h3>
						<div class="space-y-2 lg:space-y-3 <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
							<?php
								wp_nav_menu( array(
									'theme_location' => $menu_location,
									'container'      => false,
									'menu_class'     => 'space-y-3',
									'link_before'    => '',
									'link_after'     => '',
									'walker'         => new class extends Walker_Nav_Menu {
										function start_lvl( &$output, $depth = 0, $args = null ) {
											$rtl_class = is_rtl() ? 'mr-3 lg:mr-4 text-right' : 'ml-3 lg:ml-4 text-left';
											$output .= '<ul class="' . $rtl_class . ' mt-1.5 lg:mt-2 space-y-1.5 lg:space-y-2">';
										}
										function end_lvl( &$output, $depth = 0, $args = null ) {
											$output .= '</ul>';
										}
										function start_el( &$output, $item, $depth = 0, $args = null, $id = 0 ) {
											$rtl_classes = is_rtl() ? 'flex-row-reverse text-right justify-end hover:-translate-x-1' : 'text-left justify-start hover:translate-x-1';
											$icon_classes = is_rtl() ? 'w-2.5 h-2.5 lg:w-3 lg:h-3 ml-1.5 lg:ml-2 rotate-180' : 'w-2.5 h-2.5 lg:w-3 lg:h-3 mr-1.5 lg:mr-2';
											$output .= '<li>';
											$output .= '<a href="' . esc_url( $item->url ) . '" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300 ' . $rtl_classes . '">';
											$output .= '<svg class="' . $icon_classes . ' opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
											$output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
											$output .= '</svg>';
											$output .= '<span class="text-xs sm:text-sm font-medium">' . esc_html( $item->title ) . '</span>';
											$output .= '</a>';
										}
										function end_el( &$output, $item, $depth = 0, $args = null ) {
											$output .= '</li>';
										}
									}
								) );
								?>
						</div>
					</div>
					<?php else : ?>
					<!-- Fallback content when menu is not set -->
					<div class="space-y-6 group">
						<h3 class="text-lg font-bold text-white flex items-center gap-2">
							<div class="w-2 h-2 bg-gradient-secondary rounded-full"></div>
							<?php echo esc_html( $default_title ); ?>
						</h3>
						<div class="space-y-3">
							<p class="text-gray-400 text-sm">
								<?php esc_html_e('Menu not configured', 'halacoupon'); ?>
							</p>
						</div>
					</div>
					<?php endif; ?>
					<?php endforeach; ?>

					<div class="space-y-3 sm:space-y-4 lg:space-y-6 group">
						<p
							class="text-sm text-white/90 mb-4 lg:mb-8 leading-relaxed max-w-full lg:max-w-2xl <?php echo is_rtl() ? 'mr-0 text-right' : 'ml-0 text-left'; ?>">
							<?php esc_html_e( 'Get exclusive deals, early access to sales, and personalized offers delivered straight to your inbox', 'halacoupon' ) ?>
						</p>

						<div class="relative mb-4 lg:mb-6">
							<?php halacoupon_render_newsletter_form(); ?>
						</div>

					</div>
				</div>
			</div>

			<!-- Modern Footer Help Text -->
			<?php if ( !empty($footer_text_help) ) : ?>
			<div class="mt-12 sm:mt-16 col-span-1 lg:col-span-12 pt-8 sm:pt-12 border-t border-white/10 animate-fade-in-up"
				style="animation-delay: 0.6s;">
				<div class="text-center">
					<div class="max-w-3xl mx-auto">
						<div
							class="inline-flex items-center gap-2 text-xs bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full border border-white/10 mb-6">
							<svg class="w-5 h-5 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
									d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
								</path>
							</svg>
							<p class="text-white"><?php echo esc_html($footer_text_help); ?></p>							
						</div>

					</div>
				</div>
			</div>
			<?php endif; ?>
		</div>

		<!-- Footer Bottom with PROPER RTL/LTR Layout -->
		<div class="mt-8 sm:mt-12 pt-4 sm:pt-6  border-t border-white/10 animate-fade-in-up"
			style="animation-delay: 0.8s;" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
			<div
				class="flex flex-col lg:flex-row justify-between items-center space-y-3 sm:space-y-4 lg:space-y-0 <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">

				<!-- Copyright Section with PROPER RTL/LTR -->
				<div
					class="flex flex-col sm:flex-row items-center gap-1.5 sm:gap-2 lg:gap-3 text-gray-300 order-3 lg:order-1 <?php echo is_rtl() ? 'flex-row-reverse' : ''; ?>">
					<div
						class="w-5 h-5 sm:w-6 sm:h-6 lg:w-8 lg:h-8 bg-gradient-primary rounded-lg lg:rounded-xl flex items-center justify-center">
						<svg class="w-2.5 h-2.5 sm:w-3 sm:h-3 lg:w-4 lg:h-4 text-white" fill="none"
							stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<span class="text-xs sm:text-xs lg:text-sm <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
						<?php
						printf(
							esc_html__( 'Copyright © %1$s %2$s. All Rights Reserved.', 'halacoupon' ),
							esc_attr(date('Y')),
							'<span class="font-semibold text-white">' . get_bloginfo('name') . '</span>'
						);
						?>
					</span>
				</div>

				<!-- Footer Links with PROPER RTL/LTR -->
				<div
					class="flex flex-wrap items-center justify-center gap-2 sm:gap-3 lg:gap-6 text-xs lg:text-sm order-1 lg:order-2 <?php echo is_rtl() ? 'flex-row-reverse' : ''; ?>">
					<a href="#"
						class="text-gray-300 hover:text-white transition-colors duration-300 hover:underline px-1 <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
						<?php esc_html_e('Privacy Policy', 'halacoupon'); ?>
					</a>
					<span class="text-gray-500 hidden sm:inline">•</span>
					<a href="#"
						class="text-gray-300 hover:text-white transition-colors duration-300 hover:underline px-1 <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
						<?php esc_html_e('Terms of Service', 'halacoupon'); ?>
					</a>
					<span class="text-gray-500 hidden sm:inline">•</span>
					<a href="#"
						class="text-gray-300 hover:text-white transition-colors duration-300 hover:underline px-1 <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
						<?php esc_html_e('Contact Us', 'halacoupon'); ?>
					</a>
				</div>

				<!-- Credits Section with PROPER RTL/LTR -->
				<div
					class="flex flex-wrap items-center justify-center gap-1 lg:gap-2 text-gray-300 text-xs lg:text-sm order-2 lg:order-3">
					<span><?php esc_html_e('Designed with', 'halacoupon'); ?></span>
					<svg class="w-3 h-3 lg:w-4 lg:h-4 text-red-400 animate-pulse" fill="currentColor"
						viewBox="0 0 24 24">
						<path
							d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
					</svg>
					<span
						class="<?php echo is_rtl() ? 'text-right' : 'text-left'; ?>"><?php esc_html_e('by', 'halacoupon'); ?></span>
					<a href="https://abdullah-g.com/" rel="nofollow noopener" target="_blank"
						class="font-semibold text-primary hover:text-secondary transition-all duration-300 <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
						AGS
					</a>
				</div>
			</div>
		</div>
	</div>
</footer>


<?php
get_template_part('template-parts/global-coupon-modal');
wp_footer();
?>
</body>

</html>