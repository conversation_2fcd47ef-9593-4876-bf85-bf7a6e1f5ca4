<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */





get_header();

// Get post data
$is_rtl = is_rtl();
$reading_time = halacoupon_get_reading_time();
$author_id = get_the_author_meta('ID');
$author_name = get_the_author();
$author_avatar_url = get_avatar_url($author_id, ['size' => 96]);
$categories = get_the_category();
$tags = get_the_tags();
$featured_image_url = get_the_post_thumbnail_url(get_the_ID(), 'full');
?>

<!-- Enhanced Article Schema Markup -->
<article <?php post_class('modern-post-entry'); ?> itemscope itemtype="https://schema.org/BlogPosting">

    <!-- Modern Hero Section with Parallax Effect -->
    <section class="relative h-[50vh] overflow-hidden">

        <!-- Background Image with Parallax -->
        <?php if ($featured_image_url) : ?>
        <div class="absolute inset-0 parallax-bg">
            <img src="<?php echo esc_url($featured_image_url); ?>" class="w-full h-full object-cover scale-110" alt="<?php echo esc_attr(get_the_title()); ?>" itemprop="image"/>
        </div>
        <?php else : ?>
        <!-- Fallback gradient background -->
        <div class="absolute inset-0 bg-gradient-primary"></div>
        <?php endif; ?>

        <!-- Overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/60 to-transparent"></div>

        <!-- Floating Elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute top-20 <?php echo $is_rtl ? 'right-10' : 'left-10'; ?> w-32 h-32 bg-white/5 rounded-full blur-xl animate-float"></div>
            <div class="absolute bottom-20 <?php echo $is_rtl ? 'left-10' : 'right-10'; ?> w-24 h-24 bg-primary/10 rounded-full blur-lg animate-float-slow"></div>
            <div class="absolute top-1/3 <?php echo $is_rtl ? 'left-1/4' : 'right-1/4'; ?> w-16 h-16 bg-accent/10 rounded-full blur-md animate-float-fast"></div>
        </div>

        <!-- Hero Content -->
        <div class="relative h-full flex items-end">
            <div class="container mx-auto px-4 pb-34 md:pb-32">
                <div class="max-w-4xl mx-auto text-white">

                    <!-- Title -->
                    <h1 class="text-4xl md:text-6xl font-bold text-white leading-tight my-6" itemprop="headline">
                        <?php the_title(); ?>
                    </h1>
                    
                    <!-- Excerpt -->
                    <?php if (has_excerpt()) : ?>
                    <p class="text-xl md:text-2xl text-white/90 leading-relaxed max-w-3xl" itemprop="description">
                        <?php the_excerpt(); ?>
                    </p>
                    <?php endif; ?>

                    <!-- Meta -->
                    <div class="mt-8 pt-6 border-t border-white/20 flex flex-wrap items-center gap-x-6 gap-y-4 text-white/90">
                        <!-- Author -->
                        <div class="flex items-center gap-3" itemprop="author" itemscope itemtype="https://schema.org/Person">
                            <img src="<?php echo esc_url($author_avatar_url); ?>" alt="<?php echo esc_attr($author_name); ?>" class="w-10 h-10 rounded-full border-2 border-white/30">
                            <div class="text-sm">
                                <span class="font-semibold" itemprop="name"><?php echo esc_html($author_name); ?></span>
                                <p class="text-white/70"><?php esc_html_e('Author', 'halacoupon'); ?></p>
                            </div>
                        </div>

                        <!-- Date -->
                        <div class="flex items-center gap-3">
                             <div class="w-10 h-10 flex items-center justify-center">
                                <svg class="w-6 h-6 text-white/70" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg>
                            </div>
                            <div class="text-sm">
                                <time datetime="<?php echo esc_attr(get_the_date('c')); ?>" itemprop="datePublished"><?php echo esc_html(get_the_date()); ?></time>
                                <p class="text-white/70"><?php esc_html_e('Published on', 'halacoupon'); ?></p>
                            </div>
                        </div>

                        <!-- Reading Time -->
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 flex items-center justify-center">
                                <svg class="w-6 h-6 text-white/70" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg>
                            </div>
                            <div class="text-sm">
                                <span><?php printf(__('%d min read', 'halacoupon'), $reading_time); ?></span>
                                <p class="text-white/70"><?php esc_html_e('Reading time', 'halacoupon'); ?></p>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- Scroll Down Indicator -->
        <div class="absolute bottom-16 left-1/2 -translate-x-1/2 animate-bounce">
            <div class="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
            </div>
        </div>

    </section>

    <!-- Floating Content Card -->
    <div class="relative -mt-16 z-10">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">

                <!-- Main Content Card -->
                <div class="glass-card rounded-3xl border border-white/20 overflow-hidden shadow-2xl">

                    <!-- Social Sharing Bar -->
                    <div class="sticky top-0 z-20 backdrop-blur-md border-b border-gray-100 px-6 py-4">
                        <div class="flex items-center justify-between">

                            <!-- Progress Bar -->
                            <div class="flex-1 <?php echo $is_rtl ? 'ml-6' : 'mr-6'; ?>">
                                <div class="h-1 bg-gray-200 rounded-full overflow-hidden">
                                    <div id="reading-progress" class="h-full bg-gradient-primary transition-all duration-300 ease-out" style="width: 0%"></div>
                                </div>
                            </div>

                            <!-- Social Share Buttons -->
                            <div class="flex items-center gap-3">
                                <span class="text-sm text-text/70 hidden md:block"><?php esc_html_e('Share:', 'halacoupon'); ?></span>
                                <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" target="_blank" rel="noopener" class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200" aria-label="<?php esc_attr_e('Share on Twitter', 'halacoupon'); ?>"><svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg></a>
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" target="_blank" rel="noopener" class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200" aria-label="<?php esc_attr_e('Share on Facebook', 'halacoupon'); ?>"><svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg></a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(get_permalink()); ?>" target="_blank" rel="noopener" class="w-8 h-8 bg-blue-700 text-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200" aria-label="<?php esc_attr_e('Share on LinkedIn', 'halacoupon'); ?>"><svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg></a>
                                <button onclick="copyToClipboard('<?php echo esc_js(get_permalink()); ?>', this)" class="w-8 h-8 bg-gray-600 text-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200" aria-label="<?php esc_attr_e('Copy link', 'halacoupon'); ?>">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- Article Content -->
                    <div class="px-6 md:px-12 py-8 md:py-12">

                        <!-- Table of Contents (if headings exist) -->
                        <div id="table-of-contents" class="hidden mb-8 p-6 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl border border-primary/10">
                            <h3 class="text-lg font-semibold text-text mb-4 flex items-center gap-2">
                                <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>
                                <?php esc_html_e('Table of Contents', 'halacoupon'); ?>
                            </h3>
                            <ul id="toc-list" class="space-y-2"></ul>
                        </div>

                        <!-- Main Content -->
                        <div class="entry-content prose prose-lg max-w-none prose-headings:scroll-mt-28" itemprop="articleBody">
                            <?php the_content(); ?>
                        </div>

                    </div>

                </div>

            </div>
        </div>
    </div>

    <!-- Post Footer Section -->
    <div class="container mx-auto px-4 py-12">
        <div class="max-w-4xl mx-auto space-y-12">

            <!-- Tags Section -->
            <?php if ($tags) : ?>
            <section class="glass-card rounded-2xl p-6 border border-white/20">
                <h3 class="text-lg font-semibold text-text mb-4 flex items-center gap-2">
                    <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true"><path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path></svg>
                    <?php esc_html_e('Tags', 'halacoupon'); ?>
                </h3>
                <div class="flex flex-wrap gap-2">
                    <?php foreach ($tags as $tag) : ?>
                    <a href="<?php echo esc_url(get_tag_link($tag->term_id)); ?>" class="inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-primary/10 to-secondary/10 text-primary text-sm rounded-full hover:from-primary/20 hover:to-secondary/20 transition-all duration-200" itemprop="keywords">
                        <span>#</span>
                        <?php echo esc_html($tag->name); ?>
                    </a>
                    <?php endforeach; ?>
                </div>
            </section>
            <?php endif; ?>

            <!-- Author Bio -->
            <section class="glass-card rounded-2xl p-6 md:p-8 border border-white/20" itemscope itemtype="https://schema.org/Person">
                <div class="flex flex-col md:flex-row gap-6 items-center">
                    <div class="flex-shrink-0">
                        <img class="w-24 h-24 rounded-2xl shadow-lg" src="<?php echo esc_url($author_avatar_url); ?>" alt="<?php echo esc_attr($author_name); ?>" itemprop="image">
                    </div>
                    <div class="<?php echo $is_rtl ? 'md:text-right' : ''; ?>">
                        <p class="text-sm text-primary font-semibold tracking-wider uppercase"><?php esc_html_e('Written By', 'halacoupon'); ?></p>
                        <h3 class="text-xl font-bold text-text mt-1 mb-2" itemprop="name">
                            <?php echo esc_html($author_name); ?>
                        </h3>

                        <?php $author_bio = get_the_author_meta('description'); ?>
                        <?php if ($author_bio) : ?>
                        <p class="text-text/80" itemprop="description">
                            <?php echo esc_html($author_bio); ?>
                        </p>
                        <?php endif; ?>

                        <div class="flex items-center gap-4 mt-4 <?php echo $is_rtl ? 'justify-end' : ''; ?>">
                            <a href="<?php echo esc_url(get_author_posts_url($author_id)); ?>" class="inline-flex items-center gap-2 text-primary font-medium hover:text-primary-600 transition-colors duration-200">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path></svg>
                                <?php esc_html_e('View all posts', 'halacoupon'); ?>
                            </a>

                            <!-- Social Links -->
                            <?php
                            $twitter = get_the_author_meta('twitter');
                            $facebook = get_the_author_meta('facebook');
                            if ($twitter || $facebook) :
                            ?>
                            <div class="flex items-center gap-2">
                                <?php if ($twitter) : ?>
                                <a href="<?php echo esc_url($twitter); ?>" target="_blank" rel="noopener" class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200" aria-label="<?php esc_attr_e('Share on Twitter', 'halacoupon'); ?>">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>
                                </a>
                                <?php endif; ?>

                                <?php if ($facebook) : ?>
                                <a href="<?php echo esc_url($facebook); ?>" target="_blank" rel="noopener" class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200" aria-label="<?php esc_attr_e('Share on Facebook', 'halacoupon'); ?>">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>
                                </a>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Post Navigation -->
            <?php
            $prev_post = get_previous_post();
            $next_post = get_next_post();
            if ($prev_post || $next_post) :
            ?>
            <nav class="grid grid-cols-1 md:grid-cols-2 gap-6" aria-label="<?php esc_attr_e('Post navigation', 'halacoupon'); ?>">
                <?php if ($prev_post) : ?>
                <a href="<?php echo esc_url(get_permalink($prev_post)); ?>" class="group nav-card">
                    <div class="flex-1 min-w-0 <?php echo $is_rtl ? 'text-right' : ''; ?>">
                        <p class="text-sm text-text/60 mb-1"><?php esc_html_e('Previous Post', 'halacoupon'); ?></p>
                        <h4 class="font-semibold text-text group-hover:text-primary transition-colors duration-200 line-clamp-2">
                            <?php echo esc_html(get_the_title($prev_post)); ?>
                        </h4>
                    </div>
                    <div class="nav-arrow-icon">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $is_rtl ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'; ?>"></path></svg>
                    </div>
                </a>
                <?php else: ?>
                <div></div>
                <?php endif; ?>

                <?php if ($next_post) : ?>
                <a href="<?php echo esc_url(get_permalink($next_post)); ?>" class="group nav-card">
                     <div class="nav-arrow-icon">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $is_rtl ? 'M15 19l-7-7 7-7' : 'M9 5l7 7-7 7'; ?>"></path></svg>
                    </div>
                    <div class="flex-1 min-w-0 <?php echo $is_rtl ? '' : 'text-right'; ?>">
                        <p class="text-sm text-text/60 mb-1"><?php esc_html_e('Next Post', 'halacoupon'); ?></p>
                        <h4 class="font-semibold text-text group-hover:text-primary transition-colors duration-200 line-clamp-2">
                            <?php echo esc_html(get_the_title($next_post)); ?>
                        </h4>
                    </div>
                </a>
                <?php endif; ?>
            </nav>
            <?php endif; ?>


            <!-- Page Links -->
            <?php halacoupon_wp_link_pages(); ?>

            <!-- Comments Section -->
            <?php if (comments_open() || get_comments_number()) : ?>
            <section class="glass-card rounded-2xl border border-white/20 overflow-hidden">
                <div class="p-6 md:p-8">
                    <h2 class="text-2xl font-bold text-text mb-6 flex items-center gap-3">
                        <svg class="w-6 h-6 text-primary" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true"><path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path></svg>
                        <?php esc_html_e('Comments', 'halacoupon'); ?>
                        <span class="text-lg text-text/60">(<?php echo get_comments_number(); ?>)</span>
                    </h2>
                    <?php comments_template(); ?>
                </div>
            </section>
            <?php endif; ?>

        </div>
    </div>

</article>

<!-- Enhanced JavaScript for Single Post -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const isRtl = <?php echo json_encode($is_rtl); ?>;
    // Reading Progress Bar
    const progressBar = document.getElementById('reading-progress');
    const article = document.querySelector('[itemprop="articleBody"]');

    if (progressBar && article) {
        function updateProgress() {
            const articleTop = article.offsetTop;
            const articleHeight = article.offsetHeight;
            const windowHeight = window.innerHeight;
            const scrollTop = window.pageYOffset;

            const progress = Math.min(100, Math.max(0,
                ((scrollTop - articleTop + windowHeight) / articleHeight) * 100
            ));

            progressBar.style.width = progress + '%';
        }

        window.addEventListener('scroll', updateProgress, { passive: true });
        updateProgress();
    }

    // Table of Contents Generation
    const tocContainer = document.getElementById('table-of-contents');
    const tocList = document.getElementById('toc-list');

    if (article && tocContainer && tocList) {
        const headings = article.querySelectorAll('h2, h3, h4');
        if (headings.length > 1) {
            tocContainer.classList.remove('hidden');

            headings.forEach((heading, index) => {
                if (!heading.id) {
                    heading.id = 'heading-' + index;
                }
                const li = document.createElement('li');
                const a = document.createElement('a');
                a.href = '#' + heading.id;
                a.textContent = heading.textContent;
                a.className = 'text-sm text-text/70 hover:text-primary transition-colors duration-200 block py-1';
                if (heading.tagName === 'H3') {
                    a.className += isRtl ? ' mr-4' : ' ml-4';
                } else if (heading.tagName === 'H4') {
                    a.className += isRtl ? ' mr-8' : ' ml-8';
                }
                li.appendChild(a);
                tocList.appendChild(li);
            });
        }
    }

    // Smooth scrolling for TOC links
    document.querySelectorAll('#toc-list a').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Parallax effect for hero background
    const parallaxBg = document.querySelector('.parallax-bg img');
    if (parallaxBg) {
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.3;
            parallaxBg.style.transform = `translateY(${rate}px) scale(1.1)`;
        }, { passive: true });
    }
});

// Copy to clipboard function
function copyToClipboard(text, button) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const originalHTML = button.innerHTML;
        const checkIcon = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
        button.innerHTML = checkIcon;
        button.classList.add('bg-green-500');

        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove('bg-green-500');
        }, 2000);
    });
}
</script>

<style>
/* Glass morphism effects */
.glass-card {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
}
@supports not ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
    .glass-card {
        background: rgba(255, 255, 255, 0.95);
    }
}

/* Post Navigation Cards */
.nav-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.6);
    transition: all 0.2s ease-out;
}
html.no-glass .nav-card { background-color: #fff; }
.nav-card:hover {
    border-color: #d56030;
    transform: scale(1.03);
}
.nav-arrow-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background: linear-gradient(to right, #d56030, #d56030); /* bg-gradient-primary */
    color: white;
    transition: transform 0.2s ease-out;
}
.nav-card:hover .nav-arrow-icon {
    transform: scale(1.1);
}

/* Line clamp utility */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Prose enhancements */
.prose h2, .prose h3, .prose h4 {
    scroll-margin-top: 100px; /* Offset for sticky header */
}
.prose img, .prose video {
    border-radius: 1rem; /* rounded-2xl */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.07);
}

/* Parallax container */
.parallax-bg {
    will-change: transform;
}
</style>

<?php get_footer(); ?>