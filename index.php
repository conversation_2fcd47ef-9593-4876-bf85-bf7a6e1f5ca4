<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */

get_header();
?>

<!-- Enhanced Blog Page with Brand Colors -->
<div class="min-h-screen bg-background">

    <!-- Enhanced Hero Section with Dark Glass Background & Blog Content -->
    <section class="relative py-16 sm:py-20 lg:py-24 overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <!-- Enhanced floating elements with better positioning -->
        <div class="absolute inset-0 pointer-events-none z-0">
            <div class="absolute top-20 left-20 w-48 h-48 bg-gradient-secondary rounded-full filter blur-3xl opacity-20 animate-float"></div>
            <div class="absolute bottom-20 right-20 w-40 h-40 bg-gradient-primary rounded-full filter blur-2xl opacity-15 animate-float-slow"></div>
            <div class="absolute top-1/3 right-1/4 w-32 h-32 bg-gradient-accent rounded-full filter blur-xl opacity-10 animate-float-fast"></div>
            <!-- Additional glass blur elements -->
            <div class="absolute inset-0 bg-gradient-to-br from-black/50 via-transparent to-black/30 backdrop-blur-sm"></div>
        </div>

        <div class="relative max-w-5xl mx-auto px-6 sm:px-8 z-10">
            <!-- Enhanced Brand Badge with better contrast -->
            <div class="flex justify-center mb-8">
                <span class="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-secondary to-primary shadow-glow text-white font-bold text-sm backdrop-blur-xl border-2 border-white/30 hover:border-white/50 transition-all duration-300 transform hover:scale-105">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                    </svg>
                    <?php esc_html_e('Creative Blog', 'halacoupon'); ?>
                </span>
            </div>

            <!-- Fixed H1 Title with proper gradient text -->
            <h1 class="text-center text-5xl sm:text-6xl lg:text-7xl font-black font-rubik mb-8 leading-tight drop-shadow-2xl">
                <span class="bg-gradient-to-r from-white via-yellow-200 to-white bg-clip-text text-transparent">
                    <?php
                    if (is_home() && !is_front_page()) {
                        echo esc_html(get_the_title(get_option('page_for_posts')));
                    } elseif (is_archive()) {
                        echo esc_html(get_the_archive_title());
                    } elseif (is_search()) {
                        printf(__('Search Results for: %s', 'halacoupon'), '<span class="text-yellow-400">' . esc_html(get_search_query()) . '</span>');
                    } else {
                        esc_html_e('Latest Articles', 'halacoupon');
                    }
                    ?>
                </span>
            </h1>

            <!-- Enhanced Subtitle with better styling -->
            <?php if (is_home() && !is_front_page()) : ?>
            <p class="text-center text-xl sm:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-12 font-medium">
                <?php esc_html_e('Stay updated with the latest deals, savings tips, and coupon news', 'halacoupon'); ?>
            </p>
            <?php elseif (is_archive()) : ?>
            <p class="text-center text-xl sm:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-12 font-medium">
                <?php the_archive_description(); ?>
            </p>
            <?php endif; ?>

            <!-- Enhanced Blog Statistics -->
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-8 mb-8 max-w-4xl mx-auto">
                
                <!-- Total Posts -->
                <div class="group bg-white/10 backdrop-blur-2xl rounded-3xl shadow-float-lg border-2 border-white/20 p-8 hover:bg-white/15 hover:border-white/30 transition-all duration-500 transform hover:scale-105 hover:shadow-glow">
                    <div class="text-center">
                        <div class="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-primary to-secondary rounded-2xl shadow-glow group-hover:shadow-glow-secondary transition-all duration-300 mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                            </svg>
                        </div>
                        <div class="text-3xl font-black text-white mb-1"><?php echo wp_count_posts()->publish; ?></div>
                        <div class="text-sm font-semibold text-white/90 uppercase tracking-wide"><?php esc_html_e('Total Articles', 'halacoupon'); ?></div>
                    </div>
                </div>

                <!-- Categories -->
                <div class="group bg-white/10 backdrop-blur-2xl rounded-3xl shadow-float-lg border-2 border-white/20 p-8 hover:bg-white/15 hover:border-white/30 transition-all duration-500 transform hover:scale-105 hover:shadow-glow-secondary">
                    <div class="text-center">
                        <div class="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-secondary to-accent rounded-2xl shadow-glow-secondary group-hover:shadow-glow transition-all duration-300 mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                            </svg>
                        </div>
                        <div class="text-3xl font-black text-white mb-1"><?php echo count(get_categories(['hide_empty' => false])); ?></div>
                        <div class="text-sm font-semibold text-white/90 uppercase tracking-wide"><?php esc_html_e('Categories', 'halacoupon'); ?></div>
                    </div>
                </div>

                <!-- Search Results (if searching) -->
                <?php if (is_search()) : ?>
                <div class="group bg-white/10 backdrop-blur-2xl rounded-3xl shadow-float-lg border-2 border-white/20 p-8 hover:bg-white/15 hover:border-white/30 transition-all duration-500 transform hover:scale-105 hover:shadow-glow">
                    <div class="text-center">
                        <div class="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-accent to-primary rounded-2xl shadow-glow group-hover:shadow-glow-secondary transition-all duration-300 mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        <div class="text-3xl font-black text-white mb-1"><?php echo $wp_query->found_posts; ?></div>
                        <div class="text-sm font-semibold text-white/90 uppercase tracking-wide"><?php esc_html_e('Search Results', 'halacoupon'); ?></div>
                    </div>
                </div>
                <?php else : ?>
                <!-- Latest Posts -->
                <div class="group bg-white/10 backdrop-blur-2xl rounded-3xl shadow-float-lg border-2 border-white/20 p-8 hover:bg-white/15 hover:border-white/30 transition-all duration-500 transform hover:scale-105 hover:shadow-glow">
                    <div class="text-center">
                        <div class="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-accent to-primary rounded-2xl shadow-glow group-hover:shadow-glow-secondary transition-all duration-300 mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                            </svg>
                        </div>
                        <div class="text-3xl font-black text-white mb-1"><?php echo min(10, wp_count_posts()->publish); ?></div>
                        <div class="text-sm font-semibold text-white/90 uppercase tracking-wide"><?php esc_html_e('Latest Posts', 'halacoupon'); ?></div>
                    </div>
                </div>
                <?php endif; ?>

            </div>

            <!-- Enhanced Date Badge with better styling -->
            <div class="flex justify-center">
                <span class="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-black/60 backdrop-blur-xl text-white font-medium text-sm border-2 border-white/20 shadow-soft hover:bg-black/70 hover:border-white/30 transition-all duration-300">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <?php if (is_search()) : ?>
                        <?php esc_html_e('Search Results', 'halacoupon'); ?>
                    <?php else : ?>
                        <?php esc_html_e('Fresh Content Daily', 'halacoupon'); ?>
                    <?php endif; ?>
                </span>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="relative py-12 md:py-20">
        <div class="container mx-auto px-4">
            <div id="primary" class="content-area">
                <div id="main" class="site-main" role="main">
                    <?php
                    global $wp_query;

                    if (!is_singular()) {
                        if (have_posts()) {

                            // Handle the search results for 'store' post type
                            if (is_search() && get_query_var('post_type') === 'store') {
                                get_template_part('search-store');
                            } else {
                                ?>

                                <!-- Enhanced Blog Grid with Brand Colors -->
                                <div class="max-w-6xl mx-auto">

                                    <!-- Results Info -->
                                    <?php if (is_search()) : ?>
                                    <div class="mb-8 p-6 bg-white/60 backdrop-blur-sm rounded-2xl border border-primary/20">
                                        <p class="text-text/80 text-lg">
                                            <?php
                                            global $wp_query;
                                            $results_count = $wp_query->found_posts;
                                            printf(
                                                _n(
                                                    'Found %s result for "%s"',
                                                    'Found %s results for "%s"',
                                                    $results_count,
                                                    'halacoupon'
                                                ),
                                                '<span class="font-bold text-primary">' . number_format($results_count) . '</span>',
                                                '<span class="font-bold text-secondary">' . get_search_query() . '</span>'
                                            );
                                            ?>
                                        </p>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Modern Blog Grid -->
                                    <div class="blog-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
                                        <?php get_template_part('content', 'loop'); ?>
                                    </div>

                                    <!-- Enhanced Pagination -->
                                    <div class="mt-12">
                                        <?php get_template_part('content', 'paging'); ?>
                                    </div>
                                </div>

                                <?php
                            }
                        } else {
                            ?>
                            <!-- Enhanced No Content Message -->
                            <div class="max-w-2xl mx-auto text-center">
                                <div class="relative group">
                                    <div class="absolute -inset-1 bg-gradient-primary rounded-3xl blur opacity-20 group-hover:opacity-30 transition duration-300"></div>
                                    <div class="relative bg-white/80 backdrop-blur-sm rounded-3xl shadow-soft border border-primary/20 p-12">
                                        <div class="w-24 h-24 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full flex items-center justify-center mx-auto mb-6">
                                            <svg class="w-12 h-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                            </svg>
                                        </div>
                                        <h2 class="text-3xl font-bold text-text mb-4">
                                            <?php esc_html_e('No Content Found', 'halacoupon'); ?>
                                        </h2>
                                        <p class="text-text/70 text-lg leading-relaxed mb-6">
                                            <?php esc_html_e('Sorry, we couldn\'t find any content matching your request. Try searching for something else or browse our latest deals.', 'halacoupon'); ?>
                                        </p>
                                        <a href="<?php echo esc_url(home_url('/')); ?>"
                                           class="inline-flex items-center px-6 py-3 bg-gradient-primary text-white font-semibold rounded-2xl shadow-soft hover:shadow-medium transform hover:scale-105 transition-all duration-300">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                                            </svg>
                                            <?php esc_html_e('Back to Home', 'halacoupon'); ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                    } else {
                        // Load the single post content
                        get_template_part('content');
                    }
                    ?>
                </div><!-- #main -->
            </div><!-- #primary -->
        </div>
    </main>
</div>

<?php get_footer(); ?>