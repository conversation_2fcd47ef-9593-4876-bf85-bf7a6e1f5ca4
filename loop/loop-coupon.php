<?php
/**
 * Coupon Card - Matching Provided Layout
 * 70-80px image with badges below, code button with peel effect
 */

$has_thumb = halacoupon_maybe_show_coupon_thumb();
$coupon_type = halacoupon_coupon()->get_type();
$is_exclusive = halacoupon_coupon()->is_exclusive();
$coupon_code = halacoupon_coupon()->get_code();

// Get description
$coupon_description = '';
$coupon_post = get_post(halacoupon_coupon()->ID);
if ($coupon_post) {
    if (!empty($coupon_post->post_excerpt)) {
        $coupon_description = $coupon_post->post_excerpt;
    } elseif (!empty($coupon_post->post_content)) {
        $coupon_description = $coupon_post->post_content;
        $coupon_description = apply_filters('the_content', $coupon_description);
    }
}

$card_id = 'coupon-' . halacoupon_coupon()->ID;
?>

<!-- SEO & Accessibility Optimized Coupon Card -->
<article id="<?php echo $card_id; ?>"
         data-id="<?php echo halacoupon_coupon()->ID; ?>"
         c-type="<?php echo esc_attr($coupon_type); ?>"
         data-store-id="<?php echo halacoupon_store()->term_id; ?>"
         data-exclusive="<?php echo $is_exclusive ? 'true' : 'false'; ?>"
         data-expires="<?php echo halacoupon_coupon()->_ags_expires ? esc_attr(halacoupon_coupon()->get_expires()) : ''; ?>"
         data-code="<?php echo esc_attr($coupon_code); ?>"
         data-aff-url="<?php echo esc_attr(halacoupon_coupon()->get_go_out_url()); ?>"
         data-coupon-title="<?php echo esc_attr(get_the_title(halacoupon_coupon()->ID)); ?>"
         data-coupon-store="<?php echo esc_attr(halacoupon_store()->get_display_name()); ?>"
         data-coupon-description="<?php echo esc_attr(wp_strip_all_tags($coupon_description)); ?>"
         data-coupon-discount="<?php echo esc_attr(halacoupon_coupon()->_ags_coupon_save); ?>"
         data-coupon-used-today="<?php echo esc_attr(halacoupon_coupon()->get_used_today()); ?>"
         data-coupon-expiry="<?php echo esc_attr(halacoupon_coupon()->get_expires()); ?>"
         class="coupon-card relative bg-white rounded-xl shadow-soft hover:shadow-float border <?php echo $is_exclusive ? 'border-primary-300 bg-gradient-to-br from-primary-50/30 to-secondary-50/20' : 'border-gray-100 hover:border-primary-200'; ?> transition-all duration-300 hover:-translate-y-1 <?php echo $has_thumb ? 'has-thumb' : 'no-thumb'; ?> c-type-<?php echo esc_attr($coupon_type); ?> <?php if ($is_exclusive) { echo 'exclusive-coupon'; } ?>"
         itemscope
         itemtype="https://schema.org/Offer"
         role="article"
         aria-labelledby="coupon-title-<?php echo halacoupon_coupon()->ID; ?>"
         aria-describedby="coupon-stats-<?php echo halacoupon_coupon()->ID; ?>">

    <?php if ($is_exclusive) : ?>
    <!-- Exclusive Badge Ribbon -->
    <div class="absolute -top-2 -right-2 z-10">
        <div class="relative">
            <div class="bg-gradient-primary text-white px-3 py-1 rounded-full text-xs shadow-glow transform rotate-12 animate-pulse">
                <span class="flex items-center gap-1">
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    <?php esc_html_e('EXCLUSIVE', 'halacoupon'); ?>
                </span>
            </div>
            <!-- Floating sparkle effects -->
            <div class="absolute -top-1 -left-1 w-2 h-2 bg-secondary rounded-full animate-ping opacity-75"></div>
            <div class="absolute -bottom-1 -right-1 w-1.5 h-1.5 bg-primary rounded-full animate-ping opacity-60" style="animation-delay: 0.5s;"></div>
        </div>
    </div>
    <?php endif; ?>
    <!-- Main Content Section -->
    <div class="grid c-content lg:flex flex-col sm:flex-row sm:items-center gap-4 p-4">

        <!-- Store Logo -->
        <div class="flex-shrink-0 c-logo w-20 h-20"
             role="img"
             aria-label="<?php echo esc_attr(halacoupon_store()->get_display_name()); ?> store logo">
            <div class="w-20 h-20 rounded-xl border border-gray-200 overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 shadow-card hover:shadow-card-hover transition-all duration-300 hover:scale-105 coupon-store-img-wrapper"
                 itemprop="seller"
                 itemscope
                 itemtype="https://schema.org/Organization">
                <meta itemprop="name" content="<?php echo esc_attr(halacoupon_store()->get_display_name()); ?>" class="coupon-store-name">
                <?php halacoupon_thumb($has_thumb === 'save_value' ? true : true); ?>
            </div>
        </div>

        <!-- Coupon Content -->
        <div class="flex-1 c-title min-w-0" role="main">
            <!-- Badges -->
            <div class="flex items-center gap-2 mb-3">
                <?php if ($is_exclusive) : ?>
                <span class="bg-gradient-accent text-white text-xs px-3 py-1.5 rounded-full shadow-medium border border-accent-200 flex items-center gap-1"
                      role="status"
                      aria-label="<?php esc_attr_e('Exclusive VIP offer', 'halacoupon'); ?>"
                      itemprop="category">
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    <?php esc_html_e('VIP', 'halacoupon'); ?>
                </span>
                <?php endif; ?>

                <span class="<?php echo $coupon_type === 'code' ? 'bg-gradient-primary shadow-glow' : 'bg-gradient-secondary shadow-glow-secondary'; ?> text-white text-xs px-3 py-1.5 rounded-full shadow-medium border border-white/20"
                      role="status"
                      aria-label="<?php echo $coupon_type === 'code' ? esc_attr__('Coupon code offer', 'halacoupon') : esc_attr__('Sale offer', 'halacoupon'); ?>"
                      itemprop="category">
                    <?php echo $coupon_type === 'code' ? esc_html__('Code', 'halacoupon') : esc_html__('Sale', 'halacoupon'); ?>
                </span>

                <span class="bg-gradient-to-r from-green-100 to-green-200 text-green-800 text-xs px-3 py-1.5 rounded-full shadow-soft border border-green-200 flex items-center gap-1"
                      role="status"
                      aria-label="<?php esc_attr_e('Verified offer', 'halacoupon'); ?>"
                      itemprop="availability"
                      content="https://schema.org/InStock">
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <?php esc_html_e('Verified', 'halacoupon'); ?>
                </span>
            </div>

            <!-- Title -->
            <h2 id="coupon-title-<?php echo halacoupon_coupon()->ID; ?>"
                class="text-lg regular-text text-gray-900 mb-2 leading-tight coupon-title"
                itemprop="name">
                <a title="<?php echo esc_attr(get_the_title(halacoupon_coupon()->ID)) ?>"
                   <?php if (!halacoupon_is_single_enable()) { ?> rel="nofollow" <?php } ?>
                   class="hover:text-primary transition-colors duration-200 coupon-title-link"
                   data-type="<?php echo halacoupon_coupon()->get_type(); ?>"
                   data-coupon-id="<?php echo halacoupon_coupon()->ID; ?>"
                   data-aff-url="<?php echo esc_attr(halacoupon_coupon()->get_go_out_url()); ?>"
                   data-code="<?php echo esc_attr(halacoupon_coupon()->get_code()); ?>"
                   href="<?php echo esc_attr(halacoupon_coupon()->get_href()); ?>"
                   itemprop="url"
                   aria-describedby="coupon-stats-<?php echo halacoupon_coupon()->ID; ?>">
                   <?php echo get_the_title(halacoupon_coupon()->ID); ?>
                </a>
            </h2>

            <!-- Hidden Schema.org metadata -->
            <meta itemprop="description" content="<?php echo esc_attr(wp_strip_all_tags(get_the_title(halacoupon_coupon()->ID))); ?>">
            <meta itemprop="validFrom" content="<?php echo esc_attr(get_the_date('c', halacoupon_coupon()->ID)); ?>">
            <?php if (halacoupon_coupon()->_ags_expires && !halacoupon_coupon()->has_expired()) : ?>
            <meta itemprop="validThrough" content="<?php echo esc_attr(date('c', strtotime(halacoupon_coupon()->get_expires()))); ?>">
            <?php endif; ?>
            <meta itemprop="category" content="<?php echo esc_attr($coupon_type); ?>">
        </div>

        <!-- Action Button & Vote Section -->
        <div class="w-full c-action flex justify-between items-center sm:block pt-4 sm:pt-0  sm:w-auto sm:flex-shrink-0 text-center"
             role="region"
             aria-label="<?php esc_attr_e('Coupon action', 'halacoupon'); ?>">
            <?php if ($coupon_type === 'sale') : ?>
                <!-- Sale Button -->
                <a rel="nofollow"
                   data-type="<?php echo halacoupon_coupon()->get_type(); ?>"
                   data-coupon-id="<?php echo halacoupon_coupon()->ID; ?>"
                   data-aff-url="<?php echo esc_attr(halacoupon_coupon()->get_go_out_url()); ?>"
                   class="block w-full sm:w-auto bg-gradient-secondary hover:bg-gradient-cta text-white px-6 py-3 rounded-xl font-bold shadow-glow-secondary hover:shadow-float-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-opacity-50 transform hover:scale-105 border border-secondary-200"
                   href="<?php echo esc_attr(halacoupon_coupon()->get_href()); ?>"
                   itemprop="target"
                   role="button"
                   aria-label="<?php esc_attr_e('Get this deal offer', 'halacoupon'); ?>">
                    <span class="flex items-center justify-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                        </svg>
                        <?php esc_html_e('Get Deal', 'halacoupon'); ?>
                    </span>
                </a>
            <?php else : ?>
                <!-- Code Button with Enhanced Peel Effect -->
                <a class="relative mx-auto w-full cursor-pointer group"
                   data-type="<?php echo halacoupon_coupon()->get_type(); ?>"
                   data-coupon-id="<?php echo halacoupon_coupon()->ID; ?>"
                   data-code="<?php echo esc_attr(halacoupon_coupon()->get_code()); ?>"
                   data-aff-url="<?php echo esc_attr(halacoupon_coupon()->get_go_out_url()); ?>"
                   href="<?php echo esc_attr(halacoupon_coupon()->get_href()); ?>"
                   itemprop="potentialAction"
                   itemscope
                   itemtype="https://schema.org/ViewAction"
                   role="button"
                   tabindex="0"
                   aria-label="<?php esc_attr_e('Reveal and copy coupon code', 'halacoupon'); ?>"
                   onkeydown="if(event.key==='Enter'||event.key===' '){this.click()}">

                    <div class="w-full min-w-[204px] sm:w-auto bg-gradient-primary hover:bg-gradient-cta text-white px-6 py-3 rounded-xl font-bold group shadow-glow hover:shadow-float-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transform hover:scale-105 border border-primary-200">
					<div class="font-mono font-bold group <?php echo is_rtl() ? 'text-left relative left-[-14px]' : 'text-right relative right-[-14px]'; ?> text-xs coupon-code">
                            <?php echo esc_html($coupon_code); ?>
                        </div>                        
						<meta itemprop="target" content="<?php echo esc_attr(halacoupon_coupon()->get_go_out_url()); ?>">
                        <span class="OfferPeel absolute top-[-3px] <?php echo is_rtl() ? 'left-[14px] hover:left-[24px] transform transition-all duration-300  rounded-l-lg pl-1' : 'right-[14px] rounded-r-lg pr-1'; ?> h-[45px]  w-[35px] bg-white pt-1 pb-1 text-cta-900"></span>
                        <span class="absolute min-h-full top-0 <?php echo is_rtl() ? 'right-0 w-[calc(100%_-_40px)] hover:w-[calc(100%_-_50px)] transform transition-all duration-300 text-right  rounded-r-xl' : 'left-0 w-[calc(100%_-_40px)] text-left rounded-l-xl'; ?> bg-cta-500 hover:bg-cta-600 text-white text-sm py-2 px-3 sm:px-4 hover:bg-cta-600 focus:outline-none">
                            <span class="flex items-center gap-2 flex-row">
                                <span class="font-bold"><?php esc_html_e('Get Code', 'halacoupon'); ?></span>
                            </span>
                        </span>
                    </div>
                </a>
            <?php endif; ?>

            <!-- Vote Section Under Button -->
            <div class="mt-4 flex items-center justify-end gap-2">
                <!-- Like Button -->
                <?php 
                // Include the likes functionality
                if (function_exists('halacoupon_like_button')) {
                    halacoupon_like_button(halacoupon_coupon()->ID, [
                        'show_label' => false,
                        'show_count' => true,
                        'button_class' => 'text-xs',
                        'icon_class' => 'w-3 h-3'
                    ]);
                }
                ?>
            </div>
        </div>
    </div>
    <!-- Enhanced Footer with Better Layout -->
    <footer id="coupon-stats-<?php echo halacoupon_coupon()->ID; ?>"
            class="border-t border-gray-200 px-4 py-4 bg-gradient-to-r from-primary-50/50 to-accent-50/30 backdrop-blur-sm rounded-b-xl"
            role="contentinfo"
            aria-label="<?php esc_attr_e('Coupon details and actions', 'halacoupon'); ?>">

        <div class="flex flex-row items-center justify-between gap-4 text-xs">

            <!-- Left: Usage Statistics -->
            <div class="flex flex-row items-center justify-start gap-2">
                <!-- Today's Usage -->
                <div class="flex items-center gap-1 bg-white rounded-full px-3 py-2 text-gray-700 shadow-soft border border-gray-100">
                    <svg class="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span class="coupon-used-today"><?php echo halacoupon_coupon()->get_used_today(); ?></span>
                    <span class="text-xs font-medium"><?php esc_html_e('today', 'halacoupon'); ?></span>
                </div>

                <!-- Success Status -->
                <div class="hidden sm:flex lg:flex items-center gap-1 bg-gradient-to-r from-green-100 to-green-200 text-green-800 rounded-full px-3 py-2 shadow-soft border border-green-200">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <span class="text-xs"><?php esc_html_e('Success', 'halacoupon'); ?></span>
                </div>

                <!-- Expiry Information -->
                <div class="flex items-center gap-1 text-gray-700 bg-white rounded-full px-3 py-2 shadow-soft border border-gray-100">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <?php if (halacoupon_coupon()->_ags_expires && !halacoupon_coupon()->has_expired()) : ?>
                        <time datetime="<?php echo esc_attr(date('Y-m-d', strtotime(halacoupon_coupon()->get_expires()))); ?>"
                              itemprop="validThrough"
                              class="text-xs coupon-expiry">
                            <?php echo esc_html(halacoupon_coupon()->get_expires()); ?>
                        </time>
                    <?php else : ?>
                        <span itemprop="validThrough" content="" class="text-xs"><?php esc_html_e('No expiry', 'halacoupon'); ?></span>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Right: Action Buttons -->
            <div class="flex items-center justify-center sm:justify-end gap-2">

                <!-- Share Button -->
                <button class="share-btn flex items-center gap-1 bg-gradient-to-r from-primary-100 to-secondary-100 hover:bg-gradient-primary text-primary-700 hover:text-white px-3 py-2 rounded-full text-xs font-bold shadow-soft hover:shadow-medium transition-all duration-300 transform hover:scale-105 border border-primary-200"
                        type="button"
                        data-coupon-id="<?php echo halacoupon_coupon()->ID; ?>"
                        data-coupon-title="<?php echo esc_attr(get_the_title(halacoupon_coupon()->ID)); ?>"
                        data-store-name="<?php echo esc_attr(halacoupon_store()->get_display_name()); ?>"
                        aria-label="<?php esc_attr_e('Share this coupon', 'halacoupon'); ?>">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                    </svg>
                    <span><?php esc_html_e('Share', 'halacoupon'); ?></span>
                </button>

                <!-- Terms Toggle -->
                <?php if (!empty($coupon_description)) : ?>
                <button class="description-toggle flex items-center gap-1 bg-gradient-to-r from-accent-100 to-primary-100 hover:bg-gradient-accent text-accent-700 hover:text-white px-3 py-2 rounded-full text-xs shadow-soft hover:shadow-medium transition-all duration-300 transform hover:scale-105 border border-accent-200"
                        type="button"
                        data-coupon-id="<?php echo halacoupon_coupon()->ID; ?>"
                        aria-expanded="false"
                        aria-controls="description-<?php echo halacoupon_coupon()->ID; ?>"
                        aria-label="<?php esc_attr_e('Toggle coupon terms and conditions', 'halacoupon'); ?>">
                    <span><?php esc_html_e('Terms', 'halacoupon'); ?></span>
                    <svg xmlns="http://www.w3.org/2000/svg"
                         class="description-icon-down block h-3 w-3 transition-transform duration-300"
                         viewBox="0 0 20 20"
                         fill="currentColor"
                         aria-hidden="true">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg"
                         class="description-icon-up hidden h-3 w-3"
                         fill="none"
                         viewBox="0 0 24 24"
                         stroke="currentColor"
                         aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                    </svg>
                </button>
                <?php endif; ?>
            </div>
        </div>
    </footer>

    <!-- Enhanced Terms & Conditions Section -->
    <?php if (!empty($coupon_description)) : ?>
    <section id="description-<?php echo halacoupon_coupon()->ID; ?>"
             class="description-content hidden bg-gradient-to-r from-accent-50/30 to-secondary-50/20 px-4 py-4 border-t border-accent-200 rounded-b-xl"
             role="region"
             aria-labelledby="terms-heading-<?php echo halacoupon_coupon()->ID; ?>"
             aria-hidden="true">
        <div class="bg-white rounded-xl p-4 border border-accent-100 shadow-soft">
            <h4 id="terms-heading-<?php echo halacoupon_coupon()->ID; ?>"
                class="font-bold mb-3 text-gray-900 text-sm flex items-center gap-2">
                <svg class="w-4 h-4 text-accent" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
                <?php esc_html_e('Terms & Conditions', 'halacoupon'); ?>
            </h4>
            <div class="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none coupon-description" itemprop="description">
                <?php echo wp_kses_post($coupon_description); ?>
            </div>
        </div>
    </section>
    <?php endif; ?>
</article>