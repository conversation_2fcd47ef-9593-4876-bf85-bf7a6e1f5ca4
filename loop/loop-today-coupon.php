<?php
/**
 * or displaying multiple coupons with original peel effect
 *
 * @package HalaCoupon
 * @version 2.1.0 - Minimal Clean Design
 */
// Security check for direct access
if ( ! defined( 'ABSPATH' ) ) exit;
// The following functions are standard WordPress template tags and are available in the WP environment:
// esc_attr, esc_html, esc_html__, esc_html_e, get_the_title, get_the_excerpt, wp_strip_all_tags, is_rtl, etc.
// If flagged as undefined, this is a false positive from static analysis outside of WP.

// Get essential coupon data
$coupon_type = halacoupon_coupon()->get_type();
$is_exclusive = halacoupon_coupon()->is_exclusive();
$coupon_code = halacoupon_coupon()->get_code();
$has_thumb = halacoupon_maybe_show_coupon_thumb();
$card_id = 'coupon-' . halacoupon_coupon()->ID;
$has_expired = halacoupon_coupon()->has_expired();
$discount_value = halacoupon_coupon()->get_discount_value();
?>

<!-- Enhanced Minimal Coupon Card with Consistent Height -->
<article id="<?php echo $card_id; ?>"
         data-id="<?php echo halacoupon_coupon()->ID; ?>"
         c-type="<?php echo esc_attr($coupon_type); ?>"
         data-code="<?php echo esc_attr($coupon_code); ?>"
         data-aff-url="<?php echo esc_attr(halacoupon_coupon()->get_go_out_url()); ?>"
         data-coupon-title="<?php echo esc_attr(get_the_title(halacoupon_coupon()->ID)); ?>"
         data-coupon-store="<?php echo esc_attr(halacoupon_store()->get_display_name()); ?>"
         data-coupon-description="<?php echo esc_attr(wp_strip_all_tags(get_the_excerpt(halacoupon_coupon()->ID))); ?>"
         data-coupon-discount="<?php echo esc_attr(halacoupon_coupon()->get_discount_value()); ?>"
         data-coupon-used-today="<?php echo esc_attr(halacoupon_coupon()->get_used_today()); ?>"
         data-coupon-expiry="<?php echo esc_attr(halacoupon_coupon()->get_expires()); ?>"
         class="coupon-card relative rounded-xl border border-gray-200 hover:border-primary/30 transition-all duration-300 hover:scale-[1.01] group overflow-hidden <?php echo $is_exclusive ? 'shadow-lg hover:shadow-xl border-primary/20' : 'shadow-sm hover:shadow-md'; ?> <?php echo $has_expired ? 'opacity-75 grayscale' : ''; ?> h-full flex flex-col bg-white/60 backdrop-blur-sm"
         itemscope
         itemtype="https://schema.org/Offer"
         dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">

    <!-- Decorative Glass/Blurred Header -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary/10 via-white/30 to-secondary/10 pointer-events-none z-0"></div>

    <!-- Card Content with Flex Layout -->
    <div class="relative z-10 p-4 flex flex-col h-full gap-3 <?php echo is_rtl() ? 'text-right' : 'text-left'; ?>">
        <!-- Top Row: Logo + Like + Discount -->
        <div class="flex items-center justify-between mb-2 gap-2 <?php echo is_rtl() ? 'flex-row-reverse' : ''; ?>">
            <!-- Store Logo -->
            <div class="w-12 h-12 rounded-xl overflow-hidden bg-gray-50 border border-gray-200 flex-shrink-0 group-hover:border-primary/20 transition-colors duration-300 coupon-store-img-wrapper shadow-soft">
                <?php if ($has_thumb) : ?>
                    <?php halacoupon_thumb($has_thumb); ?>
                <?php else : ?>
                    <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                        <svg class="w-5 h-5 text-gray-400 coupon-store-img" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Discount Value Badge -->
            <span class="flex items-center gap-1 px-2 py-1 rounded-lg text-xs font-semibold bg-gradient-to-r from-yellow-100 to-yellow-300 text-yellow-800 border border-yellow-200 shadow-soft">
                <svg class="w-3 h-3 text-yellow-500" fill="currentColor" viewBox="0 0 20 20"><path d="M12.9 2.3a2.25 2.25 0 00-3.8 0L2.3 12.9a2.25 2.25 0 002.6 3.3l10.6-6.8a2.25 2.25 0 00-2.6-3.3z"/></svg>
                <span class="coupon-discount-value"><?php echo esc_html($discount_value); ?></span>
            </span>
        </div>
        <!-- Title -->
        <div class="flex flex-col gap-2 mb-2">
            <h3 class="text-base sm:text-lg font-semibold text-gray-900 leading-tight line-clamp-2 group-hover:text-primary transition-colors duration-300 min-h-[2.5rem] flex items-start coupon-title"
                itemprop="name">
                <a href="<?php echo esc_attr(halacoupon_coupon()->get_href()); ?>"
                   data-type="<?php echo halacoupon_coupon()->get_type(); ?>"
                   data-coupon-id="<?php echo halacoupon_coupon()->ID; ?>"
                   data-aff-url="<?php echo esc_attr(halacoupon_coupon()->get_go_out_url()); ?>"
                   data-code="<?php echo esc_attr(halacoupon_coupon()->get_code()); ?>"
                   class="hover:text-primary transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-1 rounded-md coupon-title-link"
                   title="<?php echo esc_attr(get_the_title(halacoupon_coupon()->ID)); ?>"
                   aria-label="<?php echo esc_attr(sprintf(__('View %s coupon details', 'halacoupon'), get_the_title(halacoupon_coupon()->ID))); ?>">
                    <?php echo get_the_title(halacoupon_coupon()->ID); ?>
                </a>
            </h3>
        </div>
        <div class="mb-2">
            <?php if ($coupon_type === 'sale') : ?>
               <!-- Sale Button with Theme Gradients -->
                <a rel="nofollow"
                   data-type="<?php echo halacoupon_coupon()->get_type(); ?>"
                   data-coupon-id="<?php echo halacoupon_coupon()->ID; ?>"
                   data-aff-url="<?php echo esc_attr(halacoupon_coupon()->get_go_out_url()); ?>"
                   class="block w-full bg-gradient-secondary hover:bg-gradient-cta text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-bold shadow-glow-secondary hover:shadow-float-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-opacity-50 transform hover:scale-[1.02] border border-secondary-200"
                   href="<?php echo esc_attr(halacoupon_coupon()->get_href()); ?>"
                   itemprop="target"
                   role="button"
                   aria-label="<?php esc_attr_e('Get this amazing deal offer', 'halacoupon'); ?>">
                    <span class="flex items-center justify-center gap-2">
                        <span class="text-xs"><?php esc_html_e('Get Deal', 'halacoupon'); ?></span>
                        <svg class="w-3 h-3 sm:w-4 sm:h-4 group-hover:translate-x-1 transition-transform duration-300 <?php echo is_rtl() ? 'rotate-180 group-hover:-translate-x-1' : ''; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                        </svg>
                    </span>
                </a>
            <?php else : ?>
                <a class="relative cursor-pointer group block w-full"
                   data-type="<?php echo halacoupon_coupon()->get_type(); ?>"
                   data-coupon-id="<?php echo halacoupon_coupon()->ID; ?>"
                   data-code="<?php echo esc_attr(halacoupon_coupon()->get_code()); ?>"
                   data-aff-url="<?php echo esc_attr(halacoupon_coupon()->get_go_out_url()); ?>"
                   href="<?php echo esc_attr(halacoupon_coupon()->get_href()); ?>"
                   itemprop="potentialAction"
                   itemscope
                   itemtype="https://schema.org/ViewAction"
                   role="button"
                   tabindex="0"
                   aria-label="<?php esc_attr_e('Reveal and copy coupon code', 'halacoupon'); ?>"
                   onkeydown="if(event.key==='Enter'||event.key===' '){this.click()}">

                    <div class="relative w-full min-w-[160px] sm:min-w-[180px] bg-gradient-primary hover:bg-gradient-cta text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-bold shadow-glow hover:shadow-float-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transform hover:scale-[1.02] border border-primary-200">
                        <!-- Code Display with RTL/LTR Support -->
                        <div class="font-mono font-bold <?php echo is_rtl() ? 'text-left relative left-[-14px]' : 'text-right relative right-[-14px]'; ?> text-xs coupon-code">
                            <?php echo esc_html($coupon_code); ?>
                        </div>

                        <meta itemprop="target" content="<?php echo esc_attr(halacoupon_coupon()->get_go_out_url()); ?>">

                        <!-- Peel Effect with RTL/LTR Support -->
                        <span class="OfferPeel absolute top-[-3px] <?php echo is_rtl() ? 'left-[14px]  rounded-l-lg pl-1' : 'right-[14px] rounded-r-lg pr-1'; ?> h-[45px]  w-[35px] bg-white pt-1 pb-1 text-cta-900"></span>

                        <!-- Get Code Section with RTL/LTR Support -->
                        <span class="absolute min-h-full top-0 <?php echo is_rtl() ? 'right-0 w-[calc(100%_-_40px)] text-right  rounded-r-xl' : 'left-0 w-[calc(100%_-_40px)] text-left rounded-l-xl'; ?> bg-cta-500 hover:bg-cta-600 text-white text-sm py-2 px-3 sm:px-4 hover:bg-cta-600 focus:outline-none">
                            <span class="flex items-center gap-2 <?php echo is_rtl() ? 'flex-row-reverse' : ''; ?>">
                                <span class="text-xs font-semibold"><?php esc_html_e('Get Code', 'halacoupon'); ?></span>
                            </span>
                        </span>
                    </div>
                </a>
            <?php endif; ?>
        </div>
        <!-- Footer: Used Today, VIP, Type Badge -->
        <div class="mt-auto pt-2 border-t border-gray-100 group-hover:border-primary/20 transition-colors duration-300 bg-white/40 backdrop-blur-sm rounded-b-xl">
            <div class="flex items-center justify-between gap-2 w-full">
                <!-- Used Today Stat (clock SVG + number only) -->
                <?php $used_today = halacoupon_coupon()->get_used_today(); ?>
                <div class="flex-1 min-w-0 text-xs flex items-center justify-center h-7 rounded-md bg-gradient-to-r from-green-100 to-green-200 border border-green-200 text-green-800 gap-1 px-2 py-0.5 shadow-soft">
                    <svg class="w-4 h-4 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" stroke-width="2"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6l4 2"/>
                    </svg>
                    <span class="coupon-used-today truncate font-bold text-green-700"><?php echo ($used_today && intval($used_today) > 0) ? $used_today : '0'; ?></span>
                </div>
                <!-- VIP/Exclusive Badge (crown SVG only) -->
                <div class="flex-1 text-xs min-w-0 flex items-center justify-center h-7 rounded-md bg-gradient-to-r from-purple-100 to-purple-300 border border-purple-200 text-purple-800 px-2 py-0.5 shadow-soft <?php echo $is_exclusive ? '' : 'opacity-0 pointer-events-none'; ?>">
                    <svg class="w-4 h-4 flex-shrink-0 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 3l2.39 4.84 5.36.78-3.88 3.78.92 5.36L10 14.77l-4.79 2.52.92-5.36-3.88-3.78 5.36-.78z"/>
                    </svg>
                </div>
                <!-- Type Badge (tag SVG for code, percent SVG for sale) -->
                <div class="flex-1 text-xs min-w-0 flex items-center justify-center h-7 rounded-md border shadow-soft
                    <?php echo $coupon_type === 'code' ? 'bg-gradient-to-r from-blue-100 to-blue-300 border-blue-200' : 'bg-gradient-to-r from-orange-100 to-orange-300 border-orange-200'; ?>">
                    <?php if ($coupon_type === 'code') : ?>
                        <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7l10 10M7 17L17 7"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                    <?php else : ?>
                        <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" stroke-width="2"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 15l6-6M9 9h.01M15 15h.01"/>
                        </svg>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <!-- Hidden Schema.org metadata -->
        <meta itemprop="description" content="<?php echo esc_attr(wp_strip_all_tags(get_the_title(halacoupon_coupon()->ID))); ?>">
        <meta itemprop="validFrom" content="<?php echo esc_attr(get_the_date('c', halacoupon_coupon()->ID)); ?>">
        <?php if (halacoupon_coupon()->_ags_expires && !halacoupon_coupon()->has_expired()) : ?>
        <time datetime="<?php echo esc_attr(date('Y-m-d', strtotime(halacoupon_coupon()->get_expires()))); ?>" itemprop="validThrough" class="font-bold text-xs coupon-expiry">
            <?php echo esc_html(halacoupon_coupon()->get_expires()); ?>
        </time>
        <?php endif; ?>
        <meta itemprop="category" content="<?php echo esc_attr($coupon_type); ?>">
    </div>
</article>