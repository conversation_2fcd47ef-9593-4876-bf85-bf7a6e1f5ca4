<?php
/**
 * Coupon Modal Template
 * 
 * This template is loaded via AJAX when a coupon modal is opened.
 * It displays detailed coupon information with like button functionality.
 *
 * @package HalaCoupon
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Ensure we have a coupon object
if (!halacoupon_coupon()) {
    return;
}

$coupon = halacoupon_coupon();
$coupon_type = $coupon->get_type();
$is_exclusive = $coupon->is_exclusive();
$coupon_code = $coupon->get_code();
$store = halacoupon_store();

// Get coupon terms instead of description
$coupon_terms = $coupon->get_coupon_terms();
$coupon_description = !empty($coupon_terms) ? $coupon_terms : '';

// Fallback to excerpt/content if no terms are set
if (empty($coupon_description)) {
    $coupon_post = get_post($coupon->ID);
    if ($coupon_post) {
        if (!empty($coupon_post->post_excerpt)) {
            $coupon_description = $coupon_post->post_excerpt;
        } elseif (!empty($coupon_post->post_content)) {
            $coupon_description = $coupon_post->post_content;
            $coupon_description = apply_filters('the_content', $coupon_description);
        }
    }
}
?>

<div class="coupon-modal-content p-6">
    <!-- Store Header -->
    <div class="flex items-center gap-4 mb-6 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200">
        <div class="flex-shrink-0">
            <div class="w-16 h-16 rounded-lg border border-gray-200 overflow-hidden bg-white shadow-soft">
                <?php halacoupon_thumb(true); ?>
            </div>
        </div>
        <div class="flex-1 min-w-0">
            <h3 class="text-lg font-bold text-gray-900 mb-1">
                <?php echo esc_html($store->get_display_name()); ?>
            </h3>
            <p class="text-sm text-gray-600">
                <?php esc_html_e('Verified Store', 'halacoupon'); ?>
            </p>
        </div>
        <div class="flex-shrink-0">
            <a href="<?php echo esc_url($store->get_site_url()); ?>" 
               target="_blank" 
               rel="nofollow"
               class="inline-flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg text-sm font-medium hover:bg-primary-600 transition-colors duration-200">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                </svg>
                <?php esc_html_e('Visit Store', 'halacoupon'); ?>
            </a>
        </div>
    </div>

    <!-- Coupon Details -->
    <div class="space-y-6">
        <!-- Title and Badges -->
        <div class="flex items-start justify-between gap-4">
            <div class="flex-1">
                <h2 class="text-xl font-bold text-gray-900 mb-3">
                    <?php echo esc_html(get_the_title($coupon->ID)); ?>
                </h2>
                
                <!-- Badges -->
                <div class="flex items-center gap-2 mb-4">
                    <?php if ($is_exclusive) : ?>
                    <span class="bg-gradient-primary text-white text-xs px-3 py-1.5 rounded-full font-bold shadow-medium border border-primary-200 flex items-center gap-1">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                        <?php esc_html_e('EXCLUSIVE', 'halacoupon'); ?>
                    </span>
                    <?php endif; ?>

                    <span class="<?php echo $coupon_type === 'code' ? 'bg-gradient-primary' : 'bg-gradient-secondary'; ?> text-white text-xs px-3 py-1.5 rounded-full font-bold shadow-medium border border-white/20">
                        <?php echo $coupon_type === 'code' ? esc_html__('Code', 'halacoupon') : esc_html__('Sale', 'halacoupon'); ?>
                    </span>

                    <span class="bg-gradient-to-r from-green-100 to-green-200 text-green-800 text-xs px-3 py-1.5 rounded-full font-bold shadow-soft border border-green-200 flex items-center gap-1">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <?php esc_html_e('Verified', 'halacoupon'); ?>
                    </span>
                </div>
            </div>

            <!-- Like Button -->
            <div class="flex-shrink-0">
                <?php 
                if (function_exists('halacoupon_like_button')) {
                    halacoupon_like_button($coupon->ID, [
                        'show_label' => true,
                        'show_count' => true,
                        'button_class' => 'text-sm',
                        'icon_class' => 'w-4 h-4'
                    ]);
                }
                ?>
            </div>
        </div>

        <!-- Coupon Code/Deal Section -->
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
            <?php if ($coupon_type === 'code') : ?>
                <!-- Code Display -->
                <div class="text-center">
                    <p class="text-sm text-gray-600 mb-3"><?php esc_html_e('Copy this code and use it at checkout:', 'halacoupon'); ?></p>
                    <div class="bg-white rounded-lg p-4 border-2 border-dashed border-primary-300 mb-4">
                        <div class="font-mono text-2xl font-bold text-primary-700 tracking-wider">
                            <?php echo esc_html($coupon_code); ?>
                        </div>
                    </div>
                    <button class="copy-code-btn bg-gradient-primary hover:bg-gradient-cta text-white px-8 py-3 rounded-lg font-bold shadow-glow hover:shadow-float-lg transition-all duration-300 transform hover:scale-105 border border-primary-200"
                            data-code="<?php echo esc_attr($coupon_code); ?>"
                            type="button">
                        <span class="flex items-center gap-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                            </svg>
                            <?php esc_html_e('Copy Code', 'halacoupon'); ?>
                        </span>
                    </button>
                </div>
            <?php else : ?>
                <!-- Sale/Deal Display -->
                <div class="text-center">
                    <p class="text-sm text-gray-600 mb-3"><?php esc_html_e('Click below to get this amazing deal:', 'halacoupon'); ?></p>
                    <a href="<?php echo esc_url($coupon->get_href()); ?>" 
                       target="_blank" 
                       rel="nofollow"
                       class="inline-flex items-center gap-2 bg-gradient-secondary hover:bg-gradient-cta text-white px-8 py-3 rounded-lg font-bold shadow-glow-secondary hover:shadow-float-lg transition-all duration-300 transform hover:scale-105 border border-secondary-200">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                        </svg>
                        <?php esc_html_e('Get Deal', 'halacoupon'); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Description -->
        <?php if (!empty($coupon_description)) : ?>
        <div class="bg-white rounded-xl p-6 border border-gray-200">
            <h4 class="text-lg font-semibold text-gray-900 mb-3"><?php esc_html_e('About this offer', 'halacoupon'); ?></h4>
            <div class="prose prose-sm max-w-none text-gray-700">
                <?php echo wp_kses_post($coupon_description); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Coupon Stats -->
        <div class="grid grid-cols-2 gap-4">
            <div class="bg-white rounded-xl p-4 border border-gray-200 text-center">
                <div class="text-2xl font-bold text-primary-600 mb-1">
                    <?php echo esc_html($coupon->get_total_used()); ?>
                </div>
                <div class="text-sm text-gray-600"><?php esc_html_e('Times Used', 'halacoupon'); ?></div>
            </div>
            <div class="bg-white rounded-xl p-4 border border-gray-200 text-center">
                <div class="text-2xl font-bold text-green-600 mb-1">
                    <?php echo esc_html(round($coupon->percent_success())); ?>%
                </div>
                <div class="text-sm text-gray-600"><?php esc_html_e('Success Rate', 'halacoupon'); ?></div>
            </div>
        </div>

        <!-- Vote Statistics -->
        <?php if (function_exists('halacoupon_vote_stats')): ?>
        <div class="bg-white rounded-xl p-4 border border-gray-200">
            <h4 class="text-lg font-semibold text-gray-900 mb-3"><?php esc_html_e('Community Feedback', 'halacoupon'); ?></h4>
            <?php halacoupon_vote_stats($coupon->ID, [
                'show_success_rate' => true,
                'show_total_votes' => true,
                'container_class' => 'flex items-center justify-between',
                'stat_class' => 'flex items-center gap-2'
            ]); ?>
        </div>
        <?php endif; ?>

        <!-- Expiration Info -->
        <?php if ($coupon->_ags_expires && !$coupon->has_expired()) : ?>
        <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
            <div class="flex items-center gap-2">
                <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
                <div>
                    <p class="text-sm font-medium text-yellow-800">
                        <?php esc_html_e('Expires on:', 'halacoupon'); ?> 
                        <span class="font-bold"><?php echo esc_html($coupon->get_expires()); ?></span>
                    </p>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div> 