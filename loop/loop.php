<?php
// Get the post author's data
$author_id = get_the_author_meta('ID');
$authordata = get_userdata($author_id);

// Get the selected blog items from theme options
$selected_blog_items = array(
    'author_name'    => true, // Sample default values
    'published_date' => true,
    'comment_number' => true,
);


?>

<article <?php post_class('group relative glass-card rounded-2xl border border-white/30 hover:border-primary/40 transition-all duration-500 hover:shadow-card-hover hover:scale-105 overflow-hidden animate-fade-in-up blog-card'); ?>
         itemscope itemtype="https://schema.org/BlogPosting"
         style="animation-delay: <?php echo ($GLOBALS['wp_query']->current_post * 0.1); ?>s;"
         dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">

    <!-- Featured Image -->
    <?php if (has_post_thumbnail()) : ?>
    <div class="relative aspect-video overflow-hidden">
        <?php
        the_post_thumbnail('medium_large', [
            'class' => 'w-full h-full object-cover group-hover:scale-110 transition-transform duration-700',
            'loading' => 'lazy',
            'itemprop' => 'image'
        ]);
        ?>

        <!-- Image Overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <!-- Reading Time Badge -->
        <div class="absolute top-3 <?php echo is_rtl() ? 'left-3' : 'right-3'; ?>">
            <span class="inline-flex items-center gap-1 px-2 py-1 bg-black/50 backdrop-blur-sm text-white text-xs rounded-full">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                </svg>
                <?php echo halacoupon_get_reading_time(); ?> <?php esc_html_e('min', 'halacoupon'); ?>
            </span>
        </div>

        <!-- Category Badge -->
        <?php
        $categories = get_the_category();
        if ($categories) :
        ?>
        <div class="absolute top-3 <?php echo is_rtl() ? 'right-3' : 'left-3'; ?>">
            <a href="<?php echo esc_url(get_category_link($categories[0])); ?>"
               class="inline-flex items-center gap-1 px-3 py-1 bg-gradient-primary text-white text-xs font-semibold rounded-full hover:scale-105 transition-transform duration-200">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path>
                </svg>
                <?php echo esc_html($categories[0]->name); ?>
            </a>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Card Content -->
    <div class="p-6 space-y-4">

        <!-- Meta Information -->
        <div class="flex items-center gap-3 text-sm text-text/70">
            <!-- Date -->
            <span class="flex items-center gap-1">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                </svg>
                <time datetime="<?php echo esc_attr(get_the_date('c')); ?>" itemprop="datePublished">
                    <?php echo esc_html(get_the_date()); ?>
                </time>
            </span>

            <!-- Author -->
            <span class="text-text/50">•</span>
            <span class="flex items-center gap-1" itemprop="author" itemscope itemtype="https://schema.org/Person">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                </svg>
                <span itemprop="name"><?php echo esc_html(get_the_author()); ?></span>
            </span>

            <!-- Comments -->
            <?php if (get_comments_number() > 0) : ?>
            <span class="text-text/50">•</span>
            <span class="flex items-center gap-1">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                </svg>
                <span><?php echo get_comments_number(); ?></span>
            </span>
            <?php endif; ?>
        </div>

        <!-- Title -->
        <h3 class="text-xl font-bold text-text group-hover:text-primary transition-colors duration-300 leading-tight" itemprop="headline">
            <a href="<?php the_permalink(); ?>" class="hover:underline">
                <?php the_title(); ?>
            </a>
        </h3>

        <!-- Excerpt -->
        <div class="entry-content text-text/80 line-clamp-3" itemprop="description">
            <?php
            if (has_excerpt()) {
                the_excerpt();
            } else {
                echo wp_trim_words(get_the_content(), 20, '...');
            }
            ?>
        </div>

        <!-- Footer -->
        <div class="flex items-center justify-between pt-4 border-t border-gray-100">

            <!-- Tags (first 2) -->
            <?php
            $tags = get_the_tags();
            if ($tags && count($tags) > 0) :
                $display_tags = array_slice($tags, 0, 2);
            ?>
            <div class="flex items-center gap-2">
                <?php foreach ($display_tags as $tag) : ?>
                <a href="<?php echo esc_url(get_tag_link($tag->term_id)); ?>"
                   class="inline-block px-2 py-1 bg-primary/10 text-primary text-xs rounded-full hover:bg-primary/20 transition-colors duration-200">
                    #<?php echo esc_html($tag->name); ?>
                </a>
                <?php endforeach; ?>
                <?php if (count($tags) > 2) : ?>
                <span class="text-xs text-text/60">+<?php echo count($tags) - 2; ?></span>
                <?php endif; ?>
            </div>
            <?php else : ?>
            <div></div>
            <?php endif; ?>

            <!-- Read More Button -->
            <a href="<?php the_permalink(); ?>"
               class="inline-flex items-center gap-2 text-primary font-semibold hover:text-primary-600 transition-colors duration-200 group/cta">
                <?php esc_html_e('Read More', 'halacoupon'); ?>
                <svg class="w-4 h-4 <?php echo is_rtl() ? 'group-hover/cta:-translate-x-1' : 'group-hover/cta:translate-x-1'; ?> transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo is_rtl() ? 'M7 16l-4-4m0 0l4-4m-4 4h18' : 'M17 8l4 4m0 0l-4 4m4-4H3'; ?>"></path>
                </svg>
            </a>
        </div>

    </div>

</article>

<style>
/* Enhanced animations and effects */
.blog-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
}

.blog-card:hover {
    backdrop-filter: blur(15px);
    background: rgba(255, 255, 255, 0.9);
}

/* Line clamp utility */
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Smooth loading animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}
</style>